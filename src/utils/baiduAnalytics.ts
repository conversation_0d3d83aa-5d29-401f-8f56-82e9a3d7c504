export const initBaiduAnalytics = () => {
  const script = document.createElement('script');
  script.innerHTML = `
    var _hmt = _hmt || [];
    (function() {
      var hm = document.createElement("script");
      hm.src = "https://hm.baidu.com/hm.js?e31900a158b57903f113172b7c3f95f0";
      var s = document.getElementsByTagName("script")[0]; 
      s.parentNode.insertBefore(hm, s);
    })();
  `;
  document.head.appendChild(script);
};

// 用于统计外链点击
export const trackOutboundLink = (url: string, name: string) => {
  if (window._hmt) {
    window._hmt.push(['_trackEvent', '抖音获客', 'click', name]);
  }
  window.open(url, '_blank');
};
