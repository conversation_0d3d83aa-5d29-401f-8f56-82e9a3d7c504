import axios from 'axios'
import { message } from 'antd';
import https  from 'https';
import {noConsole,baseUrl }from '../configs/config';
import moment from 'moment';


export default async (options = { method: 'GET', data: { } }) => {
  if (!noConsole) {
    console.log(
      `${new Date().toLocaleString()}【 M=${options.url} 】P=${JSON.stringify(
        options.data
      )}`
    );
  }
  const header = {}
  const Authorization=window.sessionStorage.getItem("Authorization");

  let exp=window.sessionStorage.getItem("exp");

  if(exp&&exp!=0){
    const time=new Date().getTime();

    if(time/1000>parseInt(exp)){
      message.error('發發助手登陆已过期，请重新登陆!');
      throw new Error('發發助手登陆已过期，请重新登陆!');
    }else{
      if(Authorization){
        header['Authorization'] ='Bearer '+ window.sessionStorage.getItem("Authorization");
      }
    }
  }

  const method=options.method.toUpperCase();
  const responseType=options.responseType;

  header['content-type'] = 'application/json'
  let responseData={};
  let obj= options.data;

  if(obj!=undefined){
    Object.keys(options.data).map(key => {
      if(obj[key]!=null&&obj[key]!=undefined){
        responseData[key]=obj[key];
      }
    });
  }



  const agent = new https.Agent({
    rejectUnauthorized: false
  });
  return axios({
    url:options.url,
    data: {
      ...responseData,
    },
    httpsAgent:agent,
    responseType,
    headers:{...header},
    method,
  }).then(async (res) => {

    const { status, data,header} = res;

    if (status >= 200 && status < 300) {
      if (!noConsole) {
        console.log(
          `${new Date().toLocaleString()}【 M=${options.url} 】【接口响应：】`,
          res.data
        );
      }

      return data;
    } else if(status==401){

      throw new Error(`發發助手登陆已过期，请重新登陆`);
    }else{
      throw new Error(`${data.msg}`);
    }
  }).catch(function(error) {
    // 处理 getJSON 和 前一个回调函数运行时发生的错误
    throw new Error(`${error.response.data.msg}`);
  });
};
