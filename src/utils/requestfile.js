import axios from 'axios'
import { message } from 'antd';
import https from 'https';
import { baseUrl } from '../configs/config';

// eslint-disable-next-line consistent-return
export default async (options = { method: 'GET', data: [] }) => {
  try {
    const headers = {};
    const Authorization = window.sessionStorage.getItem('Authorization');

    const exp = window.sessionStorage.getItem('exp');

    if (exp) {
      const time = new Date().getTime();

      if (time / 1000 > parseInt(exp, 10)) {
        message.error('發發助手登陆已过期，请重新登陆!');
        throw new Error('發發助手登陆已过期，请重新登陆!');
      } else if (Authorization) {
          headers.Authorization = `Bearer ${window.sessionStorage.getItem('Authorization')}`;
        }
    }


    const fd = new FormData();
    fd.append('file', options.data.originFileObj, options.data.name)


    const agent = new https.Agent({
      rejectUnauthorized: false,
    });
    // headers['content-type'] = 'multipart/form-data;'
    headers['content-type'] = undefined;
    console.log('do request file');
    return axios.post(baseUrl + options.url, fd, {
      headers,
      withCredentials: false,
      httpsAgent: agent,
    }).then(async (res) => {
      const { status, data } = res;

      if (status >= 200 && status < 300) {
        return data;
      }
      if (status === 401) {
        throw new Error('發發助手登陆已过期');
      } else {
        throw new Error(`${data.msg}`);
      }
    }).catch((error) => {
      // 处理 getJSON 和 前一个回调函数运行时发生的错误
      throw new Error(`${error.response.data.msg}`);
    });
  } catch (e) {
    console.log(e.message)
  }
};
