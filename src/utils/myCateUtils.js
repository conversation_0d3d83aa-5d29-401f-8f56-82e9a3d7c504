export var getCate = (cate) => {

  let newCate = []
  let levelCateJson = cate[1] instanceof Array ? cate[1][0] : cate[1];
  console.log(levelCateJson)

  for (var key in levelCateJson) {

    let cateObj = {
      "value": key,
      "label": levelCateJson[key].indexOf(":") == -1 ? levelCateJson[key] : levelCateJson[key].substring(0, levelCateJson[key].indexOf(":")),
      "isLeaf": true,
      "children": new getCateChileById(key, 2, cate)
    }
    newCate.push(cateObj)
  }


  return newCate;
}

/**
 * 加密
 */
var getCateChileById = (index, level, cate) => {

  let newCate = []
  let firstCateJson = cate[level][index];

  for (var key in firstCateJson) {
    let cateObj = {
      "value": key,
      "label": firstCateJson[key].indexOf(":") == -1 ? firstCateJson[key] : firstCateJson[key].substring(0, firstCateJson[key].indexOf(":")),
      "isLeaf": level == 4 ? true : false,
      "children": level < 4 ? new getCateChileById(key, level + 1, cate) : []
    }
    newCate.push(cateObj)

  }

  return newCate;
}


