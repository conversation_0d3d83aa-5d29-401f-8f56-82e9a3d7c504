/**
 * 组合数组元素
 * @param {Array} arr - 输入的数组
 * @param {string} joinWith - 连接元素的字符串
 * @returns {Array} - 组合后的结果数组
 */
export var combineArray = (arr, joinWith) => {
  // 如果输入数组为空，则返回空数组
  if (!arr) {
    return [];
  }
  // 如果输入数组只有一个元素，则直接返回该元素
  if (arr.length === 1) {
    return arr[0];
  }
  // 取出第一个元素
  const first = arr[0];
  // 递归调用 combineArray 函数，对剩余的数组元素进行组合
  const left = combineArray(arr.slice(1), joinWith);
  // 存储组合后的结果数组
  const result = [];
  // 遍历第一个元素的每个属性
  for (const i in first) {
    // 遍历剩余数组元素的每个属性
    for (const j in left) {
      // 将第一个元素的属性值与剩余数组元素的属性值连接起来，并添加到结果数组中
      result.push(first[i] + joinWith + left[j]);
    }
  }
  // 返回组合后的结果数组
  return result;
};

// 连接两个数组,  返回个数是两个数组长度的最大值
export var connectArray = (arr1, arr2) => {
  const result = [];
  let i = 0;
  let j = 0;
  const size = Math.max(arr1.length, arr2.length);

  // 遍历数组并将对应位置的元素相加
  for (let k = 0; k < size; k++) {
    result.push(arr1[i] + arr2[j]);

    // 循环使用数组中的元素，以防数组长度不一致
    i = (i + 1) % arr1.length;
    j = (j + 1) % arr2.length;
  }

  return result;
};
