const getCityChileById = (index, level, cate) => {
  const newCate = []
  const firstCateJson = cate[level][index];

  for (const key in firstCateJson) {
    let cateObj;
    if (level < 3 && cate[level + 1][key] != undefined) {
      cateObj = {
        value: key,
        label: firstCateJson[key].indexOf(':') == -1 ? firstCateJson[key] : firstCateJson[key].substring(0, firstCateJson[key].indexOf(':')),
        isLeaf: !!(level >= 3 || cate[level + 1][key] == undefined),
        children: getCityChileById(key, level + 1, cate),
      }
    } else {
      cateObj = {
        value: key,
        label: firstCateJson[key].indexOf(':') == -1 ? firstCateJson[key] : firstCateJson[key].substring(0, firstCateJson[key].indexOf(':')),
        isLeaf: !!(level >= 3 || cate[level + 1][key] == undefined),

      }
    }


    newCate.push(cateObj)
  }

  return newCate;
}

export const formcatCity = cate => {
  const newCate = []

  const levelCateJson = cate[1][0] instanceof Array ? cate[1][0] : cate[1]['0'];


  for (const key in levelCateJson) {
    const cateObj = {
      value: key,
      label: levelCateJson[key].indexOf(':') == -1 ? levelCateJson[key] : levelCateJson[key].substring(0, levelCateJson[key].indexOf(':')),
      isLeaf: true,
      children: getCityChileById(key, 2, cate),
    }
    newCate.push(cateObj)
  }


  return newCate;
}


