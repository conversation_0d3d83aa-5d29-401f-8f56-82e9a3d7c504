import axios from 'axios';
import { message } from 'antd';
import https from 'https';
import { noConsole, baseUrl } from '../configs/config';

// const name = packageJson.name;

const fetch = axios.create({
  baseURL: baseUrl,
  timeout: 60000,
  withCredentials: false,
});

fetch.interceptors.request.use(
  config => {
    const header = { version: '0.3.0' };
    const Authorization = window.sessionStorage.getItem('Authorization');
    const exp = window.sessionStorage.getItem('exp');

    if (exp && exp !== '0') {
      const time = new Date().getTime();

      if (time / 1000 > parseInt(exp, 10)) {
        // 清理本地存储的 session 数据
        window.sessionStorage.removeItem('Authorization');
        window.sessionStorage.removeItem('exp');
        message.error('登录已过期，请重新登录！');
        // 判断当前页面是否为登录页面，如果不是则跳转
        const currentPath = window.location.pathname;
        if (!currentPath.includes('/user/login')) {
          window.location.href = '/user/login';
        }
        throw new Error('session失效!');
      } else if (Authorization) {
        header.Authorization = `Bearer ${window.sessionStorage.getItem('Authorization')}`;
      }
    }
    const agent = new https.Agent({
      rejectUnauthorized: false,
    });
    // eslint-disable-next-line no-param-reassign
    config.httpAgent = agent;
    // eslint-disable-next-line no-param-reassign
    config.headers = header;
    return config;
  },
  err => {
    return Promise.reject(err);
  },
);

// 添加一个响应拦截器
fetch.interceptors.response.use(
  res => {
    const { status, data } = res;
    if (status >= 200 && status < 300) {
      if (!noConsole) {
        console.log(
          `${new Date().toLocaleString()}【 M=${res.config.url} 】【接口响应：】`,
          res.data,
        );
      }
      if (data.code && data.msg) {
        return Promise.reject(data);
      }

      return Promise.resolve(data);
    }

    if (status === 401) {
      return Promise.reject({ code: 401, msg: 'session失效' });
    } else {
      return Promise.reject(data);
    }
  },
  err => {
    if (typeof err.response === 'undefined') {
      return Promise.reject({ code: 'timeout', msg: '网络错误' });
    }
    if (err.response.status === 401) {
      window.location.href = '/user/login';
      return Promise.reject(err.response.data);
    }
    if (err.response.data) {
      // eslint-disable-next-line no-param-reassign
      return Promise.reject(err.response.data);
    }
  },
);

export default fetch;
