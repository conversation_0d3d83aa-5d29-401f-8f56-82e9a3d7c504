/* eslint-disable compat/compat */
import { routerRedux } from 'dva/router';
import * as productApi from '../services/product';
import { Modal } from 'antd';
import moment from 'moment';
import CustomError from '@/utils/error';

export default {
  namespace: 'product',

  state: {
    companyDetail: {},
    InfoList: [],
    productState: {},
    pagination: { currentPage: 1, pageSize: 10, total: 10 },
  },

  subscriptions: {
    setup({ dispatch }) {},
  },

  effects: {
    *optionProductById({ payload }, { call, put, select }) {
      try {
        const result = yield call(productApi.optionProductById, payload);
        const { data, msg } = result;
        const productState = yield call(productApi.getProductState);
        const productSize = productState.data[0];
        yield put({
          type: 'home/save',
          payload: {
            productSize,
          },
        });
        yield put({
          type: 'product/getProductStat',
          payload: {},
        });
        yield put({
          type: 'product/getProductList',
          payload: {},
        });
        return msg;
      } catch (error) {
        return Promise.reject(error);
      }
    },
    *getProductById({ payload }, { call, put, select }) {
      const { product_id } = payload;

      try {
        const response = yield call(productApi.getProductbyId, { product_id });
        const { data, code, msg } = response;
        if (code !== 0) {
          return Promise.reject(new CustomError(code, msg));
        }
        return data;
      } catch (error) {
        if (error.code && error.msg) {
          return Promise.reject(new CustomError(error.code, error.msg));
        }
        return Promise.reject(error);
      }
    },

    *updateProductById({ payload, callBack }, { call, put }) {
      try {
        const response = yield call(productApi.updateProduct, { ...payload });
        const { data, code, msg } = response;
        if (code !== 0) {
          return Promise.reject(new CustomError(code, msg));
        }
        return data;
      } catch (error) {
        if (error.code && error.msg) {
          return Promise.reject(new CustomError(error.code, error.msg));
        }
        return Promise.reject(error);
      }
    },
    *updateProductShopById({ payload, callBack }, { call, put, select }) {
      try {
        console.log(payload);
        const { data, msg } = yield call(productApi.updateProductShop, { ...payload });
        console.log(msg);
        if (callBack && typeof callBack === 'function') {
          callBack({ msg, data }); // 返回结果
        }
      } catch (error) {
        Modal.error({ title: '提示', content: error.msg || error.message, zIndex: 9999 });
      }
    },

    *similar({ payload, callBack }, { call, put, select }) {
      try {
        const { code, msg } = yield call(productApi.ProductSimilar, { ...payload });
        if (callBack && typeof callBack === 'function') {
          callBack({ msg, code });
        } else if (code === 'input_param_error') {
          Modal.error({ title: '提示', content: msg, zIndex: 9999 });
        }
      } catch (error) {
        if (callBack && typeof callBack === 'function') {
          callBack({ msg: error.msg || error.message, code: 'network_error' });
        } else {
          Modal.error({ title: '提示', content: error.msg || error.message, zIndex: 9999 });
        }
      }
    },
    *addTitleProduct({ payload, callBack }, { call, put, select }) {
      try {
        const productCompleteList = yield select(({ product }) => product.productCompleteList);
        const { product_id, keyWordList } = payload;
        console.log(productCompleteList);
        const { data } = yield call(productApi.getProductbyId, { product_id });
        const productInfo = data;

        console.log(productInfo);
        const newList = productInfo.word ? productInfo.word : [];
        const newList2 = newList.concat(keyWordList);
        const list = Array.from(new Set(newList2));
        console.log(list);
        const dataResult = yield call(productApi.updateKeywordProduct, { product_id, word: list });
        if (dataResult.msg == 'success') {
          const result = yield call(productApi.optionProductById, { product_id, opt: 1 });
          const { data, msg } = result;
          if (callBack && typeof callBack === 'function') {
            callBack({ msg, data }); // 返回结果
          }
        } else if (callBack && typeof callBack === 'function') {
          callBack({ msg: dataResult.msg, data: dataResult.data }); // 返回结果
        }
        yield put({
          type: 'product/getProductStat',
          payload: {},
        });
        yield put({
          type: 'product/getProductList',
          payload: {},
        });
      } catch (error) {
        Modal.error({ title: '提示', content: error.msg || error.message, zIndex: 9999 });
      }
    },

    *copyProduct({ payload, callBack }, { call, put, select }) {
      try {
        const productCompleteList = yield select(({ product }) => product.productCompleteList);
        const { product_id } = payload;
        let productInfo = productCompleteList.filter(item => item.id == product_id);
        productInfo = productInfo ? productInfo[0] : {};
        const { data, msg } = yield call(productApi.addProduct, productInfo);
        const productState = yield call(productApi.getProductState);
        const productSize = productState.data[0];
        yield put({
          type: 'home/save',
          payload: {
            productSize,
          },
        });
        if (callBack && typeof callBack === 'function') {
          callBack({ msg, data }); // 返回结果
        }
        yield put({
          type: 'product/getProductStat',
          payload: {},
        });
        yield put({
          type: 'product/getProductList',
          payload: {},
        });
      } catch (error) {
        Modal.error({ title: '提示', content: error.msg || error.message, zIndex: 9999 });
      }
    },
    *addProduct({ payload, callBack }, { call, put, select }) {
      try {
        const addProductData = yield call(productApi.addProduct, { ...payload });

        const { data, msg } = addProductData;
        const productState = yield call(productApi.getProductState);

        const productSize = productState.data[0];
        yield put({
          type: 'home/save',
          payload: {
            productSize,
          },
        });
        yield put({
          type: 'product/getProductStat',
          payload: {},
        });
        yield put({
          type: 'product/getProductList',
          payload: {},
        });
        return { msg, data }; // 返回结果
      } catch (error) {
        return Promise.reject(error);
      }
    },
    *getCateDetailById({ payload }, { call, put, select }) {
      try {
        const { data } = yield call(productApi.getCateById, payload);
      } catch (error) {
        Modal.error({ title: '提示', content: error.msg || error.message, zIndex: 9999 });
      }
    },

    *getChildCateById({ payload, callBack }, { call, put, select }) {
      try {
        const { data } = yield call(productApi.getChildCateById, payload);
        let options = [];
        if (data != null && data.length > 0) {
          options = data.map(item => {
            return {
              value: item.id + '',
              label: item.cat_name,
              isLeaf: false,
            };
          });
        }
        return options; // 返回结果
      } catch (error) {
        return Promise.reject(error);
      }
    },
    *getCityByPid({ payload }, { call, put, select }) {
      try {
        const { data } = yield call(productApi.getCityByPid, payload);

        if (data !== undefined && data.length > 0) {
          const options = data.map(item => {
            return {
              value: item.id + '',
              label: item.area_name,
              isLeaf: false,
            };
          });
          return options;
        }
        return Promise.reject(new Error('data is invalid'));
      } catch (error) {
        return Promise.reject(error);
      }
    },
    *getCateByPid({ payload, callBack }, { call, put, select }) {
      try {
        const { cateLength } = payload;
        const { data } = yield call(productApi.getChildCateById, payload);

        if (data != undefined && data.length > 0) {
          const options = data.map(item => {
            if (cateLength > 2) {
              return {
                value: item.id + '',
                label: item.cat_name,
                isLeaf: true,
              };
            } else {
              return {
                value: item.id + '',
                label: item.cat_name,
                isLeaf: false,
              };
            }
          });
          return options;
        }
        return null;
      } catch (error) {
        return Promise.reject(error);
      }
    },

    *getCityNotSupportPlatform({ payload, callBack }, { call, put, select }) {
      try {
        const { data } = yield call(productApi.getCityNotSupportPlatform, payload);

        if (callBack && typeof callBack === 'function') {
          callBack(data); // 返回结果
        }
      } catch (error) {
        Modal.error({ title: '提示', content: error.msg || error.message, zIndex: 9999 });
      }
    },
    *getCateNotSupportPlatform({ payload, callBack }, { call, put, select }) {
      try {
        const { data } = yield call(productApi.getCateNotSupportPlatform, payload);

        if (callBack && typeof callBack === 'function') {
          callBack(data); // 返回结果
        }
      } catch (error) {
        Modal.error({ title: '提示', content: error.msg || error.message, zIndex: 9999 });
      }
    },

    *getCateProperty({ payload, callBack }, { call, put, select }) {
      try {
        const { data } = yield call(productApi.getCateProperty, payload);
        let options = [];
        if (data) {
          options = data.map(item => {
            const begin = item.fieldname.indexOf('[') + 1;
            const fieldname =
              begin > 0
                ? item.fieldname.substring(begin, item.fieldname.length - 1)
                : item.fieldname;
            return {
              displayname: item.displayname,
              fieldtype: item.fieldtype,
              fieldname: fieldname,
              fieldoptions: item.fieldoptions,
              required: item.required,
            };
          });
        }

        if (callBack && typeof callBack === 'function') {
          callBack(options); // 返回结果
        }
      } catch (error) {
        Modal.error({ title: '提示', content: error.msg || error.message, zIndex: 9999 });
      }
    },

    // 获取商品统计信息
    *getProductStat({ payload, callBack }, { call, put, select }) {
      try {
        const productState = yield call(productApi.getProductState);

        const product = {
          productSize: productState.data[0],
          draft: productState.data[1],
          Pending: productState.data[2],
          passed: productState.data[3],
          Promoting: productState.data[4],
          unPassed: productState.data[5],
          NoMaterial: productState.data[6] + productState.data[7],
          NoData: productState.data[7],
          NoMsgTitle: productState.data[6],
          NoProductTitle: productState.data[8],
        };

        const pagination = { currentPage: 1, pageSize: 10, total: productState.data[0] };
        console.log(pagination);
        yield put({
          type: 'product/save',
          payload: {
            productState: product,
            pagination,
          },
        });
        if (callBack && typeof callBack === 'function') {
          callBack(pagination); // 返回结果
        }
      } catch (error) {
        Modal.error({ title: '提示', content: error.msg || error.message, zIndex: 9999 });
      }
    },
    *getProductList({ payload }, { call, put, select }) {
      try {
        const paginationModel = yield select(({ product }) => product.pagination);
        const { currentPage = 1, pageSize = 10 } = { ...paginationModel, ...payload };

        const {
          data: { items, total },
        } = yield call(productApi.getProductList, {
          ...payload,
          offset: (currentPage - 1) * pageSize,
          limit: pageSize,
        });

        const complete = items;

        if (items != null) {
          const productList = items.map((item, index) => {
            let status = item.status;
            if (item.status === 1) {
              status = '草稿';
            } else if (item.status === 2) {
              status = '待审核';
            } else if (item.status === 3) {
              status = '审核通过';
            } else if (item.status === 4) {
              status = '推广中';
            } else if (item.status === 5) {
              status = '未通过审核';
            } else if (item.status === 6) {
              status = '信息标题已用完';
            } else if (item.status === 8) {
              status = '产品标题已用完';
            } else if (item.status === 7) {
              status = '素材内容已用完';
            }
            return {
              key: item.id,
              sNumber: pageSize * (currentPage - 1) + index + 1,
              id: item.id,
              name: item.name,
              brand: item.brand,
              mode: item.mode,
              word: item.word ? item.word : [],
              time: moment(item.created_at).format('YYYY-MM-DD HH:mm:ss'),
              updateTime: moment(item.updated_at).format('YYYY-MM-DD HH:mm:ss'),
              status,
              statusNumber: item.status,
              platforms: item.platforms,
            };
          });

          const pagination = { currentPage, pageSize, total };
          yield put({
            type: 'product/save',
            payload: {
              productList,
              productCompleteList: complete,
              pagination,
            },
          });
        } else {
          yield put({
            type: 'product/save',
            payload: {
              productList: [],
              productCompleteList: {},
              pagination: { currentPage: 0, pageSize: 0, total: 0 },
            },
          });
        }
      } catch (error) {
        Modal.error({ title: '提示', content: error.msg || error.message, zIndex: 9999 });
      }
    },
  },

  reducers: {
    changeLoginStatus(state, { payload }) {
      return {
        ...state,
        status: payload.status,
        type: payload.type,
      };
    },
    save(state, action) {
      return {
        ...state,
        ...action.payload,
      };
    },
  },
};
