/* eslint-disable compat/compat */
import { routerRedux } from 'dva/router';
import { message } from 'antd';
import moment from 'moment';
import * as merchantApi from '../services/merchant';

export default {
  namespace: 'merchant',

  state: {
    features: {},
    merchantList: [],
    merchantStatus: [
      {
        name: '黄页88',
        enabled: false,
      },
      {
        name: '八方资源',
        enabled: false,
      },
      {
        name: '中国供应商',
        enabled: false,
      },
      {
        name: '酷易搜',
        enabled: false,
      },
      {
        name: '搜了网',
        enabled: false,
      },
      {
        name: '搜了网+爱采购',
        enabled: false,
      },
      // {
      //   name: '列表网',
      //   enabled: false,
      // },
      // {
      //   name: '百姓网',
      //   enabled: false,
      // },
      {
        name: '搜好货',
        enabled: false,
      },
    ],
  },

  subscriptions: {
    setup({ dispatch }) {},
  },
  effects: {
    *updateMerchant({ payload, callBack }, { call, put, select }) {
      try {
        const { data, msg } = yield call(merchantApi.updateMerchant, { ...payload });
        console.log(data);
        if (callBack && typeof callBack === 'function') {
          callBack(msg); // 返回结果
        }
      } catch (error) {
        message.error(error.message);
      }
    },
    *getMerchantStatus({ payload, callBack }, { call, put, select }) {
      try {
        const merchantStatus = yield select(({ merchant }) => merchant.merchantStatus);
        const { data, msg } = yield call(merchantApi.queryMerchantStatus, { ...payload });
        for (let i = 0; i < merchantStatus.length; i++) {
          data.map(item => {
            if (item.name === merchantStatus[i].name) {
              if (item.steps[item.steps.length - 1].status === 1) {
                merchantStatus[i].enabled = true;
              }
            }
            return '';
          });
        }

        const merchantStatusList = data.map(item => {
          let current = 0;
          const steps = item.steps.map((stepsItem, index) => {
            let status = 'finish';
            let disabled = true;
            if (stepsItem.status == 1) {
              status = 'finish';
              disabled = true;
            } else if (stepsItem.status == 0) {
              current = index;
              status = 'process';
              disabled = false;
              if (stepsItem.reason != null && stepsItem.reason.length > 0) {
                status = 'error';
              }
            } else if (stepsItem.status == -1) {
              status = 'wait';
              disabled = true;
            }
            const title = stepsItem.text;
            const { reason } = stepsItem;

            return { status, title, disabled, action: stepsItem.action, reason };
          });

          return {
            name: item.name,
            steps,
            current,
          };
        });

        yield put({
          type: 'merchant/save',
          payload: {
            merchantStatusList,
            merchantStatus,
          },
        });
        if (callBack && typeof callBack === 'function') {
          callBack(msg); // 返回结果
        }
      } catch (error) {
        message.error(error.message);
      }
    },

    *queryMerchant({ payload, callBack }, { call, put, select }) {
      try {
        const { data, msg } = yield call(merchantApi.queryMerchant, { ...payload });
        let features = {
          name: true, // 公司名称
          price: false, // 价格
          areaIds: true, // 公司所在城市
          cate: true, // 公司行业
          mainProduct: true, // 主营产品
          introduce: true, // 公司介绍
          license: true, // 营业执照
          logo: true, // 公司logo
          imageFront: false, // 身份证正面
          imageBack: false, // 身份证反面
          imageHand: false, // 手持身份证
          contactName: true, // 联系人姓名
          phone: true, // 手机号

          address: false, // 公司详细地址
          shortName: false, // 公司简称
          mainBrand: false, // 主要品牌
          companyType: false, // 公司性质
          workingModel: false, // 经营模式
          gender: false, // 性别
          idCardNo: false, // 身份证号码
          email: true, // 邮箱
          qq: true,
          legal: false, // 公司法人
          regNo: false, // 统一社会信用代码
          regAuthority: false, // 发照机关
          regDate: false, // 注册时间
          regAddress: false, // 营业执照注册地址
          business: false, // 经营范围
          validPeriod: false, // 营业期限
          properties: 0, // 必须有几个属性
        };
        let isOpenSou = false; // 搜了网
        let isOpenAi = false; // 搜了网+爱采购
        let isOpenLiebiao = false; // 列表网
        let isOpenBaixing = false; // 百姓网
        let isKuyisoPersonal = false; // 酷易搜个人
        let isOpenChina = false; // 中国供应商
        let isSouHaoHuo = false; // 搜好货
        const merchantList = data.map(item => {
          console.log(item);
          if (item.plat_form === 2 && item.auto_pub) {
            isOpenChina = true;
          }
          if (item.plat_form === 3 && item.account.is_personal && item.auto_pub) {
            isKuyisoPersonal = true;
          }
          if (item.plat_form === 4 && item.auto_pub) {
            isOpenSou = true;
          }
          if (item.plat_form === 5 && item.auto_pub) {
            isOpenSou = true;
            isOpenAi = true;
          }
          if (item.plat_form === 7 && item.auto_pub) {
            isOpenLiebiao = true;
          }
          if (item.plat_form === 8 && item.auto_pub) {
            isOpenBaixing = true;
          }
          if (item.plat_form === 9 && item.auto_pub) {
            isSouHaoHuo = true;
          }
          return {
            contact_phone: item.contact_phone,
            merchant_class: item.merchant_class,
            pause: item.pause,
            name: item.name,
            contact: item.contact,
            company_site: item.company_site,
            id: item.id,
            updated_at: moment(item.updated_at).format('YYYY-MM-DD HH:mm:ss'),
            created_at: moment(item.created_at).format('YYYY-MM-DD HH:mm:ss'),
            account: item.account,
            company_id: item.company_id,
            pub_count: item.pub_count,
            pub_per_count: item.pub_per_count,

            dailyPub: item.daily_pub_products,
            enablePost: item.enable_post_product,
            status: item.status,
            reason: item.reason,
            action: item.action,
          };
        });
        features.address = isOpenSou || isOpenLiebiao || isSouHaoHuo;
        features.shortName = isOpenSou;
        features.companyType = isOpenSou || isOpenChina;
        features.gender = isOpenLiebiao;
        features.imageFront = false;
        features.imageHand = isKuyisoPersonal;
        features.imageBack = isKuyisoPersonal;
        features.idCardNo = isKuyisoPersonal;
        features.legal = isOpenLiebiao || isOpenChina;
        features.regNo = isOpenLiebiao || isOpenChina || isSouHaoHuo || isOpenSou || isOpenAi;
        features.price = isOpenSou || isOpenAi;
        features.regAuthority = isOpenChina;
        features.regDate = isOpenChina || isSouHaoHuo;
        features.regAddress = isOpenChina;
        features.validPeriod = isOpenLiebiao || isOpenChina || isOpenSou || isOpenAi;
        features.business = isOpenChina;
        features.mainBrand = isOpenSou;
        features.workingModel = isOpenSou;
        if (isSouHaoHuo) {
          features.properties = 5;
        }

        yield put({
          type: 'merchant/save',
          payload: {
            merchantList,
            isOpenSou,
            isOpenAi,
            isOpenLiebiao,
            isOpenBaixing,
            features,
          },
        });
        if (callBack && typeof callBack === 'function') {
          callBack(msg); // 返回结果
        }
      } catch (error) {
        message.error(error.message);
      }
    },
  },

  reducers: {
    changeLoginStatus(state, { payload }) {
      return {
        ...state,
        status: payload.status,
        type: payload.type,
      };
    },
    save(state, action) {
      return {
        ...state,
        ...action.payload,
      };
    },
  },
};
