import React from 'react';
import { Button, Modal, Row, Col, Form, Input } from 'antd';

import styles from './SetAlias.less';

class SetAlias extends React.PureComponent {
  state = {
    isModalOpen: false,
  };

  showModal = () => {
    this.setState({ isModalOpen: true });
  };

  handleOk = () => {
    this.setState({ isModalOpen: false });
  };

  handleCancel = () => {
    this.setState({ isModalOpen: false });
  };

  render() {
    return (
      <div>
        <Button type="primary" onClick={this.showModal}>
          设置别名组
        </Button>
        <Modal
          title="设置别名组"
          open={this.state.isModalOpen}
          onOk={this.handleOk}
          onCancel={this.handleCancel}
          width={960}
        >
          <Form>
            <Row gutter={16} justify="space-between" className={styles.customright}>
              <Col span={6}>
                <div className={styles.title_head}>
                  别名组1[0]<span>同步已设置的关键词</span>
                </div>
                <Input.TextArea rows={4} placeholder="请输入别名" />
                <div className={styles.rule}>
                  换行分隔，每个关键词不超过10个字，最多可添加2000个。
                </div>
              </Col>
              <Col span={6}>
                <div className={styles.title_head}>
                  别名组2[0]<span>同步已设置的关键词</span>
                </div>
                <Input.TextArea rows={4} placeholder="请输入别名" />
                <div className={styles.rule}>
                  换行分隔，每个关键词不超过10个字，最多可添加2000个。
                </div>
              </Col>
              <Col span={6}>
                <div className={styles.title_head}>
                  别名组3[0]<span>同步已设置的关键词</span>
                </div>
                <Input.TextArea rows={4} placeholder="请输入别名" />
                <div className={styles.rule}>
                  换行分隔，每个关键词不超过10个字，最多可添加2000个。
                </div>
              </Col>
              <Col span={6}>
                <div className={styles.title_head}>
                  别名组4[0]<span>同步已设置的关键词</span>
                </div>
                <Input.TextArea rows={4} placeholder="请输入别名" />
                <div className={styles.rule}>
                  换行分隔，每个关键词不超过10个字，最多可添加2000个。
                </div>
              </Col>
            </Row>
          </Form>
        </Modal>
      </div>
    );
  }
}
export default SetAlias;
