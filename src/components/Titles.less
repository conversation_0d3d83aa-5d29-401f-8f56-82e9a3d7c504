/* 遮罩层 */
.mask{
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  background: #000;
  z-index: 9000;
  opacity: 0.5;
}

/* 弹出层 */
.modalDlg{
  width: 80%;
  height: 90%;
  position: fixed;
  top: 20px;
  left: 0;
  right: 0;
  z-index: 9999;
  margin: 0 auto;
  background-color: #fff;
  border-radius:5px;
  display: flex;
  flex-direction: column;
  padding: 10px;
  overflow:scroll;
  overflow-x:hidden;

}
.closeIcon{
  position: fixed;
  top: 25px;
  right: 12%;
  font-size: 18px;
  color: #666666;
}

.keywordResult{
  margin-top: 15px;
  height: 50vh;
  overflow:scroll;
  overflow-x:hidden;
  border:1px solid #E6E6E6;
  padding:10px;
}
.keywordItem{
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  margin-top: 5px;
  border:1px solid #E6E6E6;
  padding: 3px 10px;
  border-radius: 5px;

}
.iconDelete{
  margin-left: 5px;
  margin-right: 5px;
}
.creatKeyView{
  margin:15px 15px 10px 15px;
  width: 90%;
}
.keyVoidView{
  margin-top: 10px;

}
.closeIcon{
  position: fixed;
  top: 25px;
  right: 12%;
  font-size: 18px;
  color: #666666;
}
