import { But<PERSON>, <PERSON><PERSON>, <PERSON>, Col } from 'antd';
import React, { useState } from 'react';
import styles from './TitleGroup.less';

const TitleGroup = ({ cache = { keywords: [], alias: [], subtitles: [] } }) => {
  const [open, setOpen] = useState(false);
  return (
    <div>
      <Button type="primary" onClick={() => setOpen(true)}>
        标题组合项
      </Button>
      <Modal
        title="标题组合项"
        centered
        open={open}
        onOk={() => setOpen(false)}
        onCancel={() => setOpen(false)}
        width={1000}
      >
        <Row>
          <Col>
            <div className={styles.everbox}>
              <div className={styles.keyword1}>关键词</div>
              <div className={styles.wordstext}>{cache.keywords.join(',')}</div>
            </div>
            <div className={styles.everbox}>
              <div className={styles.ftitle1}>副标题</div>
              <div className={styles.wordstext}>{cache.subtitles.join(',')}</div>
            </div>
            <div className={styles.everbox}>
              <div className={styles.bname1}>别名</div>
              <div className={styles.wordstext}>{cache.alias.join(',')}</div>
            </div>
          </Col>
        </Row>
      </Modal>
    </div>
  );
};
export default TitleGroup;
