/* 遮罩层 */
.mask{
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  background: #000;
  z-index: 9000;
  opacity: 0.5;
}

/* 弹出层 */
.modalDlg{
  width: 80%;
  height: 90%;
  position: fixed;  
  top: 20px;
  left: 0;
  right: 0;
  z-index: 9999;
  margin: 0 auto;
  background-color: #fff;
  border-radius:5px;
  display: flex;
  flex-direction: column;
  padding: 10px;
  overflow:scroll;
  overflow-x:hidden;
 
}
.itemView{
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: start;
  font-size: 14px;
  color: #000000;
  margin: 10px 10px ;
  margin-top: 20px;
  margin-bottom: 5px;
}
.itemView2{
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: start;
  font-size: 12px;
  color: #bbb;
  margin-left: 10px;
}
  .titleName{
      font-size: 18px;
      color: #666666;
  }
.searchDiv{
  display: flex;
  flex-direction: row;
  width: 100vh
}
.ChexBoxTitle{
  padding-left: 5px;
  line-height: 45px;
  text-align: center;
  background-color: #F2E6D2;
  color: #BA904B;
  justify-content: space-between;
  display: flex;
  flex-direction: row;
  align-content: center;
  align-items: center;
  padding-right: 15px;
}
.cityChexBox{
  padding-left: 5px;
  height: 50vh;
  display: flex;
  border:1px solid #E6E6E6;
  flex-direction: column;
  color: #000;

}
 
.chexBoxChildItem{
  
  justify-content: space-between;
  display: flex;
  flex-direction: row;
  align-content: center;
  align-items: center;
  line-height: 20px;
  padding-left:5px;
  :global {
  
    .ant-checkbox-wrapper{
      white-space: nowrap;
      max-width: 120px;
      overflow: hidden;
      text-overflow: ellipsis;
      animation:antCheckboxEffect 0 ease 0 1 normal;
    }
    .ant-checkbox-checked::after{
      transition: unset;
      animation:antCheckboxEffect 0 ease 0 1 normal;
    }
    .ant-checkbox-checked .ant-checkbox-inner:after {
      transition: unset;
      animation:antCheckboxEffect 0 ease 0 1 normal;
    }
    .ant-checkbox-inner:after {
      transition: unset;
      animation:antCheckboxEffect 0 ease 0 1 normal;
    }
    .ant-checkbox-inner {
      transition: unset;
      animation:antCheckboxEffect 0 ease 0 1 normal;
    }
    
    }
}

 

.keywordResult{
  margin-top: 10px;
  height: 15vh;
  overflow:scroll;
  overflow-x:hidden;
  border:1px solid #E6E6E6;
  padding:10px;
}
.keywordItem{
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  margin-top: 5px;
  border:1px solid #E6E6E6;
  padding: 3px 10px;
  border-radius: 5px;

}
.iconDelete{
  margin-left: 5px;
  margin-right: 5px;
}
.creatKeyView{
  margin:15px 15px 10px 15px;
  width: 90%;
}
 .keyVoidView{
   margin-top: 10px;

 }
 .closeIcon{
  position: fixed;
  top: 25px;
  right: 12%;
 font-size: 18px;
 color: #666666;
}