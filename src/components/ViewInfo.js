import { But<PERSON>, <PERSON><PERSON>, <PERSON>, Col, Typography, Image, Space } from 'antd';
import React from 'react';
import styles from './ViewInfo.less';
import { connect } from 'dva';

const img1 = require('@/assets/1.jpg');

const { Text } = Typography;

class ViewInfo extends React.PureComponent {
  state = { open: false, item: null };

  setOpen = open => {
    this.setState({ open });
    if (open) {
      if (this.state.item != null) {
        return;
      }
      const record = this.props.item;
      this.props.dispatch({
        type: 'info/setInfo',
        payload: record,
        callBack: result => {
          this.setState({ item: result });
        },
      });
    }
  };

  render() {
    const { open, item } = this.state;
    return (
      <div>
        <Button type="link" onClick={() => this.setOpen(true)}>
          查看
        </Button>
        <Modal
          title="信息详情"
          centered
          open={open}
          onOk={() => this.setOpen(false)}
          onCancel={() => this.setOpen(false)}
          width={800}
          footer={
            [] // 设置footer为空，去掉 取消 确定默认按钮
          }
        >
          <Space
            direction="vertical"
            size="middle"
            style={{
              display: 'flex',
            }}
          >
            <Row gutter={[16, 16]}>
              <Col span={4}>信息标题</Col>
              <Col span={20} className={styles.bg}>
                <Text className={styles.text} disabled>
                  {item && item.title}
                </Text>
              </Col>

              <Col span={4}>关键词</Col>
              <Col span={20} className={styles.bg}>
                <Text className={styles.text} disabled>
                  {item && item.word && item.word.join(',')}
                </Text>
              </Col>
              <Col span={4}>产品别名</Col>
              <Col span={20} className={styles.bg}>
                <Text className={styles.text} disabled>
                  {item && item.product_name}
                </Text>
              </Col>
              <Col span={4}>单价</Col>
              <Col span={20} className={styles.bg}>
                <Text className={styles.text} disabled>
                  {item && item.price}
                </Text>
              </Col>
              <Col span={4}>单位</Col>
              <Col span={20} className={styles.bg}>
                <Text className={styles.text} disabled>
                  {item && item.unit}
                </Text>
              </Col>

              <Col span={4}>标题图片</Col>
              <Col span={20}>
                <Row>
                  {item &&
                    item.titlepic.map(item => (
                      <Col span={5}>
                        <Image width={110} height={110} src={item} />
                      </Col>
                    ))}
                </Row>
              </Col>

              <Col span={4}>详情图片</Col>
              <Col span={20}>
                <Row>
                  {item &&
                    item.pic.map(item => (
                      <Col span={5}>
                        <Image width={110} height={110} src={item} />
                      </Col>
                    ))}
                </Row>
              </Col>

              <Col span={4}>详细内容</Col>
              <Col span={20} className={styles.bg}>
                <Text className={styles.text} disabled>
                  {item && item.description}
                </Text>
              </Col>
            </Row>
          </Space>
        </Modal>
      </div>
    );
  }
}

export default connect(({}) => ({}))(ViewInfo);
