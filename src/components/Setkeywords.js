import React, { useState } from 'react';
import { Button, Modal, Row, Col, Form, Input } from 'antd';

import styles from './Setkeywords.less';

class Setkeywords extends React.PureComponent {
  state = {
    isModalOpen: false,
  };

  showModal = () => {
    this.setState({ isModalOpen: true });
  };

  handleOk = () => {
    this.setState({ isModalOpen: false });
  };

  handleCancel = () => {
    this.setState({ isModalOpen: false });
  };

  render() {
    return (
      <div>
        <Button type="primary" onClick={this.showModal}>
          设置关键词组
        </Button>
        <Modal
          title="设置关键词组"
          open={this.state.isModalOpen}
          onOk={this.handleOk}
          onCancel={this.handleCancel}
          width={1100}
        >
          <Form>
            <Row gutter={16} justify="space-between" className={styles.customright}>
              <Col span={6}>
                <div className={styles.title_head}>
                  关键词1[0]<span>同步已设置的关键词</span>
                </div>
                <Input.TextArea rows={4} placeholder="请输入关键词" />
                <div className={styles.rule}>
                  换行分隔，每个关键词不超过10个字，最多可添加2000个。
                </div>
              </Col>
              <Col span={6}>
                <div className={styles.title_head}>
                  关键词2[0]<span>同步已设置的关键词</span>
                </div>
                <Input.TextArea rows={4} placeholder="请输入关键词" />
                <div className={styles.rule}>
                  换行分隔，每个关键词不超过10个字，最多可添加2000个。
                </div>
              </Col>
              <Col span={6}>
                <div className={styles.title_head}>
                  关键词3[0]<span>同步已设置的关键词</span>
                </div>
                <Input.TextArea rows={4} placeholder="请输入关键词" />
                <div className={styles.rule}>
                  换行分隔，每个关键词不超过10个字，最多可添加2000个。
                </div>
              </Col>
              <Col span={6}>
                <div className={styles.title_head}>
                  关键词4[0]<span>同步已设置的关键词</span>
                </div>
                <Input.TextArea rows={4} placeholder="请输入关键词" />
                <div className={styles.rule}>
                  换行分隔，每个关键词不超过10个字，最多可添加2000个。
                </div>
              </Col>
            </Row>
          </Form>
        </Modal>
      </div>
    );
  }
}
export default Setkeywords;
