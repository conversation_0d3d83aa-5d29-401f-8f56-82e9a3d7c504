import { DownOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import { Dropdown, Space, Modal, Button, message } from 'antd';
import React, { Fragment } from 'react';
import { connect } from 'dva';
import UpdateForm from '@/components/UpdateProductShop';
import router from 'umi/router';

const { confirm } = Modal;

class ProductOperate extends React.PureComponent {
  state = {
    updateModalVisible: false,
    stepFormValues: {},
  };

  showDeleteConfirm = () => {
    confirm({
      title: '删除产品?',
      icon: <ExclamationCircleOutlined />,
      content: '删除后无法恢复，确定删除?',
      okText: '确定',
      okType: 'danger',
      cancelText: '取消',
      onOk: () => {
        this.doOperate(4);
      },
      onCancel() {
        console.log('Cancel');
      },
    });
  };

  showPromotionConfirm = () => {
    confirm({
      title: '开始推广?',
      icon: <ExclamationCircleOutlined />,
      content: '产品推广后，建议不要经常修改资料，否则会减少生成的信息量。',
      okText: '开始推广',
      cancelText: '取消',
      onOk: () => {
        this.doOperate(2);
      },
      onCancel() {
        console.log('Cancel');
      },
    });
  };

  doOperate = op => {
    const { id, onFinish } = this.props.item;
    this.props.dispatch({
      type: 'product/optionProductById',
      payload: { product_id: id, opt: op },
      callBack(optMsg) {
        if (optMsg === 'success') {
          // eslint-disable-next-line default-case
          switch (op) {
            case 2:
              message.success('开始推广成功');
              break;
            case 3:
              message.success('取消推广成功');
              break;
            case 4:
              message.success('删除成功');
              break;
          }
          if (onFinish) {
            onFinish();
          }
        } else {
          message.error(optMsg);
        }
      },
    });
  };

  /**
   * 操作产品 op:操作类型 1:编辑 2:开始推广 3:取消推广 4:删除 5: 发布平台编辑
   * @param op
   */
  optionProduct = op => {
    if (op === 4) {
      this.showDeleteConfirm();
    } else if (op === 2) {
      this.showPromotionConfirm();
    } else if (op === 3) {
      this.doOperate(op);
    } else if (op === 5) {
      // 发布平台
      this.handleUpdateModalVisible(!this.state.updateModalVisible, this.props.item);
    } else if (op === 1) {
      // 编辑
     if (this.props.item.mode === 1) {
        // 跳转自定义模板 第一页
        router.push(`/publish/products/customstep1/${this.props.item.id}`);
      } else {
        router.push(`/publish/products/add1/${this.props.item.id}`);
      } 
    }
  };

  handleUpdateModalVisible = (flag, record) => {
    console.log(`handleUpdateModalVisible:${!!flag}`);
    console.log(`record:${record}`);
    this.setState({
      updateModalVisible: !!flag,
      stepFormValues: record || {},
    });
  };

  handleUpdate = fields => {
    this.props.dispatch({
      type: 'product/updateProductShopById',
      payload: {
        ...fields,
      },
      callBack: ({ msg }) => {
        if (msg === 'success') {
          message.success('配置成功');
          if (this.props.onFinish) {
            this.props.onFinish();
          }
        } else {
          message.error(msg);
        }
      },
    });

    this.handleUpdateModalVisible();
  };

  render() {
    const { statusNumber } = this.props.item;
    const updateMethods = {
      handleUpdateModalVisible: this.handleUpdateModalVisible,
      handleUpdate: this.handleUpdate,
    };
    const items = [];
    if (statusNumber === 3) {
      items.push({
        key: `${items.length + 1}`,
        label: (
          <Button type="link" onClick={() => this.optionProduct(2)}>
            开始推广
          </Button>
        ),
      });
    }
    if (statusNumber === 4) {
      items.push({
        key: `${items.length + 1}`,
        label: (
          <Button type="link" onClick={() => this.optionProduct(3)}>
            暂停推广
          </Button>
        ),
      });
    }
    if (
      statusNumber === 1 ||
      statusNumber === 5 ||
      statusNumber === 7 ||
      statusNumber === 3 ||
      statusNumber === 5 ||
      statusNumber === 6 ||
      statusNumber === 8
    ) {
      items.push({
        key: `${items.length + 1}`,
        label: (
          <Button type="link" onClick={() => this.optionProduct(1)}>
            编辑
          </Button>
        ),
      });
      items.push({
        key: `${items.length + 1}`,
        label: (
          <Button type="link" onClick={() => this.optionProduct(5)}>
            发布平台
          </Button>
        ),
      });
    }
    if (statusNumber === 1 || statusNumber === 2 || statusNumber === 5) {
      items.push({
        key: `${items.length + 1}`,
        label: (
          <Button onClick={() => this.optionProduct(4)} type="link">
            删除
          </Button>
        ),
      });
    }
    return (
      <Fragment>
        <Dropdown
          menu={{
            items,
          }}
        >
          <a onClick={e => e.preventDefault()}>
            <Space>
              操作
              <DownOutlined />
            </Space>
          </a>
        </Dropdown>
        <UpdateForm
          {...updateMethods}
          modalVisible={this.state.updateModalVisible}
          item={this.props.item}
        />
      </Fragment>
    );
  }
}
export default connect(({}) => ({}))(ProductOperate);
