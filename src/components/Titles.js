// @flow
import React, {PureComponent} from 'react';
import {connect} from 'dva';
import {<PERSON><PERSON>, Col, Config<PERSON><PERSON>ider, Layout, Modal, Progress, Row, Select, Table} from 'antd';
import 'antd/dist/antd.css';
import {CloseOutlined, DeleteOutlined, EditOutlined} from '@ant-design/icons';
import styles from './AllKeyword.less';
import zh_CN from 'antd/lib/locale-provider/zh_CN'

const {Option} = Select;
const { confirm } = Modal;
const {Header, Content} = Layout;

class Titles extends PureComponent {
  state = {
    keywordList:[],
    MaxLen:15,
    Name:'',
  };

  componentDidMount(){
    const {keywordList, <PERSON><PERSON>en, Name}=this.props;
    this.sortKeywords(keywordList);
    this.setState(
      {
        keywordList, Max<PERSON>en, Name
      }
    )

  }

  sortKeywords(keywords) {
    keywords.sort(function (a,b){
      return b.length-a.length;
    });
  }

  consoleKeyWord=()=>{
    this.props.close();
  }
  onSaveBtn=()=>{
    const {keywordList } =this.state;
    this.props.onSaveBtn(keywordList);
    this.setState({
      keywordList:[],
    })
    this.props.close();
  }
  deleteKeyWordItem = (item, index) => {
    const that = this;
    const {keywordList, Name}=this.state;
    const title = '删除'+Name;
    const content ='确定删除该'+Name+'?';
    confirm({
      title: title,
      content: content,
      zIndex: 9999,
      cancelText: '取消',
      okText: '确定',
      onOk() {
        keywordList.splice(index, 1);
        let list= keywordList;

        that.setState({
          keywordList:list,
          inputValue: that.state.inputValue + "a"
        });

      },
      onCancel() { },
    });

  }


  clearKeyWord= () => {
    const that = this;
    const {Name}=this.state;
    confirm({
      title: '清空'+Name,
      content: '确定清空所有'+Name+'?',
      zIndex: 9999,
      cancelText: '取消',
      okText: '确定',
      onOk() {
        let list= [];
        that.setState({
          keywordList:list,
          inputValue: that.state.inputValue + "a"
        });

      },
      onCancel() { },
    });

  }

  render() {
    const {keywordList,MaxLen}=this.state;
    return (
      <div style={{ background: '#fff', padding: 24  }}>
        <div className={styles.mask}  onClick={this.consoleAlertInput}></div>
        <div className={styles.modalDlg}>
          <div style={{ color: '#000' }} className={styles.keyVoidView}>
            <div  className={styles.keywordResult}>
              <Row gutter={16}>

                {keywordList.map((item,index) => {
                  return  <Col span={8}>
                    <div  className={styles.keywordItem} style={{'border':item.length>MaxLen?
                      '1px solid red':'1px solid #d3d3d3'}}>
                      {item}
                      <DeleteOutlined  className={styles.iconDelete}    onClick={() => this.deleteKeyWordItem(item, index)}/>
                    </div></Col>
                })}
              </Row>
            </div>
            <div style={{marginTop:'15px'}}>
              当前共计{keywordList.length}个添加项
              <Button type="primary"  style={{marginLeft:'10px'}} onClick={this.clearKeyWord}>
                清空
              </Button>
            </div>
            <div style={{marginTop:'15px'}}>
              <Row gutter={16}>
                <Col span={12}>
                  <Button type="primary" block onClick={this.onSaveBtn}>
                    确定
                  </Button>
                </Col>
                <Col span={12}>
                  <Button type="primary" block onClick={this.consoleKeyWord}>
                    取消
                  </Button>
                </Col>
              </Row>
            </div>
          </div>
        </div>
      </div>)
  }
}

export default connect(({ }) => ({
}))(Titles);
