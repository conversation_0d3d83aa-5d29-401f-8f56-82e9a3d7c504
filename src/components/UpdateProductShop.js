import React, { PureComponent } from 'react';
import 'antd/dist/antd.css';
import { Form, Modal, Select, Switch } from 'antd';
import { connect } from 'dva';

const FormItem = Form.Item;

class UpdateForm extends PureComponent {
  formRef = React.createRef();

  render() {
    const { modalVisible, handleUpdate, item = {}, handleUpdateModalVisible } = this.props;

    const okHandle = () => {
      const form = this.formRef.current;
      const data = form.getFieldsValue();

      for (const i in data) {
        item.platforms[i].on = data[i];
      }

      const payload = {
        product_id: item.key,
        platforms: item.platforms,
      };

      handleUpdate(payload);
    };

    const changecheck = (key, check) => {
      console.log(check);
      console.log(key);
      this.formRef.current.setFieldsValue({
        [key]: check,
      });
    };

    const formItemLayout = {
      labelCol: {
        xs: { span: 24 },
        sm: { span: 6 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 14 },
      },
    };
    const namemap = {
      0: '黄页88',
      1: '八方资源',
      2: '中国供应商',
      3: '酷易搜',
      4: '搜了网',
      5: '搜了网+爱采购',
      // 7: '列表网',
      // 8: '百姓网',
      9: '搜好货',
    };

    return (
      <Modal
        destroyOnClose
        title="发布平台设置"
        visible={modalVisible}
        onOk={okHandle}
        onCancel={() => handleUpdateModalVisible()}
      >
        <Form
          ref={this.formRef}
          initialValues={{
            0: item.platforms && item.platforms[0] && item.platforms[0].on,
            1: item.platforms && item.platforms[1] && item.platforms[1].on,
            2: item.platforms && item.platforms[2] && item.platforms[2].on,
            3: item.platforms && item.platforms[3] && item.platforms[3].on,
            4: item.platforms && item.platforms[4] && item.platforms[4].on,
            5: item.platforms && item.platforms[5] && item.platforms[5].on,
            7: item.platforms && item.platforms[7] && item.platforms[7].on,
            8: item.platforms && item.platforms[8] && item.platforms[8].on,
            9: item.platforms && item.platforms[9] && item.platforms[9].on,
          }}
        >
          {Object.keys(namemap).map(v => {
            return (
              item.platforms &&
              item.platforms[v] !== undefined && (
                <FormItem {...formItemLayout} label={namemap[v]} name={v} key={v}>
                  <Switch
                    checkedChildren="启用"
                    unCheckedChildren="关闭"
                    defaultChecked={item.platforms[v].on}
                    onChange={checked => changecheck(v, checked)}
                  />
                </FormItem>
              )
            );
          })}
        </Form>
      </Modal>
    );
  }
}

export default connect(({}) => ({}))(UpdateForm);
