// @flow
import React, { PureComponent } from 'react';
import { connect } from 'dva';
import {
  Form,
  Table,

  Divider,
  Select,
  Row,
  Col,
  Button,
  AutoComplete,
  Input,
  Upload,
  Layout,
  message,
  Modal,
  Pagination
} from 'antd';

import { CloseOutlined, CloudUploadOutlined } from '@ant-design/icons';
import 'antd/dist/antd.css';
import styles from './PhotoWall.less';
import MyCheckImgBox from './MyCheckImgBox';
import PhotoAlbum from '../pages/v1/publish/album/PhotoAlbum';

import { baseUrl } from '../configs/config';
const { Option } = Select;

const { Header, Content } = Layout;
const columnCount = 5;
class PhotoWall extends PureComponent {
  state = {
    selPic: {},
    selPicList: [],
    selPicListurl: [],
    Picpagination:{current: 1,total:20,pageSize:20},
    isShowPhotoAlbum:false
  };
  componentDidMount() {
    const that = this;
    const {Picpagination,ischange }=this.state;

    this.props.dispatch({ type: 'picture/getAlbumsList', payload: {},callBack:(albumsList)=>{

      that.setState({
        albumsList:albumsList,
        selAlbumsId:albumsList[0].id
      },()=>{

        that.props.dispatch({
          type: 'picture/getPicturesList', payload: { id: that.state.selAlbumsId,Picpagination }, callBack: (picList) => {
            if(picList.length>=Picpagination.pageSize&&Picpagination.current==(Picpagination.total/Picpagination.pageSize)){
              Picpagination.total=Picpagination.total+Picpagination.pageSize;
              let newPagin=Picpagination;
              that.setState({
                Picpagination:newPagin
              })
            }
            that.setState({
              pictures: picList,
            })
            that.props.dispatch({ type: 'picture/getAlbumsList', payload: {},callBack:(albumsList)=>{

              that.setState({
                albumsList:albumsList,
                ischange:!ischange
              })
            }})
          }
        });
      })
    }});



  }
  consoleCompany = () => {
    this.props.close();
  }


  handleChange = (value) => {

    const that = this;

    const {ischange}=this.state;

     let Picpagination={current: 1,total:20,pageSize:20};



    this.props.dispatch({
      type: 'picture/getPicturesList', payload: { id: value,Picpagination:{current: 1,total:20,pageSize:20} }, callBack: (picList) => {


        if(picList.length>=Picpagination.pageSize&&Picpagination.current==(Picpagination.total/Picpagination.pageSize)){
          Picpagination.total=Picpagination.total+Picpagination.pageSize;

          let newPagin=Picpagination;

          that.setState({
            Picpagination:newPagin
          })
        }else{
          Picpagination.total=Picpagination.pageSize;
          let newPagin=Picpagination;

          that.setState({
            Picpagination:newPagin
          })
        }
        console.log()
        that.setState({
          pictures: picList,

          ischange:!ischange
        })
        that.props.dispatch({ type: 'picture/getAlbumsList', payload: {},callBack:(albumsList)=>{

          that.setState({
            albumsList:albumsList,
            ischange:!ischange
          })
        }})
      }
    });
    this.setState({
      selAlbumsId: value

    })

}

  onPicChange = (item) =>  {
    const { selPic, selPicList,selAlbumsId,selPicListurl } = this.state;

    let selAlbumsCheckPic = selPicList;
    let checkedList = [];
    let newcheckedList = checkedList.concat(selAlbumsCheckPic);
    let newcheckedUrlList = checkedList.concat(selPicListurl);
    const itemIndex = selAlbumsCheckPic.indexOf(item.id);
    const isCheck = itemIndex !== -1;
    console.log(isCheck)

    if (isCheck) {
      newcheckedUrlList.splice(itemIndex, 1);
      newcheckedList.splice(itemIndex, 1);
    } else {
      newcheckedUrlList.push(item.url);
      newcheckedList.push(item.id);
    }
    console.log(newcheckedUrlList)

    this.setState({
      selPicList: newcheckedList,
      selPicListurl:newcheckedUrlList
    })
  };
  selAll = () => {
    const { ischange,  selPicList,selAlbumsId,Picpagination,pictures } = this.state;

    let newSelPic=[];
    let newcheckedUrlList=[];
    for(let i=0;i<pictures.length;i++){

        newSelPic.push(pictures[i].id)
        newcheckedUrlList.push(pictures[i].url)
    }
    this.setState({
      selPicList:newSelPic,
      selPicListurl:newcheckedUrlList,
      ischange:!ischange
    })


  }
  selAllNo = () => {
    const { ischange,  selPicList,selAlbumsId,Picpagination,pictures } = this.state;


    let newcheckedUrlList=[];
    this.setState({
      selPicList:[],
      selPicListurl:newcheckedUrlList,
      ischange:!ischange
    })


  }
  selReverseAll = () => {
    const { ischange,  selPicList,selAlbumsId,Picpagination,pictures } = this.state;
    let newSelPic=[];
    let newcheckedUrlList=[];
    for(let i=0;i<pictures.length;i++){
      if(selPicList.indexOf(pictures[i].id)==-1){
        newSelPic.push(pictures[i].id)
        newcheckedUrlList.push(pictures[i].url)
      }
    }


    this.setState({
      selPicList:newSelPic,
      selPicListurl:newcheckedUrlList,
      ischange:!ischange
    })


  }


  console = () => {
    this.props.close();
  }
  onSaveBtn = () => {
    const { picList = [] } = this.props;
    const { selPicList = [] ,selPicListurl=[]} = this.state;
    if ((picList.length + selPicList.length) > 60) {
      Modal.error({
        content: '图片不能超过60个',
        zIndex: 9999
      });
      return;
    }

    this.props.onAddPic(selPicListurl);

    this.props.close();
  }

  handleChangeQrcode = info => {
    const { selAlbumsId } = this.state;
    const {ischange,Picpagination}=this.state;
    const { file } = info;
    const that=this;
    if (file && file.status == 'done') {
      if(file.response.data[0].msg=="该图片已存在"){

        message.error('图片重复，上传失败');
      }else{
        message.success('图片上传成功');

      }
    }
    this.setState({
      selPicList:[],
      Picpagination:{current: 1,total:20,pageSize:20},
    })

    if (file && file.status == 'done') {
      this.props.dispatch({ type: 'picture/getAlbumsList', payload: {},callBack:(albumsList)=>{

        that.setState({
          albumsList:albumsList,
          ischange:!ischange
        })
      }});

      this.props.dispatch({ type: 'picture/getPicturesList', payload: { id: selAlbumsId,Picpagination:{current: 1,total:20,pageSize:20}},callBack: (picList) => {
        console.log(picList)
        if(picList.length>=Picpagination.pageSize&&Picpagination.current==(Picpagination.total/Picpagination.pageSize)){
          Picpagination.total=Picpagination.total+Picpagination.pageSize;
          let newPagin=Picpagination;
          that.setState({
            Picpagination:newPagin
          })
        }
        let newPicList=picList;
          that.setState({
          pictures: newPicList,
          ischange:!ischange
        })
        that.props.dispatch({ type: 'picture/getAlbumsList', payload: {},callBack:(albumsList)=>{

          that.setState({
            albumsList:albumsList,
            ischange:!ischange
          })
        }})
      }
     });


    }
  }
  beforeUpload=(file)=>{
    const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png'|| file.type === 'image/jpg';
    if (!isJpgOrPng) {
      message.error('图片上传格式必须为jpg/png');
    }
    const isLt2M = file.size / 1024 / 1024 < 1;
    if (!isLt2M) {
      message.error('图片大小必须小于 1MB!');
    }
    return isJpgOrPng && isLt2M;
  }
  onShowSizeChange = (current, pageSize) => {

    const { ischange,  selAlbumsId,Picpagination } = this.state;
    const that=this;
    Picpagination.current=current;
    Picpagination.pageSize=pageSize;
    console.log(Picpagination)

    let newPagin=Picpagination;
    that.setState({
      Picpagination:newPagin
    })

    this.props.dispatch({
            type: 'picture/getPicturesList', payload: { id: selAlbumsId,Picpagination }, callBack: (picList) => {

              if(picList.length>=Picpagination.pageSize&&Picpagination.current==(Picpagination.total/Picpagination.pageSize)){
                Picpagination.total=Picpagination.total+Picpagination.pageSize;

              }
              let newPagin=Picpagination;
              that.setState({
                Picpagination:newPagin
              })
              that.props.dispatch({ type: 'picture/getAlbumsList', payload: {},callBack:(albumsList)=>{

                that.setState({
                  albumsList:albumsList,
                  ischange:!ischange
                })
              }})
              that.setState({
                pictures: picList,
                ischange:!ischange,

              })
            }
          });

  };

  onChangePage = page => {
    console.log(page);
    const { ischange,  selAlbumsId,Picpagination } = this.state;
    const that=this;
    Picpagination.current=page;
    console.log(Picpagination)


    this.props.dispatch({
            type: 'picture/getPicturesList', payload: { id: selAlbumsId,Picpagination }, callBack: (picList) => {
              if(picList.length>=Picpagination.pageSize&&Picpagination.current==(Picpagination.total/Picpagination.pageSize)){
                Picpagination.total=Picpagination.total+Picpagination.pageSize;


              }
              let newPagin=Picpagination;
              that.setState({
                Picpagination:newPagin
              })
              that.setState({
                pictures: picList,
                ischange:!ischange,

              })
              that.props.dispatch({ type: 'picture/getAlbumsList', payload: {},callBack:(albumsList)=>{

                that.setState({
                  albumsList:albumsList,
                  ischange:!ischange
                })
              }})
            }
          });


  };
  consolePhotoAlbum=()=>{
    const that=this;
    const { ischange,  selAlbumsId,Picpagination } = this.state;
    this.props.dispatch({ type: 'picture/getAlbumsList', payload: {},callBack:(albumsList)=>{

      that.setState({
        albumsList:albumsList,
        selAlbumsId:albumsList[0].id
      },()=>{

        that.props.dispatch({
          type: 'picture/getPicturesList', payload: { id: selAlbumsId,Picpagination }, callBack: (picList) => {
            if(picList.length>=Picpagination.pageSize&&Picpagination.current==(Picpagination.total/Picpagination.pageSize)){
              Picpagination.total=Picpagination.total+Picpagination.pageSize;
              let newPagin=Picpagination;
              that.setState({
                Picpagination:newPagin
              })

            }
            that.setState({
              pictures: picList,
            })
          }
        });
      })
    }});
    this.setState({
      isShowPhotoAlbum:false,
      isBack:false
     })

  }
  showPhotoAlbum=()=>{

    this.setState({
      isShowPhotoAlbum:true,
      isBack:true
     })
  }


  render(){
    const { albumsList = [], pictures = [], selAlbumsId = 0, selPicList=[],Picpagination } = this.state;
    let selAlbumsName = "默认相册";
    let selItem = {};


    if (selAlbumsId == 0) {
      if (albumsList.length > 0) {
        selAlbumsName = albumsList[0].name;
      }

    } else {
      if (albumsList.length > 0) {
        selItem = albumsList.filter(item => item.id == selAlbumsId)[0];
        if (selItem) {
          selAlbumsName = selItem.name
        }

      }

    }

    let Authorization = { "Authorization": 'Bearer ' + window.sessionStorage.getItem("Authorization"), "multiple": "" };
    return (
      <div style={{ background: '#fff', padding: 24 }}>
        <div className={styles.mask} onClick={this.consoleCompany}></div>
        <div className={styles.modalDlg}>
          <div className={styles.titleName}>相册</div>
          <CloseOutlined className={styles.closeIcon} onClick={this.consoleCompany} />
          <div className={styles.itemView}>
            <div style={{ marginLeft: '5px', marginRight: '5px' }}> 公司相册</div>
            <div>
              <Select
                defaultValue={selAlbumsName}
                style={{ width: 120 }}
                onChange={this.handleChange}
                getPopupContainer={triggerNode => triggerNode.parentNode}
              >
                {albumsList.map(item => (
                  <Option value={item.id}>{item.name}</Option>
                ))}

              </Select>
            </div>
            <div style={{ marginLeft: '10px' }}>
              <Upload
                name="product"
                multiple
                action={baseUrl + '/v2/album/upload/' + selAlbumsId}
                // withCredentials
                headers={Authorization}
                data={file => ({ // data里存放的是接口的请求参数
                  file: file, // file 是当前正在上传的图片

                })}
                beforeUpload={this.beforeUpload}
                showUploadList={false}
                className="avatar-uploader"
                onChange={this.handleChangeQrcode}
              >
                <Button>
                  <CloudUploadOutlined />
             上传
          </Button>
              </Upload>
            </div>
            <div style={{position:'absolute',right:'0px',marginRight:'15px'}} onClick={this.showPhotoAlbum}>相册管理</div>
          </div>

          <div className={styles.cityChexBox} style={{ height: '100%' }}>
            <Row style={{ justifyContent: 'start' }}>

            {pictures.map((item, index) => {
                return <Col key={item.name+index}>
                  <MyCheckImgBox
                    checked={selPicList && selPicList.indexOf(item.id) !== -1}
                    value={item.url}
                    name={item.name}
                    item={item}
                    id={item.id}
                    index={index}
                    disable={false}
                    onChange={(item) => this.onPicChange(item)}

                  >
                  </MyCheckImgBox> </Col>
              })}
            </Row>
          </div>
          <Row style={{display:'flex',justifyContent:'space-between',marginTop:'10px'}}>
            <Col span={16}>
              <Row>
                <Col  style={{color:'black',lineHeight:'30px',justifyContent:'center'}}>
                 共{selItem.display_total}张图片
                </Col>
      <Col  >
                <Button  appearance="default" size="sm" style={{ marginLeft: '10px' }} onClick={this.selAll}>全选</Button>
                </Col>
                <Col  >
                <Button  appearance="default" size="sm" style={{ marginLeft: '10px' }} onClick={this.selAllNo}>全不选</Button>
                </Col>
                <Col  >
                <Button  appearance="default" size="sm" style={{ marginLeft: '10px' }} onClick={this.selReverseAll}>反选</Button>
                </Col>

              </Row>

            </Col>
            <Col span={8} style={{display:'flex',justifyContent:'flex-end'}}> <Pagination onShowSizeChange={this.onShowSizeChange}  current={Picpagination.current} pageSize={Picpagination.pageSize} onChange={this.onChangePage} total={Picpagination.total} /></Col>
          </Row>
           <div style={{ marginTop: '10px', display: 'flex', flexDirection: 'row', justifyContent: 'center' }}>
            <Row gutter={16}>
              <Col span={12}>
                <Button type="primary" style={{ width: '120px' }} block onClick={this.onSaveBtn}>
                  确定
              </Button>
              </Col>
              <Col span={12}>
                <Button type="primary" style={{ width: '120px' }} block onClick={this.console}>
                  取消
                </Button>
              </Col>
            </Row>
          </div>
        </div>
        {this.state.isShowPhotoAlbum&&<PhotoAlbum close={this.consolePhotoAlbum}/>}
      </div>
    );
  }
}

export default connect(({ picture }) => ({
  ...picture
}))(PhotoWall);
