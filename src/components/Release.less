.titleView{
  border-left: 5px solid #FF6600;
  color: black;
  font-weight: 600;
  font-size: 22px;
  padding-left: 10px;
}
.dottedLine{
  margin-top: 5px;
  border-bottom: 1px dashed #B6B6B6;
}
.childItem{
  margin:5px 15px;
  display: flex;
  flex-direction: row;
  padding: 10px;
}
.itemView{
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: start;
  font-size: 20px;
  color: #FF6600;
  margin: 10px 10px ;
}
.CardView{
  cursor: pointer;
}
.tipTitle{
  text-align: center;
  align-items: center;
  font-size: 20px;
  color: #FF6600;
}
.closeIcon{
  position: fixed;
  top: 25px;
  right: 12%;
 font-size: 18px;
 color: #666666;
}
.iconView{
 height: 25px;
 width: 25px;
}
.iconView2{
  height: 20px;
  width: 20px;
 }
