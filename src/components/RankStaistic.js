import React from 'react';

import { AppstoreFilled, DiffFilled } from '@ant-design/icons';
import { Row, Col, Card, Statistic } from 'antd';
import styles from './RankStaistic.less';

class RankStaistic extends React.PureComponent {
  render() {
    const { statics } = this.props;
    return (
      <div className={styles.specing}>
        <Row justify="space-between">
          <Col span={5}>
            <Card className={styles.cardbg} bordered={false}>
              <div className={styles.rankingtop}>
                <div className={styles.totalnumbers}>
                  <Statistic title="总数量排名" value={statics.rank_total} />
                </div>
                <div className={styles.rank_iconblue}>
                  <AppstoreFilled className={styles.rankiconblue} />
                </div>
              </div>
            </Card>
          </Col>
          <Col span={5}>
            <Card className={styles.cardbg}>
              <div className={styles.rankingtop}>
                <div className={styles.totalnumbers}>
                  <Statistic title="发布信息数量" value={statics.info_total} />
                </div>
                <div className={styles.rank_iconblue}>
                  <DiffFilled className={styles.rankicongreen} />
                </div>
              </div>
            </Card>
          </Col>
          <Col span={2}>
            <Card className={styles.cardbg2}>
              <Statistic
                title="百度"
                titleStyle={{
                  textAlign: 'center',
                }}
                value={statics.baidu}
                valueStyle={{
                  color: '#0a9ccd',
                  textAlign: 'center',
                }}
              />
            </Card>
          </Col>
          <Col span={2}>
            <Card className={styles.cardbg2}>
              <Statistic
                title="搜狗"
                titleStyle={{
                  textAlign: 'center',
                }}
                value={statics.sougou}
                valueStyle={{
                  color: '#fa690d',
                  textAlign: 'center',
                }}
              />
            </Card>
          </Col>
          <Col span={2}>
            <Card className={styles.cardbg2}>
              <Statistic
                title="好搜"
                titleStyle={{
                  textAlign: 'center',
                }}
                value={statics.haosou}
                valueStyle={{
                  color: '#6ccf6c',
                  textAlign: 'center',
                }}
              />
            </Card>
          </Col>
          <Col span={2}>
            <Card className={styles.cardbg2}>
              <Statistic
                title="神马"
                titleStyle={{
                  textAlign: 'center',
                }}
                value={statics.shenma}
                valueStyle={{
                  color: '#b6714b',
                  textAlign: 'center',
                }}
              />
            </Card>
          </Col>
          <Col span={2}>
            <Card className={styles.cardbg2}>
              <Statistic
                title="头条"
                titleStyle={{
                  textAlign: 'center',
                }}
                value={statics.toutiao}
                valueStyle={{
                  color: '#f61e01',
                  textAlign: 'center',
                }}
              />
            </Card>
          </Col>
        </Row>
      </div>
    );
  }
}
export default RankStaistic;
