import React, { PureComponent } from 'react';
import { connect } from 'dva';
import videojs from 'video.js';
import 'video.js/dist/video-js.css';

class Video extends React.Component {
  constructor(props) {
    super(props);
    this.videoNode = React.createRef();
    this.player = null;
  }

  componentDidMount() {
    // instantiate Video.js
    const options = {
      controls: true,
    };
    this.player = videojs(this.videoNode.current, options, function onPlayerReady() {
      console.log('onPlayerReady', this);
    });
  }

  componentWillUnmount() {
    if (this.player) {
      this.player.dispose();
    }
  }

  componentDidUpdate(prevProps) {
    if (prevProps.value !== this.props.value) {
      // 更新video源
      this.player.src(this.props.value);
    }
  }

  render() {
    const { width, height } = this.props;
    return (
      <video
        className="video-js"
        width={width}
        height={height}
        ref={this.videoNode}
      >
        <source src={this.props.value}></source>
        <track kind="captions" />
      </video>
    );
  }
}

export default connect(() => ({}))(Video);
