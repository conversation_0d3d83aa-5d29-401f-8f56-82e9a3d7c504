// @flow
import React, {PureComponent} from 'react';
import {connect} from 'dva';
import {<PERSON><PERSON>, Col, ConfigProvider, Layout, Progress, Row, Select, Table} from 'antd';
import 'antd/dist/antd.css';
import {CloseOutlined} from '@ant-design/icons';
import styles from './Seek.less';
import zh_CN from 'antd/lib/locale-provider/zh_CN'

const {Option} = Select;

const {Header, Content} = Layout;

class RankingList extends PureComponent {
  state = {
    confirmDirty: false,
    autoCompleteResult: [],
    keyword: '',
    pub_type: '',
    status: 'm_baidu',
  };

  consoleCompany = () => {
    this.props.close();
  }
  handleChange = (value) => {
    this.setState({
      pub_type: value
    })
    const {userId} = this.props;
  }

  changePageSize = (content) => {
    console.log(content)
    const {pagination = {}} = this.props;
    const {pageSize} = content;
    const {status} = this.state;
    this.props.dispatch({type: 'seek/getSeekList', payload: {status, currentPage: pagination.current, pageSize}});
  }
  pageChange = (currentPage) => {
    const {status,} = this.state;
    const {pagination = {}} = this.props;
    this.props.dispatch({type: 'seek/getSeekList', payload: {status, currentPage: currentPage}});
  }

  componentDidMount() {
    console.log(this.props)
    this.props.dispatch({type: 'seek/getSeek', payload: {}});
    this.props.dispatch({type: 'seek/getSeekList', payload: {eg: 'pc_baidu'}});
  }

  search = () => {
    const {keyword, pub_type} = this.state;
  }

  handleChangeStatus = (value) => {
    this.setState({
      status: value
    })
  }
  search = () => {
    const {status} = this.state;
    this.props.dispatch({type: 'seek/getSeekList', payload: {status, currentPage: 1}});
  }

  render() {
    const renderContent = (value, row, index) => {
      const obj = {
        children: value,
        props: {},
      };

      return obj;
    };

    const columns = [
      {
        title: '信息标题',
        dataIndex: 'name',
        render: renderContent,
      },
      {
        title: '收录时间',
        dataIndex: 'seekTime',
        render: renderContent,
        width: 200,
      },
      {
        title: '操作',
        key: 'action',
        width: 100,
        render: (text, record) => (
          <span className={styles.aText}>
          {<Button type="link" onClick={() => {
            window.open(record.url, '_blank');
          }}>查看信息</Button>}

      </span>
        ),
      },
    ];

    const {seekList = [], seekCount = {}, pagination = {}} = this.props;

    const paginationProps = {
      ...pagination,
      showSizeChanger: false,
      showQuickJumper: true,
      pageSizeOptions: ['10', '20', '30', '100'],

      onChange: this.pageChange
    };


    return (
      <div style={{background: '#fff', padding: 24}}>
        <div className={styles.mask} onClick={this.consoleCompany}></div>
        <div className={styles.modalDlg}>
          <div className={styles.titleName}>收录统计</div>
          <CloseOutlined className={styles.closeIcon} onClick={this.consoleCompany}/>
          <div style={{color: '#000000', marginLeft: '15px'}}>
            发布信息总数=显示中的信息数
          </div>
          <Row type="flex" justify="space-around" gutter={16} align="middle">
            <Col span={5}>
              <div style={{display: 'flex'}}>

                <div className={styles.InfoList}>
                  <div className={styles.InfoItem}>
                    <div className={styles.itemTitle}>信息总数</div>
                    <div className={styles.itemValue}>{seekCount.itemTotal}条</div>
                  </div>
                  <div className={styles.InfoItem}>
                    <div className={styles.itemTitle}>信息收录数</div>
                    <div className={styles.itemValue}>{seekCount.Included}条</div>
                  </div>
                  <div className={styles.InfoItem}>
                    <div className={styles.itemTitle}>信息未收录数</div>
                    <div className={styles.itemValue}>{seekCount.NoIncluded}条</div>
                  </div>
                  <div className={styles.InfoItem}>
                    <div className={styles.itemTitle}>信息收录率</div>
                    <div className={styles.itemValue}>{seekCount.IncludedRate}%</div>
                  </div>
                </div>
              </div>
            </Col>
            <Col span={4}>
              <div style={{display: 'flex', flexDirection: 'column', justifyContent: 'center', textAlign: 'center'}}>
                <div style={{color: '#000000'}}>百度收录{seekCount.baiduIc}条</div>
                <Progress
                  style={{marginTop: '10px'}}
                  type="circle"
                  strokeColor={{
                    '0%': '#5B9DFF',
                    '100%': '#5B9DFF',
                  }}
                  strokeWidth={10}
                  percent={seekCount.baiduRate}
                />
              </div>

            </Col>
            <Col span={4}>
              <div style={{display: 'flex', flexDirection: 'column', justifyContent: 'center', textAlign: 'center'}}>
                <div style={{color: '#000000'}}>搜狗收录{seekCount.sogouIc}条</div>
                <Progress
                  style={{marginTop: '10px'}}
                  type="circle"
                  strokeColor={{
                    '0%': '#00BE3C',
                    '100%': '#00BE3C',
                  }}
                  strokeWidth={10}
                  percent={seekCount.sogouRate}
                />
              </div>
            </Col>
            <Col span={4}>
              <div style={{display: 'flex', flexDirection: 'column', justifyContent: 'center', textAlign: 'center'}}>
                <div style={{color: '#000000'}}>360收录{seekCount.haosouIc}条</div>
                <Progress
                  style={{marginTop: '10px'}}
                  type="circle"
                  strokeColor={{
                    '0%': '#F84C19',
                    '100%': '#F84C19',
                  }}
                  strokeWidth={10}
                  percent={seekCount.haosouRate}
                />
              </div>

            </Col>
            <Col span={4}>
              <div style={{display: 'flex', flexDirection: 'column', justifyContent: 'center', textAlign: 'center'}}>
                <div style={{color: '#000000'}}>头条收录{seekCount.toutiaoIc}条</div>
                <Progress
                  style={{marginTop: '10px'}}
                  type="circle"
                  strokeColor={{
                    '0%': '#F84C19',
                    '100%': '#F84C19',
                  }}
                  strokeWidth={10}
                  percent={seekCount.toutiaoRate}
                />
              </div>

            </Col>
          </Row>
          <Row>
            <Col md={7} sm={24}>
              <div className={styles.lableView}>
                搜索引擎：
                <Select defaultValue="百度电脑端" style={{width: 120}} value={this.state.status}
                        onChange={this.handleChangeStatus}
                        getPopupContainer={triggerNode => triggerNode.parentNode}
                >
                  <Option value="m_baidu">百度移动</Option>
                  <Option value="pc_baidu">百度电脑端</Option>
                  <Option value="pc_sogou">搜狗电脑端</Option>
                  <Option value="m_sogou">搜狗移动</Option>
                  <Option value="pc_haosou">360</Option>
                  <Option value="m_toutiao">头条</Option>

                </Select>
              </div>

            </Col>
            <Col md={3} sm={24}>
              <div className={styles.lableView}>
            <span className={styles.submitButtons}>
              <Button type="primary" onClick={this.search}>
                查询
              </Button>
            </span>
              </div>
            </Col>
          </Row>
          <div className={styles.tableView}>
            <ConfigProvider locale={zh_CN}>
              <Table columns={columns} dataSource={seekList} bordered

                     pagination={paginationProps}/>
            </ConfigProvider>
          </div>
        </div>
      </div>
    );
  }
}

export default connect(({seek, home}) => ({
  ...seek, ...home
}))(RankingList);
