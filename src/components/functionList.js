// @flow
import React, { PureComponent } from 'react';
import { connect } from 'dva';
import {
  Form,
  Table,

  Divider,
  Select,
  Row,
  Col,
  Button,
  AutoComplete,
  Input,
  Layout,
} from 'antd';
import { CloseOutlined } from '@ant-design/icons';
import 'antd/dist/antd.css';
import styles from './functionList.less';

const { Option } = Select;

const { Header, Content } = Layout;

 class ShopListForm extends PureComponent {
  state = {
    confirmDirty: false,
    autoCompleteResult: [],
  };

  consoleCompany=()=>{
    this.props.close();
  }

  componentDidMount(){
    const that=this;
    this.props.dispatch({ type: 'home/getFunList', payload: {}});


  }


  getProductOnlyShow= (data)=>{
    this.props.dispatch({ type: 'home/getUrl', payload: {
      url:"http://my.huangye88.com/item/tool/",
    },callBack:(url)=>{
      window.open(url, '_blank');
    }});
  }

  render() {

    // In the fifth row, other columns are merged into first column
// by setting it's colSpan to be 0
const renderContent = (value, row, index) => {
  const obj = {
    children: value,
    props: {},
  };

  return obj;
};

const columns = [
  {
    title: '功能名称',
    dataIndex: 'name',
    render: renderContent,
    width: 100,
  },
  {
    title: '平台',
    dataIndex: 'oem_class',
    render: renderContent,
    width: 100,
  },
  // {
  //   title: '开通状态',
  //   dataIndex: 'states',
  //   render: renderContent,
  //   width: 100,
  // },

  {
    title: '到期时间',
    dataIndex: 'expireTime',
    render: renderContent,
  },

  {
    title: '操作',
    dataIndex: 'dailyPub',
    render: (text, record) => {
      const isEnable=record.isEnable;
      return   (<div> {<Row>
        <Button type="link" onClick={( )=>this.getProductOnlyShow()}>   {isEnable&&"续费"}
        {!isEnable&&"开通"} </Button>
        </Row>}

        </div>)
    },
    width: 150,
  },

];
 const {functuonList ,tuonList=[]}=this.props;
  console.log(functuonList)
    return (
      <div  style={{ background: '#fff', padding: 24  }}>
        <div className={styles.mask}></div>
        <div className={styles.modalDlg}>
          <div className={styles.titleName}>功能列表</div>
          <CloseOutlined  className={styles.closeIcon}   onClick={this.consoleCompany} />
          <Table columns={columns} dataSource={functuonList} bordered  pagination={{ position:'none'} }/>

        </div>
      </div>
   );
  }
}

export default connect(({home}) => ({...home
}))(ShopListForm);
