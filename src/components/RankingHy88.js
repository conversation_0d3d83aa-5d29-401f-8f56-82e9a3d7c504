import React, { Fragment } from 'react';
import RankStaistic from '@/components/RankStaistic';
import { Button, Col, Form, Pagination, Row, Select, Table } from 'antd';
import { connect } from 'dva';
import styles from './RankingHy88.less';

class RankingHy88 extends React.PureComponent {
  formRef = React.createRef();

  state = {
    eg: 0,
    currentPage: 1,
    pageSize: 10,
    total: 0,
    items: [],
    statics: {
      info_total: 0,
      rank_total: 0,
      baidu: 0,
      sougou: 0,
      shenma: 0,
      haosou: 0,
      toutiao: 0,
    },
  };

  componentDidMount() {
    this.loadData();
  }

  loadData = () => {
    const { eg, currentPage, pageSize } = this.state;

    this.props
      .dispatch({ type: 'ranking/getRankHy88', payload: { eg, currentPage, pageSize } })
      .then(data => {
        this.setState({
          items: data.data,
          total: data.pageinfos.total,
          statics: data.statics,
        });
      });
  };

  onPageChange = (currentPage, pageSize) => {
    this.setState(
      {
        currentPage,
        pageSize,
      },
      () => {
        this.loadData();
      },
    );
  };

  handleSubmit = values => {
    this.setState({ eg: values.eg }, () => {
      this.loadData();
    });
  };

  handleFailed = errorInfo => {
    console.log('Failed:', errorInfo);
  };

  render() {
    const engines = {
      BD: '百度',
      BDM: '百度手机',
      360: '360',
      SG: '搜狗',
      SGM: '搜狗手机',
      SM: '神马',
      TTM: '头条移动端',
      TT: '头条',
    };

    const columns = [
      {
        title: '关键词',
        dataIndex: 'keyword',
        key: 'keyword',
      },
      {
        title: '搜索引擎',
        dataIndex: 'engine',
        key: 'engine',
        render: text => engines[text],
      },
      {
        title: '位置',
        dataIndex: 'rank',
        key: 'rank',
        render: () => '第1页',
      },
      {
        title: '操作',
        key: 'operate',
        dataIndex: 'rank',
        render: (text, record) => (
          <a href={record.snap} target="_blank">
            查看快照
          </a>
        ),
      },
    ];

    const engineOptions = [
      {
        value: 0,
        label: '全部',
      },
      {
        value: 1,
        label: '百度',
      },
      {
        value: 2,
        label: '360',
      },
      {
        value: 3,
        label: '搜狗',
      },
      {
        value: 4,
        label: '神马',
      },
      {
        value: 5,
        label: '头条',
      },
    ];

    return (
      <Fragment>
        <RankStaistic statics={this.state.statics} />
        <Form
          ref={this.formRef}
          onFinish={this.handleSubmit}
          onFinishFailed={this.handleFailed}
          initialValues={{ eg: 0 }}
        >
          <Row>
            <Col span={6}>
              <Form.Item
                label={<span>搜索引擎</span>}
                labelCol={{ span: 6 }}
                wrapperCol={{ span: 18 }}
                name="eg"
              >
                <Select defaultValue={0} options={engineOptions} />
              </Form.Item>
            </Col>

            <Col span={6}>
              <Form.Item
                wrapperCol={{
                  offset: 1,
                  span: 16,
                }}
              >
                <Button type="primary" htmlType="submit">
                  查询
                </Button>
              </Form.Item>
            </Col>
            <Col span={6}></Col>
          </Row>
        </Form>
        {/* 排名查询组件 */}
        <Table pagination={false} columns={columns} dataSource={this.state.items} />
        {/* 关键词排名表格组件 */}
        <div className={styles.pagesline}>
          <Pagination
            total={this.state.total}
            showSizeChanger
            onChange={this.onPageChange}
            showQuickJumper
            showTotal={total => `总共 ${total} 条`}
          />
        </div>
      </Fragment>
    );
  }
}

// eslint-disable-next-line no-empty-pattern
export default connect(({}) => ({}))(RankingHy88);
