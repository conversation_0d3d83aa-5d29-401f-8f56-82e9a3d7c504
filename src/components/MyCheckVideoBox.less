/* 遮罩层 */

.borderDiv{
  height: 150px;
  width: 150px;
  padding: 5px;
}

.noadmin{

  height: 100%;
  width: 100%;

 display: flex;
align-items: center;
background:#D9D9D9;
  border:1px #7F7F7F solid;
  :global {
   .ant-checkbox-wrapper{
      white-space: nowrap;
      max-width: 180px;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .ant-checkbox-checked::after {
      transition: unset;
      animation:antCheckboxEffect 0 ease-in-out;
    }
    .ant-checkbox-checked .ant-checkbox-inner::after {
      transition: unset;
    }
    .ant-checkbox-inner::after {
      transition: unset;
    }
    .ant-checkbox-inner {
      transition: unset;
    }

    }
}

.CheckImgbox{
  position: absolute;
  bottom:5%;
  right:10%;
  z-index: 5;

}

.demo{
  width:  100%;
  height: 130px;
 line-height: 130px;
  display: flex;
  justify-content: center;
  align-items: center;
  background:#D9D9D9;
}
.imagePic{

  width: 100px;
    height: 100px;
    max-width: 100%;
    max-height: 130px;

    object-fit:cover;
  vertical-align: middle;

}
.imagePic {
  :global {
    .ant-image-img{
      max-height: 130px;
     }
    }


}
.eyesDelete{
  position: absolute;
  top:75px;
  left:45px;
  height: 60px;
  width: 60px;
  z-index: 5;
  pointer-events:none;
  color: #7F7F7F;
}
