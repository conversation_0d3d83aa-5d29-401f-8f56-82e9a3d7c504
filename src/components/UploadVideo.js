// @flow
import React, { PureComponent } from 'react';
import { connect } from 'dva';
import {
  Form,


  Select,

  Checkbox,
  Input,
  Modal,
  Layout,
  Image
} from 'antd';
import 'antd/dist/antd.css';
import { CloseOutlined, PlusOutlined, DeleteOutlined, EyeOutlined, UpCircleOutlined, DownCircleOutlined } from '@ant-design/icons';
const { TextArea } = Input;
import styles from './UploadVideo.less';
import MyVideoBox from "./MyVideoBox";

const { confirm } = Modal;

const { Option } = Select;
const CheckboxGroup = Checkbox.Group;

const { Header, Content } = Layout;


class upLoadVideo extends PureComponent {
  state = {
    newItemList: [],
    item: '',
    isUnfold: false,
  };


  onRemove = (item) => {
    const that = this;
    confirm({
      title: '删除视频',
      content: '确定删除这张视频?',
      zIndex: 9999,
      cancelText: '取消',
      okText: '确定',
      onOk() {
        that.props.onRemove(item);
        //  that.VirtualListRef.forceUpdate();

      },
      onCancel() { },
    });
  }
  onAddVideo = ()=>{
    this.props.onAddVideoClicked();
  }
  setUnfold = () => {
    this.setState({
      isUnfold: !this.state.isUnfold,
    })
  }

  render() {
    const { videoList=[], disabled } = this.props;
    const { isUnfold } = this.state;
    let isShowBtn = videoList.length > 5;

    return (
      <div style={{display:'flex',flexDirection:'row',}}>
      <div className={styles.picList}>
        <div className={styles.uploadBtn} disabled={disabled} onClick={this.onAddVideo}>
          <PlusOutlined />
          <div className="ant-upload-text" >上传</div>
        </div>

        {videoList.map((item, index) => {

          if (!isUnfold && index >= 6) {
            return;
          } else {
            return (
              <div className={styles.videoDiv}>
                <MyVideoBox
                  value={item.path}
                  index={index}
                  item={item}
                  disable={false}
                  onVideoRemove={(item) => this.onRemove(item)}
                >
                </MyVideoBox>
              </div>)
          }

        })}


      </div>
       </div>
    );
  }
}

export default connect(({ }) => ({
}))(upLoadVideo);
