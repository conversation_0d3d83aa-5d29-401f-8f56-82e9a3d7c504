import React from 'react';

import { Row, Col, Form, Select, Button } from 'antd';

// select
const handleChange = value => {
  console.log(`selected ${value}`);
};

class RankSelect extends React.PureComponent {
  render() {
    return (
      <Form>
        <Row>
          <Col span={6}>
            <Form.Item
              label={<span>搜索引擎</span>}
              labelCol={{ span: 6 }}
              wrapperCol={{ span: 18 }}
            >
              <Select
                defaultValue="全部"
                onChange={handleChange}
                options={[
                  {
                    value: '百度',
                    label: '百度',
                  },
                  {
                    value: '搜狗',
                    label: '搜狗',
                  },
                ]}
              />
            </Form.Item>
          </Col>
          {/* 第三方显示发布平台 */}
          <Col span={6}>
            <Form.Item
              label={<span>发布平台</span>}
              labelCol={{ span: 6 }}
              wrapperCol={{ span: 18 }}
            >
              <Select
                defaultValue="全部"
                onChange={handleChange}
                options={[
                  {
                    value: '黄页88',
                    label: '黄页88',
                  },
                  {
                    value: '酷易搜',
                    label: '酷易搜',
                  },
                ]}
              />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item
              wrapperCol={{
                offset: 1,
                span: 16,
              }}
            >
              <Button type="primary">查询</Button>
            </Form.Item>
          </Col>
          <Col span={6}></Col>
        </Row>
      </Form>
    );
  }
}
export default RankSelect;
