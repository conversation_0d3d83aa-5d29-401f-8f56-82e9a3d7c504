// @flow
import React, { PureComponent } from 'react';
import { connect } from 'dva';
import {
  Form,


  Select,

  Checkbox,
  Input,
  Modal,
  Layout,
  Image
} from 'antd';
import 'antd/dist/antd.css';
import { CloseOutlined, PlusOutlined, DeleteOutlined, EyeOutlined, UpCircleOutlined, DownCircleOutlined } from '@ant-design/icons';
const { TextArea } = Input;
import styles from './UploadPic.less';

const { confirm } = Modal;

const { Option } = Select;
const CheckboxGroup = Checkbox.Group;

const { Header, Content } = Layout;
 

class upLoadPic extends PureComponent {
  state = {
    newItemList: [],
    item: '',
    isUnfold: false,
  };


  onAddPic = () => {
    this.props.onAddPic();
  }
  onRemove = (item) => {
    const that = this;
    confirm({
      title: '删除图片',
      content: '确定删除这张图片?',
      zIndex: 9999,
      cancelText: '取消',
      okText: '确定',
      onOk() {
        that.props.onRemove(item);
        //  that.VirtualListRef.forceUpdate();

      },
      onCancel() { },
    });


  }
  setUnfold = () => {
    this.setState({
      isUnfold: !this.state.isUnfold,
    })
  }

  render() {
    const { picList, disabled } = this.props;
    const { isUnfold } = this.state;
    let isShowBtn = picList.length > 5;

    return (
      <div style={{display:'flex',flexDirection:'row',}}>
      <div className={styles.picList}>
        <div className={styles.uploadBtn} disabled={disabled} onClick={this.onAddPic}>
          <PlusOutlined />
          <div className="ant-upload-text" onClick={this.onAddPic}>上传</div>
        </div>
      
        {picList.map((item, index) => {

          if (!isUnfold && index >= 5) {
            return;
          } else {
            return (
              <div className={styles.imageDiv}>
 
                <Image
                  width={83}
                  height={83}
                  src={item}
                  placeholder
                />
 
                <EyeOutlined className={styles.eyesDelete} />
                <DeleteOutlined className={styles.iconDelete} onClick={(e) => { e.stopPropagation(); this.onRemove(item) }} />
              </div>)
          }

        })}
      
     
      </div>
       {isShowBtn && isUnfold &&<div className={styles.UnfoldDiv}> <img alt=""  src={require('../assets/up_pic.png')} onClick={this.setUnfold}  style={{ height:'45px',width:'45px' }} />   </div>}
       {isShowBtn && !isUnfold &&<div className={styles.UnfoldDiv}> <img alt=""  src={require('../assets/down_pic.png')} onClick={this.setUnfold}  style={{height:'45px',width:'45px' }}/>  </div>}
       </div>
    );
  }
}

export default connect(({ }) => ({
}))(upLoadPic);