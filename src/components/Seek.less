/* 遮罩层 */
.mask{
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  background: #000;
  z-index: 9000;
  opacity: 0.5;
}

/* 弹出层 */
.modalDlg{
  width: 80%;
  height: 90%;
  position: fixed;
  top: 20px;
  left: 0;
  right: 0;
  z-index: 9999;
  margin: 0 auto;
  background-color: #fff;
  border-radius:5px;
  display: flex;
  flex-direction: column;
  padding: 10px;
 
  :global {
  
    .ant-form-item-label{
        width: 80px;
    }
     
    }
}
.InfoList{
  display: flex;
  flex-direction:column;
  margin-left: 20px;
}
.InfoItem{
  display: flex;
  flex-direction:row;
  justify-content:space-between ;
  margin-top: 10px;
}
.itemTitle{
  font-size: 14px;
  color: #000000;
}
.itemValue{
  font-size: 14px;
  color: #F9976B;
  margin-left: 10px;
}
.InfoView{
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content:space-between ;
  font-size: 12px;
  margin: 10px 10px ;
  margin-top: 10px;
  padding-left: 10px;
  padding-right: 10px;
  color: #000000;
}
.itemView{
  height: 50px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  font-size: 12px;
  color: #ffffff;
  background-color: #1890FF;
  margin: 10px 10px ;
  margin-top: 20px;
  padding-left: 10px;
  padding-right: 10px;
}
.spanTitle{
  font-size: 18px;
}
  .titleName{
      font-size: 18px;
      color: #666666;
  }
.searchDiv{
  display: flex;
  flex-direction: row;
  width: 100vh
}
.closeIcon{
  position: fixed;
  top: 25px;
  right: 12%;
 font-size: 18px;
 color: #666666;
}
.iconView{
  height: 120px;
  width: 100px;
}
.tableView{
  width: 100%;
  height: 100%;
  overflow:scroll;
  overflow-x:hidden;
 
}
.lableView{
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: start;
  font-size: 16px;
  color: #000000;
  margin: 10px 10px ;
  margin-top: 20px;
}