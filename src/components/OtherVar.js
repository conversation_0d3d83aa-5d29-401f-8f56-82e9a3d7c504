import { MinusCircleOutlined } from '@ant-design/icons';
import { Button, Form, Input } from 'antd';
import React from 'react';

const OtherVar = () => {
  const onFinish = values => {
    console.log('Received values of form:', values);
  };
  return (
    <Form name="dynamic_form_nest_item" onFinish={onFinish} autoComplete="off">
      <Form.List name="users">
        {(fields, { add, remove }) => (
          <div>
            <Form.Item>
              <Button type="primary" onClick={() => add()}>
                添加变量组
              </Button>
            </Form.Item>
            {fields.map(({ key, name, ...restField }) => (
              <div
                key={key}
                style={{
                  marginBottom: 18,
                  display: 'block',
                }}
              >
                <Form.Item
                  {...restField}
                  name={[name, 'first']}
                  style={{
                    marginBottom: 5,
                  }}
                >
                  <Input placeholder="请输入变量" />
                </Form.Item>
                <Form.Item
                  {...restField}
                  name={[name, 'last']}
                  help="每个内容之间使用空白行分隔，每个内容不超过500字，当前共0个内容，最多可添加100个内容。"
                  style={{
                    marginBottom: 5,
                  }}
                >
                  <Input.TextArea rows={5} placeholder="请输入内容" />
                </Form.Item>
                <div style={{ display: 'block', height: '20px' }}>
                  <MinusCircleOutlined onClick={() => remove(name)} style={{ float: 'right' }} />
                </div>
              </div>
            ))}
          </div>
        )}
      </Form.List>
    </Form>
  );
};
export default OtherVar;
