import React, { PureComponent } from 'react';
import { message, Modal, Upload } from 'antd';
import { LoadingOutlined, PlusOutlined } from '@ant-design/icons';
import { connect } from 'dva';
import { baseUrl } from '../configs/config';

class UploadFile extends PureComponent {
  state = {
    isReady: false,
    previewVisible: false,
    loading: false,
    imgUrl: null,
    uploadingFile: null,
  }

  constructor(props) {
    super(props);
    this.state = {
      imgUrl: props.value || null,
    };
  }

  componentDidUpdate(prevProps) {
    if (prevProps.value !== this.props.value) {
      this.setState({ imgUrl: this.props.value });
    }
  }


  getBase64 = (img, callback) => {
    // eslint-disable-next-line no-undef
    const reader = new FileReader();
    reader.addEventListener('load', () => callback(reader.result));
    reader.readAsDataURL(img);
  };

  beforeUpload = file => {
    const isPDF = file.type === 'application/pdf';
    if (!isPDF) {
      message.error('合同上传格式必须为PDF格式');
    }
    const isLt2M = file.size / 1024 / 1024 < 8;
    if (!isLt2M) {
      message.error('合同大小必须小于 8MB!');
    }

    if (isPDF && isLt2M) {
      this.setState({
        isReady: true,
      });
    } else {
      this.setState({
        isReady: false,

      });
    }
    return isPDF && isLt2M;
  };

  handlePreview = async file => {
    if (!file.url && !file.preview) {
      // eslint-disable-next-line no-param-reassign
      file.preview = await this.getBase64(file.originFileObj, imageUrl => {
        this.setState({
          previewImage: imageUrl,
          previewVisible: true,
        });
      });
    } else {
      this.setState({
        previewImage: file.url || file.preview,
        previewVisible: true,
      });
    }
  };

  handleCancel = () => this.setState({ previewVisible: false });


  handleChange = info => {
    const { onChange } = this.props;
    console.log('handleChange', info);
    const { file } = info;
    if (!this.state.isReady) {
      this.setState({
        loading: false,
      });
      onChange('');
      return;
    }
    if (!file) {
      return;
    }
    if (file.status === 'uploading') {
      this.setState({ loading: true,uploadingFile: file });
    } else if (file.status === 'done') {
      const { url } = file.response.data[0];
      onChange(url);
      this.setState({
        loading: false,
        imgUrl: url,
        uploadingFile: null,
      });
    } else if (file.status === 'removed') {
      onChange('');
      this.setState({
        imgUrl: null,
        loading: false,
        uploadingFile: null,
      });
    }
  };

  render() {
    const { onChange, purpose, disabled } = this.props;
    const { previewVisible, previewImage, loading, imgUrl,uploadingFile } = this.state;
    console.log('imgUrl', imgUrl);
    const images = [];
    if (imgUrl) {
      images.push({ uid: '1', url: imgUrl });
    }
    if (uploadingFile) {
      images.push(uploadingFile);
    }


    const onRemove = () => {
      onChange('');
    };
    const Authorization = { Authorization: `Bearer ${window.sessionStorage.getItem('Authorization')}`, multiple: '' };
    const uploadButton = (
      <div>
        {!loading && <PlusOutlined />}
        {loading && <LoadingOutlined />}
        <div style={{ marginTop: 8 }}>上传</div>
      </div>
    );
    return (
      <div>
        <Upload
          name="handimage"
          listType="picture-card"
          className="avatar-uploader"
          headers={Authorization}
          data={file => ({ // data里存放的是接口的请求参数
            file, // file 是当前正在上传的图片
            purpose: { purpose },
          })}
          fileList={images}
          onPreview={this.handlePreview}
          action={`${baseUrl}/v2/album/upload/0?purpose=${purpose}`}
          beforeUpload={this.beforeUpload}
          onChange={this.handleChange}

          disabled={disabled}
          onRemove={onRemove}
        >
          {!imgUrl && uploadButton}
        </Upload>
      </div>

    );
  }
}


export default connect(() => ({
}))(UploadFile);
