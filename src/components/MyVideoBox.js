import React, { PureComponent } from 'react';
import { connect } from 'dva';
import {
    Checkbox,
    Image,
    Row,
    Col
} from 'antd';

import videojs from "video.js";

import "video.js/dist/video-js.css";
import styles from './MyVideoBox.less';
import { EditOutlined, DeleteOutlined, EyeOutlined } from '@ant-design/icons';

class MyVideoBox extends React.Component {

    constructor(props) {
        super(props);
        const { checked, disable } = props;
        this.state = {
            checked: checked,
            disable: disable,
            value: "",
        };
    }
  componentDidMount() {

    // instantiate Video.js
    let options = {
      controls: true,
    };
    this.player = videojs(this.videoNode, options, function onPlayerReady() {
      // this.preload = true;
      console.log("onPlayerReady", this);
    });

    // this.player.play();

  }

  // destroy player on unmount

  componentWillUnmount() {

    if (this.player) {

      this.player.dispose();

    }

  }

    componentWillReceiveProps(nextProps, nextContext) {
        const { checked, value } = nextProps;
        this.setState({
            checked: checked,
            value: value,
            disable: false
        })

    }
    shouldComponentUpdate(nextProps, nextState) {
        if (nextProps.checked == this.state.checked && nextProps.value == this.state.value) {
            return false;
        } else {
            return true;
        }
    }


    disableView = () => {
        this.setState({
            disable: true
        })
        this.forceUpdate();
    }

  onRemove = ()=>{
    const { item } = this.props;
      this.props.onVideoRemove(item);
  }
    //onerror="this.src=http://oss.huangye88.net/live/user/1530371/1533886544033995600-5.jpg;this.οnerrοr=null'"
    render() {
        const { name, id,item } = this.props;
        const { disable } = this.state;
        return (
            <div className={styles.borderDiv}   >
                <div className={styles.noadmin}>
                <div className={styles.demo}>
                    <video className="video-js"
                           width="135" height="85"
                           ref={(node) => (this.videoNode = node)}
                    >
                      <source src={this.props.value} ></source>
                    </video>
                    </div>


                  <DeleteOutlined className={styles.iconDelete}
                                  onClick={(e) => { e.stopPropagation(); this.onRemove(item) }} />

                </div>


            </div>
        );
    }
}
export default connect(({ }) => ({
}))(MyVideoBox);
