import React, { PureComponent } from 'react';
import { connect } from 'dva';
import {
    Checkbox,
    Image,
    Row,
    Col
} from 'antd';


import styles from './MyAlbumImgBox.less';
import { EditOutlined, DeleteOutlined, EyeOutlined } from '@ant-design/icons';

class MyCheckImgbox extends React.Component {

    constructor(props) {
        super(props);
        const { checked, disable } = props;
        this.state = {
            checked: checked,
            disable: disable,
            value: "",
        };
    }

    componentWillReceiveProps(nextProps, nextContext) {
        const { checked, value } = nextProps;
        this.setState({
            checked: checked,
            value: value,
            disable: false
        })

    }
    shouldComponentUpdate(nextProps, nextState) {
        if (nextProps.checked == this.state.checked && nextProps.value == this.state.value) {
            return false;
        } else {
            return true;
        }
    }
    ChangeName = () => {
        const { name, id } = this.props;
        console.log(id)
        this.props.ChangeName({ name, id });
    }
    delAlbum = () => {
        const {  id } = this.props;
        this.props.delAlbum({id });
    }
    disableView = () => {
        this.setState({
            disable: true
        })
        this.forceUpdate();
    }
    //onerror="this.src=http://oss.huangye88.net/live/user/1530371/1533886544033995600-5.jpg;this.οnerrοr=null'"
    render() {
        const { name, id } = this.props;
        const { disable } = this.state;
        return (
            <div className={styles.borderDiv}   >
                <div className={styles.noadmin}>
                <div className={styles.demo}>
                    <Image className={styles.imagePic} src={disable ? 'http://mentalroad-apk-release.oss-cn-shanghai.aliyuncs.com/error.png' : this.props.value} alt="avatar" onError={this.disableView} />
                    </div>
                   
                   
                    <EyeOutlined className={styles.eyesDelete} />


                </div>
                <Row wrap={false}>
                    <Col span={2} style={{ textAlign: 'center', lineHeight: '40px' }}>  <Checkbox
                        className={styles.CheckImgbox}
                        disabled={disable}
                        onChange={() => {
                            this.setState({ checked: !this.state.checked });
                            this.props.onChange(id);
                        }}
                        checked={this.state.checked}
                        value={this.props.value}
                    >
                    </Checkbox></Col>

                    <Col wrap={false} span={14} style={{ textAlign: 'center', lineHeight: '40px' }}>{name.length>5?name.substring(0,5)+"...":name} </Col>

                    <Col onClick={this.ChangeName} span={4} style={{ textAlign: 'center', lineHeight: '40px' }}><EditOutlined /></Col>

                    <Col onClick={this.delAlbum} span={4} style={{ textAlign: 'center', lineHeight: '40px' }}><DeleteOutlined /></Col>


                </Row>

            </div>
        );
    }
}
export default connect(({ }) => ({
}))(MyCheckImgbox);