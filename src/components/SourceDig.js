import React from 'react';
import { connect } from 'dva';
import { Modal, Input, Button, Table, Radio } from 'antd';

import styles from './SourceDig.less';

const { Search } = Input;

class SourceDig extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      open: false,
      kw: '',
      pagination: { current: 1, pageSize: 25, total: 0 },
      selected: [],
      chargeUrl: 'https://fafa.huangye88.com/dig/',
      searched: false,
      loading: false,
    };
  }

  componentDidMount() {
    const { list = [], pagination = {}, num = 0, chargeUrl, kw } = this.props;
    // 在 render 方法中使用 this.props 来初始化 DigMarias 组件对象
    const initialState = {
      list,
      kw,
      num,
      pagination,
      chargeUrl,
      selected: [],
      searched: false,
    };
    // 使用初始化数据来设置组件的初始状态
    this.state = { ...initialState };
  }

  doSave = () => {
    if (this.state.selected.length === 0) {
      Modal.warning({ title: '提示', content: '请选择素材', zIndex: 9999 });
      return;
    }
    let { single = true } = this.props;
    if (this.props.onSave) {
      this.props.onSave(
        single ? this.state.selected[0].data : this.state.selected.map(v => v.data),
      );
    }
    this.setState({ open: false });
  };

  didSearch = v => {
    this.doSearch({ kw: v, ...this.state.pagination });
  };

  doSearch = payload => {
    this.setState({ loading: true });
    this.props
      .dispatch({
        type: 'digMarias/getList',
        payload,
      })
      .then(result => {
        this.setState({ searched: true, ...result });
      })
      .catch(e => {
        if (e.msg === '额度已用完') {
          this.setState({ chargeUrl: e.data });
          Modal.confirm({
            content: '搜索次数已用完，请先购买搜索次数。',
            zIndex: 9999,
            cancelText: '取消',
            okText: '充值',
            onOk() {
              window.open(e.data, '_blank');
            },
            onCancel() {},
          });
        } else {
          Modal.warning({ title: '提示', content: e.msg, zIndex: 9999 });
        }
      })
      .finally(() => {
        this.setState({ loading: false });
      });
  };

  handleChangeKey = e => {
    this.setState({
      kw: e.target.value,
    });
  };

  pageChange = page => {
    this.doSearch({ kw: this.state.kw, ...this.state.pagination, current: page });
  };

  render() {
    const { open, pagination, list, kw, num } = this.state;
    const { single = true } = this.props;
    console.log('single:', single);
    const paginationProps = {
      ...pagination,
      showSizeChanger: false,
      showQuickJumper: true,
      pageSizeOptions: ['10', '20', '30', '100'],

      onChange: this.pageChange,
    };

    const rowSelection = {
      onChange: (selectedRowKeys, selectedRows) => {
        console.log(`selectedRowKeys: ${selectedRowKeys}`, 'selectedRows: ', selectedRows);
        this.setState({ selected: selectedRows });
      },
    };
    const columns = [
      {
        title: '序号',
        render: value => value.key + 1,
      },
      {
        title: '素材',
        render: value => value.data,
      },
    ];

    return (
      <div style={{ display: 'inline-block' }}>
        <Button type="primary" onClick={() => this.setState({ open: true })}>
          素材挖掘
        </Button>
        <Modal
          title="素材挖掘"
          centered
          visible={open}
          onOk={this.doSave}
          onCancel={() => this.setState({ open: false })}
          width={1000}
        >
          <div>
            <Search
              placeholder="请输入具体产品名称或服务项目"
              allowClear
              enterButton="搜索"
              size="large"
              onChange={this.handleChangeKey}
              value={kw}
              onSearch={this.didSearch}
              loading={this.state.loading}
            />
            {this.state.searched && (
              <p className={styles.tips}>
                还可以搜索{num}次，次数不够？点击
                <a href={this.state.chargeUrl} rel="noreferrer" target="_blank">
                  充值
                </a>
              </p>
            )}
            <div className={styles.sourcetip}>
              素材内容仅供参考，请根据产品选择相关的合法的描述使用。
            </div>
            <Table
              rowSelection={{
                type: single ? 'radio' : 'checkbox',
                ...rowSelection,
              }}
              bordered
              columns={columns}
              dataSource={list}
              pagination={paginationProps}
            />
          </div>
        </Modal>
      </div>
    );
  }
}

export default connect(state => ({
  ...state.digMarias,
}))(SourceDig);
