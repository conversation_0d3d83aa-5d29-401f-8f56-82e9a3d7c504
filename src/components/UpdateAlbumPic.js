// @flow
import React, { PureComponent } from 'react';
import { connect } from 'dva';
import {
  Form,
  Table,
  Image,
  Divider,
  Select,
  Row,
  Col,
  message,
  Button,
  Checkbox,
  Input,
  Modal,
  Layout,
  Upload
} from 'antd';
import 'antd/dist/antd.css';
import { CloseOutlined, CloudUploadOutlined } from '@ant-design/icons';
const { TextArea } = Input;
import styles from './UpdateAlbumPic.less';
import { baseUrl } from '../configs/config';
const { confirm } = Modal;
const { Option } = Select;
const CheckboxGroup = Checkbox.Group;

const { Header, Content } = Layout;


class UpdateAlbumPic extends PureComponent {
  state = {
    selAlbumsId: 0,
    albumsList: [],
    isShowTip: false,
    albumState:{}
  };
  componentDidMount() {
    const { value } = this.props;

    this.setState(
      {
        selAlbumsId: value
      }
    )
    const that = this;
    this.props.dispatch({
      type: 'picture/getAlbumsList', payload: {}, callBack: (albumsList) => {

        that.setState({
          albumsList: albumsList,
        })
      }
    });
    this.props.dispatch({
      type: 'picture/getAlbumState', payload: {}, callBack: (albumState) => {

        that.setState({
          albumState: albumState,
        })
      }
    });


  }



  consoleAlertInput = () => {
    this.props.close();
  }

  onChange = (e) => {
    this.setState({
      item: e.target.value
    })
  }

  handleChange = (value) => {
    const that = this;

    this.setState({
      selAlbumsId: value

    })

  }
  ShowTip = () => {
    window.open('http://www.huangye88.com/help/wentis_247.html', '_blank');
  }
  closeTip = () => {
    this.setState({
      isShowTip: false,
    })
  }
  openFeatures =()=>{


  }
  beforeUpload=(file)=>{
    const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png'|| file.type === 'image/jpg';
    if (!isJpgOrPng) {
      message.error({
        content: '图片上传格式必须为jpg/png',

        style: {
          zIndex: 9999,
        },
      });
    }
    const isLt2M = file.size / 1024 / 1024 < 1;
    if (!isLt2M) {

      message.error({
        content: '图片大小必须小于 1MB!',

        style: {
          zIndex: 9999,
        },
      });
    }
    return isJpgOrPng && isLt2M;
  }
  handleChangeQrcode = info => {
    const { selAlbumsId = 0 } = this.props;
    const { file } = info;
    if (file && file.status == 'done') {
      if (file && file.status == 'done') {
        if(file.response.data[0].msg=="该图片已存在"){

          message.error('图片重复，上传失败');
        }else{
          message.success('图片上传成功');

        }
      }
    }
    console.log(info)
    const that = this;
        if (file && file.status == 'done') {

    this.props.dispatch({
      type: 'picture/getAlbumsList', payload: {}, callBack: (albumsList) => {

        that.setState({
          albumsList: albumsList,
        })
      }
    });
    this.props.dispatch({
      type: 'picture/getAlbumState', payload: {}, callBack: (albumState) => {

        that.setState({
          albumState: albumState,
        })
      }
    });
    }
  }

  render() {
    const { selAlbumsId = 0, albumsList,albumState } = this.state;

    let selAlbumsName = "默认相册";
    if (selAlbumsId == 0) {
      if (albumsList.length > 0) {
        selAlbumsName = albumsList[0].name;
      }

    } else {
      if (albumsList.length > 0) {
        let item = albumsList.filter(item => item.id == selAlbumsId)[0];
        if (item) {
          selAlbumsName = item.name
        }

      }

    }
    const Authorization = { Authorization: `Bearer ${window.sessionStorage.getItem('Authorization')}`, multiple: '' };
    return (
      <div style={{ background: '#fff', padding: 24 }}>

        <div className={styles.mask} onClick={this.consoleAlertInput}></div>
        <div className={styles.modalDlg}>

          <CloseOutlined className={styles.closeIcon} onClick={this.consoleAlertInput} />
          <div className={styles.itemView}>上传图片</div>
          <div className={styles.itemView}>

            <div>
              选择相册：
              <Select
                defaultValue={selAlbumsName}
                style={{ width: 120 }}
                onChange={this.handleChange}
                getPopupContainer={triggerNode => triggerNode.parentNode}
              >
                {albumsList.map(item => (
                  <Option value={item.id}>{item.name}</Option>
                ))}

              </Select>
            </div>
            <div style={{ marginLeft: '10px' }}>
              <Upload
                name="product"
                multiple
                action={baseUrl + '/v2/album/upload/' + selAlbumsId}
                beforeUpload={this.beforeUpload}
                headers={Authorization}
                data={file => ({ // data里存放的是接口的请求参数
                  file: file, // file 是当前正在上传的图片

                })}
                showUploadList={false}
                className="avatar-uploader"
                onChange={this.handleChangeQrcode}
              >
                <Button>
                  <CloudUploadOutlined />
             上传
          </Button>
              </Upload>
            </div>



          </div>

          <div className={styles.itemViewTip}>图片要求：每张图片小于1M，格式为jpg,jpeg,png格式，尺寸建议为750*750</div>
          <div className={styles.itemViewTip}>温馨提示：为避免您上传的图片不符合使用要求，上传之前请先阅读<a style={{color:'blue'}} onClick={this.ShowTip}>图片规则</a></div>
          <div className={styles.itemView} style={{marginTop:'20px',fontWeight:'bolder'}}>图库容量</div>
          <div className={styles.itemView} style={{marginTop:'5px' }}>可上传图片数：<span style={{color:'red'}}>{albumState.can_uploaded}</span>张</div>
          <div className={styles.itemView} style={{marginTop:'5px' }}>图库总图片数：<span style={{color:'red'}}>{albumState.can_uploaded+albumState.uploaded_cnt}</span>张</div>
          <div className={styles.itemView} style={{marginTop:'5px' }}>已上传图片数：<span style={{color:'red'}}>{albumState.uploaded_cnt}</span>张</div>
          <div   style={{justifyContent:'center',marginTop:'20px',width:'100%',display:'flex'}}><Button type="primary" onClick={this.consoleAlertInput}>关闭</Button></div>

        </div>


      </div>
    );
  }
}

export default connect(({ picture }) => ({
  ...picture
}))(UpdateAlbumPic);
