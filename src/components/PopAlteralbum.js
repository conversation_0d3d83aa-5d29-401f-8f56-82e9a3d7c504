import { Button, Form, Input, message, Modal } from 'antd';
import React from 'react';
import { EditOutlined } from '@ant-design/icons';
import { connect } from 'dva';
import styles from '../pages/v1/publish/album/PhotoAlbum.less';

class PopAlterAlbum extends React.PureComponent {
  state = { loading: false, open: false };

  formRef = React.createRef();

  showModal = () => {
    this.setState({ open: true });
  };

  onFinish = () => {
    const form = this.formRef.current;
    const formData = form.getFieldsValue();
    // 处理表单的值
    if (formData.name === this.props.item.name) {
      return;
    }
    this.setState({ loading: true });
    this.props
      .dispatch({
        type: 'picture/updateAlbums',
        payload: { name: formData.name, is_open: '1', id: this.props.item.id },
      })
      .then(() => {
        this.setState({ loading: false, open: false });
        if (this.props.onSuccess) {
          this.props.onSuccess();
        }
      })
      .catch(err => {
        message.error(err.msg || err);
        this.setState({ loading: false });
      });
  };

  handleCancel = () => {
    this.setState({ open: false });
  };

  render() {
    const { open, loading } = this.state;
    const { item } = this.props;
    return (
      <div style={{ float: 'left' }}>
        <Button type="link" onClick={this.showModal}>
          <EditOutlined />
        </Button>
        <Modal
          title="修改相册"
          open={open}
          onOk={this.onFinish}
          confirmLoading={loading}
          onCancel={this.handleCancel}
        >
          <div className={styles.linebox}>
            <div className={styles.linebox_right}>
              <Form
                name="wrap"
                ref={this.formRef}
                labelCol={{ flex: '110px' }}
                labelAlign="left"
                labelWrap
                wrapperCol={{ flex: 1 }}
                colon={false}
                onFinish={this.onFinish}
                initialValues={{ name: item.name }} // 设置初始值
              >
                <Form.Item
                  label="相册名称："
                  name="name"
                  rules={[
                    { required: true, message: '请输入相册名字' },
                    { max: 8, message: '相册名称最多只能输入8个字符' },
                  ]}
                >
                  <Input autoFocus />
                </Form.Item>
              </Form>
            </div>
          </div>
        </Modal>
      </div>
    );
  }
}

export default connect(() => ({}))(PopAlterAlbum);
