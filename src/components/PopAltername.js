import { Button, Form, Input, message, Modal } from 'antd';
import React from 'react';
import { EditFilled } from '@ant-design/icons';
import { connect } from 'dva';
import styles from '../pages/v1/publish/album/PhotoAlbum.less';

class PopAltername extends React.PureComponent {
  state = { loading: false, open: false };

  formRef = React.createRef();

  showModal = () => {
    this.setState({ open: true });
  };

  handleCancel = () => {
    this.setState({ open: false });
  };

  onFinish = () => {
    const form = this.formRef.current;
    const formData = form.getFieldsValue();
    // 处理表单的值
    if (formData.name === this.props.item.name) {
      return;
    }
    this.setState({ loading: true });

    this.props
      .dispatch({
        type: 'picture/updatePicName',
        payload: { name: formData.name, id: this.props.item.id },
        callBack: () => {
          this.setState({ loading: false, open: false });
          if (this.props.onSuccess) {
            this.props.onSuccess();
          }
        },
      })
      .catch(err => {
        message.error(err.msg || err);
        this.setState({ loading: false });
      });
  };

  render() {
    const { open, loading } = this.state;
    const { item } = this.props;
    return (
      <div
        style={{ margin: '0px 10px 0px 0px', position: 'absolute', right: '-20px', bottom: '0px' }}
      >
        <Button type="link" onClick={this.showModal}>
          <EditFilled />
        </Button>
        <Modal
          title="修改图片名称"
          open={open}
          onOk={this.onFinish}
          confirmLoading={loading}
          onCancel={this.handleCancel}
        >
          <div className={styles.linebox}>
            <div className={styles.linebox_right}>
              <Form
                name="wrap"
                ref={this.formRef}
                labelCol={{ flex: '110px' }}
                labelAlign="left"
                labelWrap
                wrapperCol={{ flex: 1 }}
                colon={false}
                onFinish={this.onFinish}
                initialValues={{ name: item.name }} // 设置初始值
              >
                <Form.Item
                  label="图片名称："
                  name="name"
                  rules={[{ required: true, message: '请输入图片名称' }]}
                >
                  <Input />
                </Form.Item>
              </Form>
            </div>
          </div>
        </Modal>
      </div>
    );
  }
}

export default connect(() => ({}))(PopAltername);
