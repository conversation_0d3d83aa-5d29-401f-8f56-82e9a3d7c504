// @flow
import React, { PureComponent } from 'react';
import { connect } from 'dva';
import VirtualList from 'react-tiny-virtual-list';
import {
  Form,
  Table,
 
  Divider,
  Select,
  Row,
  Col,
  Button,
  Checkbox,
  Input,
  Modal,
  Layout,
  Spin
} from 'antd';
import 'antd/dist/antd.css';
 
import { CloseOutlined,DeleteOutlined,PlusCircleOutlined,EditOutlined } from '@ant-design/icons';
import MyCheckBox from './MyCheckBox';

import styles from './KeyWord.less';

const { confirm } = Modal;

const { Option } = Select;
const CheckboxGroup = Checkbox.Group;

const { Header, Content } = Layout;

const plainCityOptions = ['北京',
  '天津',
  '上海',
  '重庆',
  '香港',
  '澳门',
  '台湾',
  '常德',
  '长沙',
  '邵阳',
  '永州',
  '怀化',
  '益阳',
  '岳阳',
  '张家界',
  '株洲',
  '湘潭',
  '衡阳',
  '郴州',
  '娄底',
  '湘西',
  '洛阳',
  '焦作',
  '三门峡',
  '郑州',
  '新乡',
  '鹤壁',
  '安阳',
  '濮阳',
  '开封',
  '商丘',
  '许昌',
  '漯河',
  '平顶山',
  '南阳',
  '信阳',
  '周口',
  '驻马店',
  '石家庄',
  '张家口',
  '秦皇岛',
  '承德',
  '唐山',
  '廊坊',
  '保定',
  '沧州',
  '衡水',
  '邢台',
  '邯郸',
  '南京',
  '徐州',
  '连云港',
  '宿迁',
  '盐城',
  '扬州',
  '泰州',
  '南通',
  '镇江',
  '常州',
  '无锡',
  '苏州',
  '淮安',
  '南昌',
  '九江',
  '景德镇',
  '鹰潭',
  '新余',
  '萍乡',
  '赣州',
  '上饶',
  '抚州',
  '宜春',
  '吉安',
  '昆明',
  '曲靖',
  '玉溪',
  '丽江',
  '昭通',
  '临沧',
  '保山',
  '德宏',
  '怒江',
  '迪庆',
  '大理',
  '楚雄',
  '红河',
  '文山',
  '西双版纳',
  '普洱',
  '武汉',
  '十堰',
  '襄樊',
  '荆门',
  '孝感',
  '黄冈',
  '鄂州',
  '黄石',
  '咸宁',
  '荆州',
  '宜昌',
  '随州',
  '恩施',
  '广州',
  '深圳',
  '汕头',
  '珠海',
  '清远',
  '韶关',
  '河源',
  '梅州',
  '潮州',
  '揭阳',
  '汕尾',
  '惠州',
  '江门',
  '佛山',
  '肇庆',
  '云浮',
  '阳江',
  '茂名',
  '湛江',
  '东莞',
  '大同',
  '太原',
  '朔州',
  '阳泉',
  '长治',
  '晋城',
  '忻州',
  '吕梁',
  '晋中',
  '临汾',
  '运城',
  '沈阳',
  '朝阳',
  '阜新',
  '铁岭',
  '抚顺',
  '本溪',
  '辽阳',
  '鞍山',
  '丹东',
  '大连',
  '营口',
  '盘锦',
  '锦州',
  '葫芦岛',
  '长春',
  '白城',
  '松原',
  '吉林',
  '四平',
  '辽源',
  '通化',
  '白山',
  '延边',
  '杭州',
  '湖州',
  '嘉兴',
  '舟山',
  '宁波',
  '绍兴',
  '金华',
  '台州',
  '温州',
  '丽水',
  '衢州',
  '合肥',
  '宿州',
  '淮北',
  '阜阳',
  '蚌埠',
  '淮南',
  '滁州',
  '马鞍山',
  '芜湖',
  '铜陵',
  '安庆',
  '黄山',
  '六安',
  '巢湖',
  '池州',
  '宣城',
  '亳州',
  '福州',
  '南平',
  '三明',
  '莆田',
  '泉州',
  '厦门',
  '漳州',
  '龙岩',
  '宁德',
  '济南',
  '聊城',
  '德州',
  '东营',
  '淄博',
  '潍坊',
  '烟台',
  '威海',
  '青岛',
  '日照',
  '临沂',
  '枣庄',
  '济宁',
  '泰安',
  '莱芜',
  '滨州',
  '菏泽',
  '呼和浩特',
  '包头',
  '乌海',
  '赤峰',
  '呼伦贝尔',
  '兴安盟',
  '锡林郭勒盟',
  '乌兰察布',
  '巴彦淖尔',
  '阿拉善盟',
  '通辽',
  '鄂尔多斯',
  '哈尔滨',
  '齐齐哈尔',
  '黑河',
  '大庆',
  '伊春',
  '鹤岗',
  '佳木斯',
  '双鸭山',
  '七台河',
  '鸡西',
  '牡丹江',
  '绥化',
  '大兴安岭',
  '南宁',
  '桂林',
  '柳州',
  '梧州',
  '贵港',
  '玉林',
  '钦州',
  '北海',
  '防城港',
  '百色',
  '河池',
  '贺州',
  '来宾',
  '崇左',
  '海口',
  '三亚',
  '成都',
  '广元',
  '绵阳',
  '德阳',
  '南充',
  '广安',
  '遂宁',
  '内江',
  '乐山',
  '自贡',
  '泸州',
  '宜宾',
  '攀枝花',
  '巴中',
  '达州',
  '资阳',
  '眉山',
  '雅安',
  '阿坝',
  '甘孜',
  '凉山',
  '贵阳',
  '六盘水',
  '遵义',
  '毕节',
  '铜仁',
  '安顺',
  '黔东南',
  '黔南',
  '黔西南',
  '拉萨',
  '那曲',
  '昌都',
  '林芝',
  '山南',
  '日喀则',
  '阿里',
  '西安',
  '延安',
  '铜川',
  '咸阳',
  '渭南',
  '宝鸡',
  '汉中',
  '榆林',
  '安康',
  '商洛',
  '兰州',
  '金昌',
  '白银',
  '天水',
  '酒泉',
  '张掖',
  '武威',
  '庆阳',
  '平凉',
  '定西',
  '陇南',
  '临夏',
  '甘南',
  '西宁',
  '海东',
  '海北',
  '海南',
  '黄南',
  '果洛',
  '玉树',
  '海西',
  '银川',
  '石嘴山',
  '吴忠',
  '固原',
  '中卫',
  '乌鲁木齐',
  '克拉玛依',
  '喀什',
  '阿克苏',
  '和田',
  '吐鲁番',
  '哈密',
  '克孜勒苏',
  '博尔塔拉',
  '昌吉',
  '巴音郭楞',
  '伊犁哈萨克',
  '伊犁',
  '塔城',
  '阿勒泰',
  '醴陵',
  '鄢陵县',
  '济源',
  '中山',
  '大丰',
  '武夷山',
  '张北',
  '长葛',
  '诸城',
  '垦利',
  '和县',
  '霍邱',
  '桐城',
  '临猗',
  '清徐',
  '嘉峪关',
  '仙桃',
  '潜江',
  '神农架',
  '天门',
  '永新',
  '库尔勒',
  '阿拉尔',
  '石河子',
  '图木舒克',
  '五家渠',
  '迁安',
  '海拉尔',
  '思茅',
  '海南省直辖',
  '襄阳',
  '阿泰勒',
  '克孜勒苏柯尔克孜',
  '通辽市',
  '乌海市',
  '巴彦倬尔'];
const defaultCityCheckedList = [];

let beforeModifiers = ['电动', '二手', '供应', '国产', '环保', '进口', '迷你', '哪家', '全新', '热门', '特价', '微型', '现货', '销售', '小型', '新款', '优质', '原装', '正规', '正宗', '知名', '智能', '专业', '专用', '自动', '半自动', '便携式', '哪里有', '哪里买', '全自动', '专业的', '哪里有卖', '原装进口', '专业承接', '专业从事', '专业定做', '专业定制', '专业订制', '专业生产', '专业制造'];
let defaultCheckedBeforeModifier = [ ];

let afterModifiers = ['厂家直销', '专业快速', '原装现货', '优惠促销', '性价比最高', '特价批发', '总代直销', '批发代理', '量大从优', '价格实惠', '行业领先', '低价促销', '包邮正品', '哪家好', '哪家专业', '服务周到', '优质服务', '信誉保证', '哪家强', '哪家比较好', '放心省心', '不二之选', '安全可靠'];
let defaulCheckedAfterModifiers = [];

const Type_AddCity = 1;
const Type_EditCity = 2;
const Type_AddBeforeModifiers = 3;
const Type_EditBeforeModifiers = 4;
const Type_AddAfterModifiers = 5;
const Type_EditAfterModifiers = 6;

class RankingListForm extends PureComponent {
  state = {
    plainCityOptionsCity:plainCityOptions,
    cityCheckedListIndex: defaultCityCheckedList,
    indeterminateCity: true,
    checkAllCity: false,
    beforeModifierList:beforeModifiers,
    afterModifierList:afterModifiers,
    beforeModifiersCheckedList: defaultCheckedBeforeModifier,
    beforeModifiersIndeterminate: true,
    checkAllBeforeModifiers: false,

    afterModifiersCheckedList: defaulCheckedAfterModifiers,
    afterModifiersIndeterminate: true,
    checkAllAfterModifiers: false,

    modalTitle: "添加城市",
    visible: false, 
    type: Type_AddCity,
    inputValue: '产品名称',
    productName:"",
    keywordList: [],
    checkKeyType: ["3","13"],
    loading: true,
    brand:"",
    cityCheckedList:[]
  };
 
  componentDidMount(){
    const {productName,brand}=this.props;
    if(productName!=this.state.productName){
      this.setState({
        keywordList:[],
        productName:productName,
        brand:brand
      })
    }
    const that=this;
    
   
  }
  consoleKeyWord = () => {
    this.props.close();
  }

 
  onSaveBtn=()=>{
    const {keywordList:itemList}=this.props;
    const {keywordList } =this.state;
    if((keywordList.length+itemList.length)<200||(keywordList.length+itemList.length)>2000){
      Modal.error({
        content: '关键字必须在200~2000条之间',
        zIndex:9999
      });
      return;
    }

    this.props.onSaveBtn(keywordList);
    
  }
  onCityChange = (item,index) => {
    const {cityCheckedList,plainCityOptionsCity}=this.state;
    let checkedList=[];
    let newcheckedList=checkedList.concat(cityCheckedList);
    const itemIndex=cityCheckedList.indexOf(index);
    const isCheck=itemIndex !== -1;
    if(isCheck){
      newcheckedList.splice(itemIndex,1);
    }else{
      newcheckedList.push(index);
    }
    this.setState({
      cityCheckedList: newcheckedList,
      checkAllCity: newcheckedList.length === plainCityOptionsCity.length,
    });
  };

  onCheckAllCityChange = e => {
    const {plainCityOptionsCity}=this.state;
    let checkedList=[];
    for(let i=0;i<plainCityOptionsCity.length;i++){
      checkedList.push(i);
    }
    this.setState({
      cityCheckedList: e.target.checked ? checkedList : [],
      indeterminateCity: e.target.checked,
      checkAllCity: e.target.checked,
    });
  };


  onBeforeModifiersChange =  (item,index) => {
    const {beforeModifierList,beforeModifiersCheckedList}=this.state;
    let checkedList=[];
    let newcheckedList=checkedList.concat(beforeModifiersCheckedList);
    const itemIndex=beforeModifiersCheckedList.indexOf(item);
    const isCheck=itemIndex !== -1;
    if(isCheck){
      newcheckedList.splice(itemIndex,1);
    }else{
      newcheckedList.push(item);
    }
   
    this.setState({
      beforeModifiersCheckedList: newcheckedList,
      beforeModifiersIndeterminate: !!newcheckedList.length && newcheckedList.length < beforeModifierList.length,
      checkAllBeforeModifiers: newcheckedList.length === beforeModifierList.length,
    });
  };

  onCheckAllBeforeModifiers = e => {
     
    const {beforeModifierList}=this.state;
    this.setState({
      beforeModifiersCheckedList: e.target.checked ? beforeModifierList : [],
      beforeModifiersIndeterminate: false,
      checkAllBeforeModifiers: e.target.checked,
    });
  };

  onAfterModifiersChange =  (item,index) => {
 
    const {afterModifierList,afterModifiersCheckedList}=this.state;
    let checkedList=[];
    let newcheckedList=checkedList.concat(afterModifiersCheckedList);
    const itemIndex=afterModifiersCheckedList.indexOf(item);
    const isCheck=itemIndex !== -1;
    if(isCheck){
      newcheckedList.splice(itemIndex,1);
    }else{
      newcheckedList.push(item);
    }
   
    this.setState({
      afterModifiersCheckedList: newcheckedList,
      afterModifiersIndeterminate: !!newcheckedList.length && newcheckedList.length < afterModifierList.length,
      checkAllAfterModifiers: newcheckedList.length === afterModifierList.length,
    });
 
  };

  onCheckAllAfterModifiers = e => {
    const {afterModifierList}=this.state;
    this.setState({
      afterModifiersCheckedList: e.target.checked ? afterModifierList : [],
      afterModifiersIndeterminate: false,
      checkAllAfterModifiers: e.target.checked,
    });
  };


  hideModal = () => {
    this.setState({
      visible: false,
    });
  };

  okModal = () => {
  
    const { type, inputValue, itemIndex,plainCityOptionsCity,beforeModifierList,afterModifierList} = this.state;
    let keywordList=inputValue.split(/,|，|\r|\n|\r\n|\s+/);
    for(let i=0;i<keywordList.length;i++){
      
      if(keywordList[i].length>10 ){
        Modal.error({
          content: '字数必须在10字以内',
          zIndex:9999
        });
        return;
      }
    }
    this.setState({
      visible: false,
    });
    switch (type) {
      case Type_AddCity:   
          for(let i=0;i<keywordList.length;i++){
            if(plainCityOptionsCity.indexOf(keywordList[i])>-1){
              Modal.error({
                title:"已有该城市",
                content: keywordList[i],
                zIndex:9999
              });
              continue;
            }
            plainCityOptionsCity.push(keywordList[i]);
            this.setState({
              plainCityOptionsCity:plainCityOptionsCity,
            })
          }    
        break;
      case Type_EditCity:
          if(plainCityOptionsCity.indexOf(keywordList)>-1){
            Modal.error({
              title:"已有该城市",
              content: inputValue,
              zIndex:9999
            });
            return;
          }
        plainCityOptionsCity[itemIndex] = inputValue;
        this.VirtualListRef.forceUpdate();
         
        break;
      case Type_AddBeforeModifiers:
          
          for(let i=0;i<keywordList.length;i++){
            if(beforeModifierList.indexOf(keywordList[i])>-1){
              Modal.error({
                title:"已有该前置修饰词",
                content: keywordList[i],
                zIndex:9999
              });
              continue;
            }
            beforeModifierList.push(keywordList[i]);
            this.setState({
              beforeModifierList:beforeModifierList,
            })
          }
          

           
        break;
      case Type_EditBeforeModifiers:
          if(beforeModifierList.indexOf(keywordList)>-1){
            Modal.error({
              title:"已有该前置修饰词",
              content:inputValue,
              zIndex:9999
            });
            return;
          }
          beforeModifierList[itemIndex] = inputValue;
        break;
      case Type_AddAfterModifiers:
      
          for(let i=0;i<keywordList.length;i++){
            if(afterModifierList.indexOf(keywordList[i])>-1){
              Modal.error({
                title:"已有该后置修饰词",
                content: keywordList[i],
                zIndex:9999
              });
              continue;
            }
             afterModifierList.push(keywordList[i]);
            this.setState({
              afterModifierList:afterModifierList,
            })
          }
          
             
        break;
      case Type_EditAfterModifiers:
          if(afterModifierList.indexOf(keywordList)>-1){
            Modal.error({
              title:"已有该后置修饰词",
              content: inputValue,
              zIndex:9999
            });
            return;
          }
          afterModifierList[itemIndex] = inputValue;
        break;
    }
     
  };

  onChangeInput = e => {
    this.setState({
      inputValue: e.target.value,
      
    });
  }
  AddCityModal = () => {
    this.setState({
      visible: true,
      type: Type_AddCity,
      modalTitle: '添加城市',
      inputValue: ""
    });
  };
   removeBatch2=(arr,toDeleteIndexes)=>{
    toDeleteIndexes.sort((a,b)=> a-b)//按小到大排列

    for (var i=toDeleteIndexes.length-1 ;i>=0; i--){
        arr.splice(toDeleteIndexes[i],1);
    }
    return arr;
  }
  deleteSelCityModal = () => {
    const that = this;
    const {plainCityOptionsCity,cityCheckedList}=this.state;
    confirm({
      title: '删除城市',
      content: '确定删除选中城市?',
      zIndex: 9999,
      cancelText: '取消',
      okText: '确定',
      onOk() {
        let newList2=[];
      
  
        let newList=that.removeBatch2(plainCityOptionsCity,cityCheckedList)
      
        that.setState({
          plainCityOptionsCity:newList,
          cityCheckedList:[]
         });
        //  that.VirtualListRef.forceUpdate();
      
    
      },
      onCancel() { },
    });

  }

  editCityModal = (item, index) => {

    this.setState({
      visible: true,
      type: Type_EditCity,
      modalTitle: '修改城市名称',
      itemIndex: index,
      inputValue: item,
    });
  }

  
  deleteCityModal = (item, index) => {
    const that = this;
    const {plainCityOptionsCity,cityCheckedList}=this.state;
    confirm({
      title: '删除城市',
      content: '确定删除该城市?',
      zIndex: 9999,
      cancelText: '取消',
      okText: '确定',
      onOk() {
        plainCityOptionsCity.splice(index, 1);
        let list=[];
        let newList=list.concat(plainCityOptionsCity);
    
        cityCheckedList.sort((a,b)=> a-b)
  
        let checkIndex=cityCheckedList.indexOf(index);

        if(checkIndex!=-1){
          for(let i=checkIndex;i<cityCheckedList.length;i++){
            cityCheckedList[i]=cityCheckedList[i]-1;
          }
          cityCheckedList.splice(checkIndex, 1)
           let CheckList=[];
           let newCheckList=list.concat(cityCheckedList);
  
          that.setState({
            cityCheckedList:newCheckList
           });
        }
        that.setState({
          inputValue: that.state.inputValue + "a",
          plainCityOptionsCity:newList,
         });
        that.VirtualListRef.forceUpdate();
      },
      onCancel() { },
    });

  }
  AddBeforeModifiersModal = () => {
    this.setState({
      visible: true,
      type: Type_AddBeforeModifiers,
      modalTitle: '添加前置修饰词',
      inputValue: ""
    });
  };

  editBeforeModifiersModal = (item, index) => {

    this.setState({
      visible: true,
      type: Type_EditBeforeModifiers,
      modalTitle: '修改前置修饰词',
      itemIndex: index,
      inputValue: item,
    });
  }
  deleteBeforeModifiersModal = (item, index) => {
const {beforeModifierList,beforeModifiersCheckedList}=this.state;
    const that = this;
    confirm({
      title: '删除修饰词',
      content: '确定删除该修饰词?',
      zIndex: 9999,
      cancelText: '取消',
      okText: '确定',
      onOk() {
        beforeModifierList.splice(index, 1);
        let list=[];
        let newList=list.concat(beforeModifierList);
        let checkIndex=beforeModifiersCheckedList.indexOf(item)

      
        if(checkIndex!=-1){
          beforeModifiersCheckedList.splice(checkIndex,1);
        
        }
        that.setState({
          inputValue: that.state.inputValue + "a",
          beforeModifierList:newList,
     
        });

      },
      onCancel() { },
    });


  }

 
  AddAfterModifiersModal = () => {
    this.setState({
      visible: true,
      type: Type_AddAfterModifiers,
      modalTitle: '添加后置修饰词',
      inputValue: ""
    });
  };

  editAfterModifiersModal = (item, index) => {

    this.setState({
      visible: true,
      type: Type_EditAfterModifiers,
      modalTitle: '修改后置修饰词',
      itemIndex: index,
      inputValue: item
    });
  }
  removeBatch=(arr,toDeleteIndexes)=>{
    var result=[];
    for (var i=0;i<arr.length ; i++){
        var o  = arr[i];
        var needDelete = false;
        for (var j=0;j<toDeleteIndexes.length ; j++){
            if(o==toDeleteIndexes[j]){needDelete=true; break;}
        }
        if(!needDelete){
            result.push(arr[i]);
        }
    }
    return result;
  }
  
  deleteAfterModifiersModal = (item, index) => {
    const that = this;
    const {afterModifierList}=this.state;
    confirm({
      title: '删除修饰词',
      content: '确定删除该修饰词?',
      zIndex: 9999,
      cancelText: '取消',
      okText: '确定',
      onOk() {
     

        afterModifierList.splice(index, 1);
        that.setState({
          inputValue: that.state.inputValue + "a",
          afterModifierList:afterModifierList
        });
      },
      onCancel() { },
    });

  }

  deleteSelBeforeModifiersModal = ( ) => {

    const that = this;
    const {beforeModifiersCheckedList,beforeModifierList}=this.state;
    confirm({
      title: '删除修饰词',
      content: '确定删除选中的修饰词?',
      zIndex: 9999,
      cancelText: '取消',
      okText: '确定',
      onOk() {
     let newList=that.removeBatch(beforeModifierList,beforeModifiersCheckedList)
    
        that.setState({
          beforeModifierList:newList,
          beforeModifiersCheckedList:[],
          inputValue: that.state.inputValue + "a",
          checkAllBeforeModifiers:false
         });
        

      },
      onCancel() { },
    });


  }
  deleteSelAfterModifiersModal = () => {
    const that = this;
 
    const {afterModifiersCheckedList,afterModifierList}=this.state;
    confirm({
      title: '删除修饰词',
      content: '确定删除选中的修饰词?',
      zIndex: 9999,
      cancelText: '取消',
      okText: '确定',
      onOk() {
    
        let newList2=[];
      
  
        let newList=that.removeBatch(afterModifierList,afterModifiersCheckedList)
      
        that.setState({
          afterModifierList:newList,
          afterModifiersCheckedList:[],
          inputValue: that.state.inputValue + "a",
          checkAllAfterModifiers:false
         });
        

      },
      onCancel() { },
    });

  }
  clearKeyWord= () => {
    const that = this;
    const {keywordList}=this.state;
    confirm({
      title: '清空关键词',
      content: '确定清空所有关键词?',
      zIndex: 9999,
      cancelText: '取消',
      okText: '确定',
      onOk() {
       let list= [];
     
        that.setState({
          keywordList:list,
          inputValue: that.state.inputValue + "a"
        });
      },
      onCancel() { },
    });

  }
  deleteKeyWordItem = (item, index) => {
    const that = this;
    const {keywordList}=this.state;
    confirm({
      title: '删除关键词',
      content: '确定删除该关键词?',
      zIndex: 9999,
      cancelText: '取消',
      okText: '确定',
      onOk() {
        keywordList.splice(index, 1);
       let list= keywordList;
     
        that.setState({
          keywordList:list,
          inputValue: that.state.inputValue + "a"
        });

      },
      onCancel() { },
    });

  }
  onChange = (checkedValues) => {

    this.setState({
      checkKeyType: checkedValues
    })

  }
 

  generateKeyWord = () => {
    const { checkKeyType, productName, cityCheckedList, beforeModifiersCheckedList, afterModifiersCheckedList,plainCityOptionsCity,brand } = this.state;
    const {keywordList:itemList}=this.props;
    let keywordList = [];
     
    if (checkKeyType.length == 0) {
      Modal.warning({
        title: '提示',
        content: '请选择生成关键词类型',
        zIndex: 9999
      });
      return;
    }
    if (checkKeyType.indexOf('1') >= 0) {
      for (let i = 0; i < cityCheckedList.length; i++) {
        for (let j = 0; j < beforeModifiersCheckedList.length; j++) {
          for (let z = 0; z < afterModifiersCheckedList.length; z++) {
            if(keywordList.length>=2000){
              
              this.setState({
                keywordList
              })
              return;
            }
             let title =plainCityOptionsCity[cityCheckedList[i]] + beforeModifiersCheckedList[j] + productName + afterModifiersCheckedList[z];
            
             if(title.length<20){
              if(itemList.indexOf(title) == -1&&keywordList.indexOf(title)==-1){
                keywordList.push(title.trim());
              }else{
                Modal.error({
                  title:"关键词重复",
                  content: title,
                  zIndex:9999
                });
                return;
              }
             }else{
              Modal.error({
                title:"生成关键词字数超出限制",
                content: title,
                zIndex:9999
              });
              return;
             }
          
          }
        }
      }
    }
    if (checkKeyType.indexOf('2') !=-1) {
      for (let i = 0; i < cityCheckedList.length; i++) {
        for (let j = 0; j < beforeModifiersCheckedList.length; j++) {
          if(keywordList.length>=2000){
            this.setState({
              keywordList
            })
            return;
          }
        
          let title =plainCityOptionsCity[cityCheckedList[i]] + beforeModifiersCheckedList[j] + productName;
        
          if(title.length<20){
            if(itemList.indexOf(title) == -1&&keywordList.indexOf(title)==-1){
              keywordList.push(title.trim());
            }else{
              Modal.error({
                title:"关键词重复",
                content: title,
                zIndex:9999
              });
              return;
            }
          }else{
           Modal.error({
             title:"生成关键词字数超出限制",
             content: title,
             zIndex:9999
           });
           return;
          }
        }
      }
    }
    if (checkKeyType.indexOf('3') !=-1) {
      for (let i = 0; i < cityCheckedList.length; i++) {
        for (let z = 0; z < afterModifiersCheckedList.length; z++) {
          if(keywordList.length>=2000){
            this.setState({
              keywordList
            })
            return;
          }
          let title =plainCityOptionsCity[cityCheckedList[i]] + productName + afterModifiersCheckedList[z];
        
          if(title.length<20){
            if(itemList.indexOf(title) == -1&&keywordList.indexOf(title)==-1){
              keywordList.push(title.trim());
            }else{
              Modal.error({
                title:"关键词重复",
                content: title,
                zIndex:9999
              });
              return;
            }
          }else{
           Modal.error({
             title:"生成关键词字数超出限制",
             content: title,
             zIndex:9999
           });
           return;
          }
        }
      }
    }
    if (checkKeyType.indexOf('4') !=-1) {
      for (let j = 0; j < beforeModifiersCheckedList.length; j++) {
        for (let z = 0; z < afterModifiersCheckedList.length; z++) {
          if(keywordList.length>=2000){
            this.setState({
              keywordList
            })
            return;
          }
          let title =beforeModifiersCheckedList[j] + productName + afterModifiersCheckedList[z];
        
          if(title.length<20){
            if(itemList.indexOf(title) == -1&&keywordList.indexOf(title)==-1){
              keywordList.push(title.trim());
            }else{
              Modal.error({
                title:"关键词重复",
                content: title,
                zIndex:9999
              });
              return;
            }
          }else{
           Modal.error({
             title:"生成关键词字数超出限制",
             content: title,
             zIndex:9999
           });
           return;
          }
        }
      }
    }
    if (checkKeyType.indexOf('5') !=-1) {
      for (let i = 0; i < cityCheckedList.length; i++) {
        if(keywordList.length>=2000){
          this.setState({
            keywordList
          })
          return;
        }
        let title =plainCityOptionsCity[cityCheckedList[i]]+ productName;
      
        if(title.length<20){
          if(itemList.indexOf(title) == -1&&keywordList.indexOf(title)==-1){
            keywordList.push(title.trim());
          }else{
            Modal.error({
              title:"关键词重复",
              content: title,
              zIndex:9999
            });
            return;
          }
        }else{
         Modal.error({
           title:"生成关键词字数超出限制",
           content: title,
           zIndex:9999
         });
         return;
        }
      }
    }
    if (checkKeyType.indexOf('6') !=-1) {
      for (let j = 0; j < beforeModifiersCheckedList.length; j++) {
        if(keywordList.length>=2000){
          this.setState({
            keywordList
          })
          return;
        }
        let title =beforeModifiersCheckedList[j] + productName;
      
        if(title.length<20){
           
          if(itemList.indexOf(title) == -1&&keywordList.indexOf(title)==-1){
            keywordList.push(title.trim());
          }else{
            Modal.error({
              title:"关键词重复",
              content: title,
              zIndex:9999
            });
            return;
          }
        }else{
         Modal.error({
           title:"生成关键词字数超出限制",
           content: title,
           zIndex:9999
         });
         return;
        }
      }
    }
    if (checkKeyType.indexOf("7") !=-1) {
      
      for (let z = 0; z < afterModifiersCheckedList.length; z++) {
        if(keywordList.length>=2000){
          this.setState({
            keywordList
          })
          return;
        }
        let title =productName + afterModifiersCheckedList[z];
      
        if(title.length<20){
       
          if(itemList.indexOf(title) == -1&&keywordList.indexOf(title)==-1){
            keywordList.push(title );
          }else{
            Modal.error({
              title:"关键词重复",
              content: title,
              zIndex:9999
            });
            return;
          }
        }else{
         Modal.error({
           title:"生成关键词字数超出限制",
           content: title,
           zIndex:9999
         });
         return;
        }
      }
    }
    if (checkKeyType.indexOf("8") !=-1) {
        if(keywordList.length>=2000){
          this.setState({
            keywordList
          })
          return;
        }
        let title =brand+productName;
      
        if(title.length<20){
       
          if(itemList.indexOf(title) == -1&&keywordList.indexOf(title)==-1){
            keywordList.push(title );
          }else{
            Modal.error({
              title:"关键词重复",
              content: title,
              zIndex:9999
            });
            return;
          }
        }else{
         Modal.error({
           title:"生成关键词字数超出限制",
           content: title,
           zIndex:9999
         });
         return;
        }
      }
    if (checkKeyType.indexOf('9') >= 0) {
      for (let i = 0; i < cityCheckedList.length; i++) {
        for (let j = 0; j < beforeModifiersCheckedList.length; j++) {
          for (let z = 0; z < afterModifiersCheckedList.length; z++) {
            if(keywordList.length>=2000){
              
              this.setState({
                keywordList
              })
              return;
            }
             let title =plainCityOptionsCity[cityCheckedList[i]] + beforeModifiersCheckedList[j] +brand+productName + afterModifiersCheckedList[z];
            
             if(title.length<20){
              if(itemList.indexOf(title) == -1&&keywordList.indexOf(title)==-1){
                keywordList.push(title.trim());
              }else{
                Modal.error({
                  title:"关键词重复",
                  content: title,
                  zIndex:9999
                });
                return;
              }
             }else{
              Modal.error({
                title:"生成关键词字数超出限制",
                content: title,
                zIndex:9999
              });
              return;
             }
          
          }
        }
      }
    }
    if (checkKeyType.indexOf('10') !=-1) {
      for (let i = 0; i < cityCheckedList.length; i++) {
        for (let j = 0; j < beforeModifiersCheckedList.length; j++) {
          if(keywordList.length>=2000){
            this.setState({
              keywordList
            })
            return;
          }
        
          let title =plainCityOptionsCity[cityCheckedList[i]] + beforeModifiersCheckedList[j] + brand+productName;
        
          if(title.length<20){
            if(itemList.indexOf(title) == -1&&keywordList.indexOf(title)==-1){
              keywordList.push(title.trim());
            }else{
              Modal.error({
                title:"关键词重复",
                content: title,
                zIndex:9999
              });
              return;
            }
          }else{
           Modal.error({
             title:"生成关键词字数超出限制",
             content: title,
             zIndex:9999
           });
           return;
          }
        }
      }
    }
    if (checkKeyType.indexOf('11') !=-1) {
      for (let i = 0; i < cityCheckedList.length; i++) {
        for (let z = 0; z < afterModifiersCheckedList.length; z++) {
          if(keywordList.length>=2000){
            this.setState({
              keywordList
            })
            return;
          }
          let title =plainCityOptionsCity[cityCheckedList[i]] + brand+productName + afterModifiersCheckedList[z];
        
          if(title.length<20){
            if(itemList.indexOf(title) == -1&&keywordList.indexOf(title)==-1){
              keywordList.push(title.trim());
            }else{
              Modal.error({
                title:"关键词重复",
                content: title,
                zIndex:9999
              });
              return;
            }
          }else{
           Modal.error({
             title:"生成关键词字数超出限制",
             content: title,
             zIndex:9999
           });
           return;
          }
        }
      }
    }
    if (checkKeyType.indexOf('12') !=-1) {
      for (let j = 0; j < beforeModifiersCheckedList.length; j++) {
        for (let z = 0; z < afterModifiersCheckedList.length; z++) {
          if(keywordList.length>=2000){
            this.setState({
              keywordList
            })
            return;
          }
          let title =beforeModifiersCheckedList[j] + brand+productName + afterModifiersCheckedList[z];
        
          if(title.length<20){
            if(itemList.indexOf(title) == -1&&keywordList.indexOf(title)==-1){
              keywordList.push(title.trim());
            }else{
              Modal.error({
                title:"关键词重复",
                content: title,
                zIndex:9999
              });
              return;
            }
          }else{
           Modal.error({
             title:"生成关键词字数超出限制",
             content: title,
             zIndex:9999
           });
           return;
          }
        }
      }
    }
    if (checkKeyType.indexOf('13') !=-1) {
      for (let i = 0; i < cityCheckedList.length; i++) {
        if(keywordList.length>=2000){
          this.setState({
            keywordList
          })
          return;
        }
        let title =plainCityOptionsCity[cityCheckedList[i]]+ brand+productName;
      
        if(title.length<20){
          if(itemList.indexOf(title) == -1&&keywordList.indexOf(title)==-1){
            keywordList.push(title.trim());
          }else{
            Modal.error({
              title:"关键词重复",
              content: title,
              zIndex:9999
            });
            return;
          }
        }else{
         Modal.error({
           title:"生成关键词字数超出限制",
           content: title,
           zIndex:9999
         });
         return;
        }
      }
    }
    if (checkKeyType.indexOf('14') !=-1) {
      for (let j = 0; j < beforeModifiersCheckedList.length; j++) {
        if(keywordList.length>=2000){
          this.setState({
            keywordList
          })
          return;
        }
        let title =beforeModifiersCheckedList[j] + brand+productName;
      
        if(title.length<20){
           
          if(itemList.indexOf(title) == -1&&keywordList.indexOf(title)==-1){
            keywordList.push(title.trim());
          }else{
            Modal.error({
              title:"关键词重复",
              content: title,
              zIndex:9999
            });
            return;
          }
        }else{
         Modal.error({
           title:"生成关键词字数超出限制",
           content: title,
           zIndex:9999
         });
         return;
        }
      }
    }
    if (checkKeyType.indexOf("15") !=-1) {
      
      for (let z = 0; z < afterModifiersCheckedList.length; z++) {
        if(keywordList.length>=2000){
          this.setState({
            keywordList
          })
          return;
        }
        let title =brand+productName + afterModifiersCheckedList[z];
      
        if(title.length<20){
       
          if(itemList.indexOf(title) == -1&&keywordList.indexOf(title)==-1){
            keywordList.push(title );
          }else{
            Modal.error({
              title:"关键词重复",
              content: title,
              zIndex:9999
            });
            return;
          }
        }else{
         Modal.error({
           title:"生成关键词字数超出限制",
           content: title,
           zIndex:9999
         });
         return;
        }
      }
    }

    for (let i = 1; i < keywordList.length; i++) {
      const random = Math.floor(Math.random() * (i + 1));
      [keywordList[i], keywordList[random]] = [keywordList[random], keywordList[i]];
    }
  
    this.setState({
      keywordList
    })
  }
  render() {
 
    const { visible, modalTitle, inputValue, productName,brand, keywordList,checkKeyType,cityCheckedList,afterModifierList,plainCityOptionsCity,beforeModifierList } = this.state;
    return (
      <div style={{ background: '#fff', padding: 24  }}>
        <div className={styles.mask}  onClick={this.consoleKeyWord}></div>
        <div className={styles.modalDlg}>
        <CloseOutlined  className={styles.closeIcon}   onClick={this.consoleKeyWord}  />
 
          <div className={styles.itemView} >使用帮助：勾选需要的组合元素和生成的规则点击生成即可。需要最少生成200个，最多生成2000个关键词，每个关键词不得超过20个字</div>
          <div className={styles.itemView2} >例如：选择勾选了热门地区和前置修饰词，可选择生成规则：地区+前置+产品，地区+产品，前置+产品</div>
         
          <Row gutter={16} style={{marginTop:'10px',height:'200px'}}  >
            <Col span={6}  >
              <div>
                <div className={styles.ChexBoxTitle} >
                  <Checkbox
                    indeterminate={this.state.indeterminateCity}
                    onChange={this.onCheckAllCityChange}
                    checked={this.state.checkAllCity}
                  >
                    热门地区
                  </Checkbox>
                  <div>
                
                  <DeleteOutlined  className={styles.iconDelete}   onClick={this.deleteSelCityModal}  />
                 
                  <PlusCircleOutlined   style={{ fontSize: '18px' }} onClick={this.AddCityModal} />
                  </div>
                
                </div>

                <div className={styles.cityChexBox} style={{height:'160px'}}>
                  <div
                    style={{ width: '100%',height:'160px' }}
                  >
                    <VirtualList
                      width='100%'
                      height={160}
                     
                      itemCount={plainCityOptionsCity.length}
                      itemSize={20} // Also supports variable heights (array or function getter)

                      ref={(ref) => this.VirtualListRef = ref} 
                      renderItem={({index, style}) =>
                      {
                        const item=plainCityOptionsCity[index];
                        
                        return(<div key={index} style={style}>
                        <div className={styles.chexBoxChildItem}> 
                        <MyCheckBox 
                          checked={cityCheckedList.indexOf(index) !== -1}
                          isSelectAll={this.state.checkAllCity}
                          value= {item}
                          indeterminate={this.state.indeterminateCity}
                          index={index}
                          onChange={(item,index)=>this.onCityChange(item,index)}
                          >
                          </MyCheckBox>
                          <div>
                    
                            <EditOutlined   onClick={() => this.editCityModal(item, index)} />
                            <DeleteOutlined type="delete" className={styles.iconDelete} onClick={() => this.deleteCityModal(item, index)} />
                          </div>
                        </div> 
                        </div>)
                    
                      }}
                      />

                  </div>
                </div>
              </div>
            </Col>
            <Col span={6}>
              <div>
                <div className={styles.ChexBoxTitle}  >
                  <Checkbox
                    indeterminate={this.state.beforeModifiersIndeterminate}
                    onChange={this.onCheckAllBeforeModifiers}
                    checked={this.state.checkAllBeforeModifiers}
                  >
                    前置修饰词
          </Checkbox>
                  <div>
                  <DeleteOutlined className={styles.iconDelete} onClick={this.deleteSelBeforeModifiersModal} />
                  <PlusCircleOutlined  style={{ fontSize: '18px' }} onClick={this.AddBeforeModifiersModal} />
                  </div>
                
                 
                </div>

                <div className={styles.cityChexBox} style={{height:'160px'}}>
                  <CheckboxGroup
               
                    style={{ width: '100%', overflow: 'scroll', overflowX: 'hidden',height:'160px' }}
                    value={this.state.beforeModifiersCheckedList}
                   
                  >
                    {beforeModifierList.map((item, index) =>
                      <div className={styles.chexBoxChildItem}>
                        <Checkbox value={item} 
                         onChange={(e)=>this.onBeforeModifiersChange(item,index)}
                         >
                          {item}
                        </Checkbox>
                        <div>
                          <EditOutlined  onClick={() => this.editBeforeModifiersModal(item, index)} />
                          <DeleteOutlined  className={styles.iconDelete} onClick={() => this.deleteBeforeModifiersModal(item, index)} />
                        </div>
                      </div>
                    )}

                  </CheckboxGroup>
                </div>
              </div>
            </Col>
            <Col span={3}>
              <div>
                <div className={styles.ChexBoxTitle} >
                  品牌
                </div>
                <div className={styles.cityChexBox} style={{height:'160px'}}>
                  {brand}
                </div>
              </div>
            </Col>
            <Col span={3}>
              <div>
                <div className={styles.ChexBoxTitle} >
                  产品名称
                </div>
                <div className={styles.cityChexBox} style={{height:'160px'}}>
                  {productName}
                </div>
              </div>
            </Col>
            <Col span={6}>
              <div>
                <div className={styles.ChexBoxTitle} >
                  <Checkbox
                    indeterminate={this.state.afterModifiersIndeterminate}
                    onChange={this.onCheckAllAfterModifiers}
                    checked={this.state.checkAllAfterModifiers}
                  >
                    后置修饰词
          </Checkbox>
          <div>
                  <DeleteOutlined  className={styles.iconDelete} onClick={this.deleteSelAfterModifiersModal} />
                  <PlusCircleOutlined  style={{ fontSize: '18px' }} onClick={this.AddAfterModifiersModal} />
                  </div>
               
                </div>

                <div className={styles.cityChexBox} style={{height:'160px'}}>
                  <CheckboxGroup
                    style={{ width: '100%', overflow: 'scroll', overflowX: 'hidden',height:'160px' }}
                    value={this.state.afterModifiersCheckedList}
                 
                  >
                    {afterModifierList.map((item, index) =>
                      <div className={styles.chexBoxChildItem}>
                        <Checkbox value={item}
                         onChange={(e)=>{
                          this.onAfterModifiersChange(item,index)}}
                        >
                          {item}
                        </Checkbox>
                        <div>
                          <EditOutlined  onClick={() => this.editAfterModifiersModal(item, index)} />
                          <DeleteOutlined type="delete" className={styles.iconDelete} onClick={() => this.deleteAfterModifiersModal(item, index)} />
                        </div>
                      </div>
                    )}

                  </CheckboxGroup>
                </div>
              </div>
            </Col>


          </Row>

          <Modal
            title={modalTitle}
            visible={visible}
            onOk={this.okModal}
            onCancel={this.hideModal}
            okText="确认"
            cancelText="取消"
            zIndex={9999}
            wrapClassName='modalDlg'
          >
            <Input placeholder={inputValue}
              value={inputValue}
              onChange={this.onChangeInput}
            />
            <div>空格或中、英文逗号分隔</div>
          </Modal>
          <Checkbox.Group
          defaultValue={checkKeyType}
          style={{ width: '100%', marginTop: '5px' }} onChange={this.onChange}>
            规则：<Row>
              <Col span={6}>
                <Checkbox value="1">地区+前置+产品+后置</Checkbox>
              </Col>
              <Col span={6}>
                <Checkbox value="2">地区+前置+产品</Checkbox>
              </Col>
              <Col span={6}>
                <Checkbox value="3">地区+产品+后置</Checkbox>
              </Col>
              <Col span={6}>
                <Checkbox value="4">前置+产品+后置</Checkbox>
              </Col>
              <Col span={6}>
                <Checkbox value="5">地区+产品</Checkbox>
              </Col>
              <Col span={6}>
                <Checkbox value="6">前置+产品</Checkbox>
              </Col>
              <Col span={6}>
                <Checkbox value="7">产品+后置</Checkbox>
              </Col>
              <Col span={6}>
                <Checkbox value="8">品牌+产品</Checkbox>
              </Col>
              <Col span={6}>
                <Checkbox value="9">地区+前置+品牌+产品+后置</Checkbox>
              </Col>
              <Col span={6}>
                <Checkbox value="10">地区+前置+品牌+产品</Checkbox>
              </Col>
              <Col span={6}>
                <Checkbox value="11">地区+品牌+产品+后置</Checkbox>
              </Col>
              <Col span={6}>
                <Checkbox value="12">前置+品牌+产品+后置</Checkbox>
              </Col>
              <Col span={6}>
                <Checkbox value="13">地区+品牌+产品</Checkbox>
              </Col>
              <Col span={6}>
                <Checkbox value="14">前置+品牌+产品</Checkbox>
              </Col>
              <Col span={6}>
                <Checkbox value="15">品牌+产品+后置</Checkbox>
              </Col>
            </Row>
          </Checkbox.Group>

          <div style={{ color: '#000' }} className={styles.keyVoidView}>
            <Button type="primary" block className={styles.creatKeyBtn} onClick={this.generateKeyWord}>
              生成
          </Button>
          <div  className={styles.keywordResult}>
          <Row gutter={16}>
              
              {keywordList.map((item,index) => {
              return  <Col span={8}>
                <div  className={styles.keywordItem}>
                {item}
                <DeleteOutlined  className={styles.iconDelete} onClick={() => this.deleteKeyWordItem(item, index)} />
                         
              </div></Col>
            })}
          </Row>  
          </div>
          <div style={{marginTop:'10px'}}> 
          当前共计{keywordList.length}个关键词 
          <Button type="primary"  style={{marginLeft:'10px'}} onClick={this.clearKeyWord}>
              清空
          </Button>
          </div>
            <div style={{marginTop:'10px'}}>
              <Row gutter={16}>
                <Col span={12}>
                  <Button type="primary" block onClick={this.onSaveBtn}>
                    确定
              </Button>
                </Col>
                <Col span={12}>
                  <Button type="primary" block onClick={this.consoleKeyWord}>
                    取消
                </Button>
                </Col>
              </Row>
            </div>
          </div>
        </div>
      </div>
    );
  }
}

export default connect(({ }) => ({}))(RankingListForm);