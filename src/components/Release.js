// @flow
import React, { PureComponent } from 'react';
import { connect } from 'dva';
import { Layout, Menu, Breadcrumb,Row, Col,Card,Icon,message } from 'antd';
import 'antd/dist/antd.css';
import styles from './Release.less';
import { color } from '../utils/theme';

const { Header, Content } = Layout;


 class Release extends PureComponent {
  state = {
    url:"http://fabuxinxi.huangye88.com/",

  };
  componentDidMount(){
    const that=this;
  }

  showProduct=()=>{
    const {maxProducts=30,productSize}=this.props;
    this.props.dispatch({ type: 'product/save', payload: { 'productInfo': {} } });
    if(productSize<maxProducts){
      this.props.showProduct();
    }else{
      message.warn('超出添加产品上限，请联系管理员');
    }

  }
  showPhotoAlbum=()=>{
    this.props.showPhotoAlbum();
  }
  showManual=()=>{
    this.props.showManual();
  }
  showProductList=()=>{
    this.props.showProductList();
  }
  showHistoryList=()=>{
    this.props.showHistoryList();
  }
  showShopList=()=>{
    this.props.showShopList();
  }
  showRanking=()=>{
    // message.warn('该功能暂未开通');
    this.props.showRanking();
  }
  showSeek=()=>{
    // message.warn('该功能暂未开通');
    this.props.showSeek();
  }


  render() {

    return (
      <div style={{ background: '#fff', padding:'24px',minHeight: '100vh',color:'#000000' }}>
        <Row  gutter={16}>
        <Col span={12} style={{borderRight:'1px solid #B6B6B6'}}>
          <div style={{marginTop:'5px',marginBottom:'5px'}} >
          <div className={styles.titleView}>产品管理</div>

          <div className={styles.dottedLine}></div>
          <Row className={styles.childItem}  gutter={16}>
            <Col span={12}>
            <Card className={styles.CardView}  bordered bodyStyle={{ padding: 0 }} onClick={this.showProductList}>
                <div className={styles.itemView}>
                <img alt="" className={styles.iconView} src={require('../assets/productList.png')} />
              <div style={{marginLeft:'5px'}}>自动发布</div>
                </div>

            </Card>
            </Col>
            <Col span={12}>
            <Card className={styles.CardView}   bordered bodyStyle={{ padding: 0 }}  onClick={this.showManual}>
                <div className={styles.itemView}>
                <img alt="" className={styles.iconView} src={require('../assets/release.png')} />
              <div style={{marginLeft:'5px'}}>手动发布</div>
                </div>

            </Card>
            </Col>
          </Row>
          </div>
          <div style={{marginTop:'5px',marginBottom:'5px'}} >
          <div  className={styles.titleView}>内容管理</div>
          <div className={styles.dottedLine}></div>
          <Row className={styles.childItem}  gutter={16}>
            <Col span={12}>
              <Card className={styles.CardView}   bordered bodyStyle={{ padding: 0 }}  onClick={this.showPhotoAlbum}>
                <div className={styles.itemView}>
                <img alt="" className={styles.iconView} src={require('../assets/album.png')} />
              <div style={{marginLeft:'5px',color:'#66A0FF'}}>相册管理</div>
                </div>

            </Card>
            </Col>
            <Col span={12}>
            <Card className={styles.CardView}   bordered bodyStyle={{ padding: 0 }} onClick={this.showHistoryList}>
                <div className={styles.itemView}>
                <img alt="" className={styles.iconView} src={require('../assets/history.png')} />
              <div style={{marginLeft:'5px',color:'#66A0FF'}}>发布历史</div>
                </div>

            </Card>
            </Col>
          </Row>
          </div>

          <div style={{marginTop:'5px',marginBottom:'5px'}} >
          <div className={styles.titleView}>排名统计</div>
          <div className={styles.dottedLine}></div>
          <Row className={styles.childItem}  gutter={16}>

            <Col span={12}>
            <Card className={styles.CardView}  bordered bodyStyle={{ padding: 0 }} onClick={this.showRanking} >
                <div className={styles.itemView}>
                <img alt="" className={styles.iconView} src={require('../assets/rank.jpg')} />
              <div style={{marginLeft:'5px',color:'#FF5F92'}}>排名统计</div>
                </div>

            </Card>
            </Col>
          </Row>
          </div>

          <div style={{marginTop:'5px',marginBottom:'5px'}} >
          <div className={styles.titleView}>B2B商铺</div>
          <div className={styles.dottedLine}></div>
          <Row className={styles.childItem}  gutter={16}>
            <Col span={12}>
              <Card   className={styles.CardView} bordered bodyStyle={{ padding: 0 }}  onClick={this.showShopList}>
                <div className={styles.itemView}>
                <img alt="" className={styles.iconView} src={require('../assets/shop.png')} />
                <div style={{marginLeft:'5px',color:'#FFC000'}}>B2B商铺</div>
                </div>
            </Card>
            </Col>
            <Col span={12}>

            </Col>
          </Row>
          </div>


          </Col>
        <Col span={12}>
        <div style={{marginTop:'5px',marginBottom:'5px'}} >
          <div className={styles.tipTitle}>通知</div>
          <div style={{fontSize:'18px'}}>
               &ensp;&ensp;添加产品前请先阅读<a  onClick={() => {
                 window.open('http://www.huangye88.com/help/wentis_243.html', '_blank');
                }}  style={{color:'blue'}}>《禁售产品总则》</a>、<a onClick={() => {
                  window.open('http://www.huangye88.com/help/wentis_251.html', '_blank');
                 }}  style={{color:'blue'}}  >《發發助手产品详情发布标准》</a>、<a onClick={() => {
                  window.open('http://www.huangye88.com/help/fafazhushous_247.html', '_blank');
                 }} style={{color:'blue'}}>《發發助手图片规则示例》</a>，严禁发布禁售产品。
            <br/>
            &ensp;&ensp;请不要插入与产品不相关的素材，如跨行业新闻、小说等；请遵守广告法，不夸大、虚假宣传；不使用他人图片进行推广。
            <br/>
            &ensp;&ensp; 为保证您的推广效果，我们要求产品图片、关键词、素材内容需保持一致，否则将不会通过审核；同时我们还将对产品质量进行审核，请确保产品质量。
            <br/>
            &ensp;&ensp;發發助手是帮助客户解决B2B平台发布信息难的问题，客户编辑好产品详情后，系统即可生成信息并自动发布，可节省大量时间。目前已实现自动查询排名、自动发布信息、群发B2B平台等功能，后续我们还将支持更多功能，感谢您的使用！  <br/>

          </div>
        </div>
       </Col>
        </Row>

      </div>
   );
  }
}


export default connect(({home}) => ({...home}))(Release);
