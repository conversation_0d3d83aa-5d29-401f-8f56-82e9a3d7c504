import { Table, Radio } from 'antd';
import React from 'react';
import AddSource from '@/components/AddSource';

const columns = [
  {
    title: '序号',
    dataIndex: 'number',
    key: 'number',
  },
  {
    title: '素材内容',
    dataIndex: 'content',
    key: 'content',
  },
  {
    title: '操作',
    dataIndex: 'operate',
    key: 'operate',
    width: 200,
    render: (text, record) => <span>{record.status !== 2 && <AddSource />}</span>,
  },
];
const data = [
  {
    key: '1',
    number: '1',
    content: '素材内容素材内容素材内容素材内容',
    operate: '修改',
  },
];

const App = () => (
  <Table
    rowSelection={{
      type: Radio,
    }}
    pagination={false}
    bordered
    columns={columns}
    dataSource={data}
  />
);
export default App;
