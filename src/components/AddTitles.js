// @flow
import React, { PureComponent } from 'react';
import { connect } from 'dva';
import { Button, Checkbox, Radio, Col, Input, Layout, Modal, Row, Select, Spin } from 'antd';
import 'antd/dist/antd.css';
import styles from './AddTitles.less';
import DigKeyword from './DigKeyword';
import KeyWord from './KeyWord';
import { combineArray } from '../utils/autoKeyword';

const { Option } = Select;
const { confirm } = Modal;
const { TextArea } = Input;
const { Header, Content } = Layout;

class AddTitles extends PureComponent {
  state = {
    keywordList: [], // 关键词列表
    titleList: [], // 副标题列表
    itemList: [], // 自动生成 需要参考, 引用关键词和副标题
    customList: [], // 自定义标题
    alias: [], // 别名
    brand: '', // 只读
    optionTitleList: [], // 生成的标题
    methods: [],
    selectedSepSymbol: '',
    productName: '',
    // model 控制
    showDigKeywords: false,
    isShowKeyWord: false,
    keywordType: 'k',
    userId: 0,
    isSping: false,
    maxLen: 30,
  };

  sepSymbols = [
    { label: '无', value: '' },
    { label: ',', value: ',' },
    { label: '-', value: '-' },
    { label: '|', value: '|' },
  ];

  // 分隔符号
  calOptions = [
    { label: '关键词', value: 'k' },
    { label: '关键词+别名', value: 'k#a' },
    { label: '关键词+副标题', value: 'k#t' },
    { label: '别名+关键词', value: 'a#k' },
    { label: '副标题+关键词', value: 't#k' },
    { label: '关键词+别名+副标题', value: 'k#a#t' },
    { label: '关键词+副标题+别名', value: 'k#t#a' },
    { label: '别名+关键词+副标题', value: 'a#k#t' },
    { label: '别名+副标题+关键词', value: 'a#t#k' },
  ];

  componentDidMount() {
    const {
      optionTitleList,
      keywordList,
      titleList,
      alias,
      brand,
      productName,
      userId,
    } = this.props;
    console.log('AddTitles', this.props);
    this.sortKeywords(keywordList);
    this.sortKeywords(titleList);
    this.sortKeywords(alias);
    this.setState({
      optionTitleList,
      keywordList,
      titleList,
      alias,
      brand,
      productName,
      userId,
    });
  }

  // 生成标题
  productTitles() {}

  sortKeywords(keywords) {
    if (keywords) {
      keywords.sort((a, b) => b.length - a.length);
    }
  }

  showDigKeywordForKeyword = () => {
    this.showDigKeywordFor('k');
  };

  showDigKeywordForTitle = () => {
    this.showDigKeywordFor('t');
  };

  showDigKeywordFor = keywordType => {
    this.setState({
      showDigKeywords: true,
      keywordType,
    });
  };

  hideDigKeyword = () => {
    this.setState({
      showDigKeywords: false,
    });
  };

  onSaveFromDig = list => {
    switch (this.state.keywordType) {
      case 'k':
        {
          const keywordList = this.state.keywordList.concat(list);
          this.setState({
            keywordList,
          });
        }
        break;
      case 't':
        {
          const titleList = this.state.titleList.concat(list);
          this.setState({
            titleList,
          });
        }
        break;
    }
    // 不关闭关键词挖掘页面，用户可以继续选择关键词
  };

  showAutoKeyword = () => {
    this.showKeyWord('k', 20, this.state.keywordList);
  };

  showAutoTitle = () => {
    this.showKeyWord('t', 15, this.state.titleList);
  };

  showKeyWord = (keywordType, maxLen, itemList) => {
    this.setState({
      isShowKeyWord: true,
      keywordType,
      maxLen,
      itemList,
    });
  };

  hideKeyWord = () => {
    this.setState({
      isShowKeyWord: false,
    });
  };

  onKeywordListUpdated = keywordList => {
    switch (this.state.keywordType) {
      case 'k':
        this.setState({
          wordChange: true,
          isShowKeyWord: false,
          keywordList: this.state.keywordList.concat(keywordList),
        });
        break;
      case 't':
        this.setState({
          wordChange: true,
          isShowKeyWord: false,
          titleList: this.state.titleList.concat(keywordList),
        });
        break;
    }
  };

  onKeywordsChange = e => {
    const v = e.target.value;
    const newlist = v.split(/\r\n|\r|\n|\s+/);
    console.log(v, newlist);
    this.setState({
      keywordList: newlist,
    });
  };

  onTitleChange = e => {
    const v = e.target.value;
    const newlist = v.split(/\r\n|\r|\n|\s+/);
    this.setState({
      titleList: newlist,
    });
  };

  onAliasChange = e => {
    const v = e.target.value;
    const newlist = v.split(/\r\n|\r|\n|\s+/);

    this.setState({
      alias: newlist,
    });
  };

  onCustomChange = e => {
    const v = e.target.value;
    const newlist = v.split(/\r\n|\r|\n|\s+/);
    this.setState({
      customList: newlist,
    });
  };

  onClose = () => {
    this.props.close();
  };

  onSaveBtn = () => {
    // 生成标题会比较慢
    let { keywordList, titleList, alias, optionTitleList } = this.state;
    keywordList = keywordList.filter(item => item.length > 0);
    titleList = titleList.filter(item => item.length > 0);
    alias = alias.filter(item => item.length > 0);
    const errors = [];
    for (const item of keywordList) {
      const err = this.checkTitleError(item, 20);
      if (err != '' && errors.indexOf(`关键词${err}`) === -1) {
        errors.push(`关键词${err}`);
      }
    }
    for (const item of titleList) {
      const err = this.checkTitleError(item, 15);
      if (err != '' && errors.indexOf(`副标题${err}`) === -1) {
        errors.push(`副标题${err}`);
      }
    }
    for (const item of alias) {
      const err = this.checkTitleError(item, 15);
      if (err != '' && errors.indexOf(`别名${err}`) === -1) {
        errors.push(`别名${err}`);
      }
    }
    for (const item of optionTitleList) {
      const err = this.checkTitleError(item, 30);
      if (err != '' && errors.indexOf(`自定义标题${err}`) === -1) {
        errors.push(`自定义标题${err}`);
      }
    }
    const doSave = () => {
      const titles = this.genTitles();
      const mergeList = optionTitleList ? optionTitleList.concat(titles) : titles;
      const len1 = mergeList.length;
      let pureList = this.removeDuplicated(mergeList);
      const len2 = pureList.length;
      pureList = this.removeTooLarge(pureList, 30);
      const len3 = pureList.length;
      let tip = '';
      if (len1 !== len2) {
        tip = '部分标题重复，已去重';
      }
      if (len2 !== len3) {
        tip = '部分标题超过30个字已删除';
      }
      if (len3 > 2000) {
        tip = '标题最多添加2000个';
        pureList = pureList.slice(0, 2000);
      }

      if (tip !== '') {
        confirm({
          title: '提示',
          content: tip,
          zIndex: 9999,
          cancelText: '取消',
          okText: '确定',
          onOk: () => {
            this.props.onSaveBtn(keywordList, titleList, alias, pureList);
            this.props.close();
          },
          onCancel() {},
        });
      } else {
        this.props.onSaveBtn(keywordList, titleList, alias, pureList);
        this.props.close();
      }
    };

    if (errors.length > 0) {
      confirm({
        title: '提示',
        content: `${errors.join(',')},已去除`,
        zIndex: 9999,
        okText: '确定',
        onOk: () => {
          keywordList = keywordList.filter(item => this.checkTitleError(item, 20) === '');
          titleList = titleList.filter(item => this.checkTitleError(item, 15) === '');
          alias = alias.filter(item => this.checkTitleError(item, 15) === '');
          optionTitleList = optionTitleList.filter(item => this.checkTitleError(item, 30) === '');
          doSave();
        },
        onCancel() {},
      });
    } else {
      doSave();
    }
  };

  removeDuplicated = list => {
    const items = [];
    for (const i in list) {
      if (items.indexOf(list[i]) === -1) {
        items.push(list[i]);
      }
    }
    return items;
  };

  removeTooLarge = (list, limitSize) => {
    const items = [];
    for (const i in list) {
      if (list[i].length <= limitSize) {
        items.push(list[i]);
      }
    }
    return items;
  };

  genTitles = () => {
    const { keywordList, titleList, alias, customList, methods, selectedSepSymbol } = this.state;
    const filterCustomList = this.removeDuplicated(
      customList.filter(item => item.length > 0 && this.isTitleOk(item, 30)),
    );
    let resultList = [];
    const typemapping = {
      a: alias.filter(item => item.length > 0), // 别名
      k: keywordList.filter(item => item.length > 0), // 关键词
      t: titleList.filter(item => item.length > 0), // 副标题
    };
    for (const i in methods) {
      const types = methods[i].split('#');
      const arrs = [];
      let ok = true;
      for (const j in types) {
        const options = typemapping[types[j]];
        if (!options || options.length == 0) {
          ok = false;
          break;
        }
        arrs.push(options);
      }
      if (!ok) {
        continue;
      }
      let titles = [];
      titles = combineArray(arrs, selectedSepSymbol);

      for (const k in titles) {
        const title = titles[k];
        if (resultList.length >= 2000 - filterCustomList.length) {
          break;
        }
        if (resultList.indexOf(title) == -1 && filterCustomList.indexOf(title) == -1) {
          resultList.push(title.replace(/\s+/g, ''));
        }
      }
    }
    if (filterCustomList.length > 0) {
      resultList = resultList.concat(filterCustomList);
    }
    for (let i = 1; i < resultList.length; i++) {
      const random = Math.floor(Math.random() * (i + 1));
      [resultList[i], resultList[random]] = [resultList[random], resultList[i]];
    }
    return resultList;
  };

  deleteKeyWordItem = (item, index) => {
    const that = this;
    const { keywordList, Name } = this.state;
    const title = `删除${Name}`;
    const content = `确定删除该${Name}?`;
    confirm({
      title,
      content,
      zIndex: 9999,
      cancelText: '取消',
      okText: '确定',
      onOk() {
        keywordList.splice(index, 1);
        const list = keywordList;

        that.setState({
          keywordList: list,
          inputValue: `${that.state.inputValue}a`,
        });
      },
      onCancel() {},
    });
  };

  clearKeyWord = () => {
    const that = this;
    const { Name } = this.state;
    confirm({
      title: `清空${Name}`,
      content: `确定清空所有${Name}?`,
      zIndex: 9999,
      cancelText: '取消',
      okText: '确定',
      onOk() {
        const list = [];
        that.setState({
          keywordList: list,
          inputValue: `${that.state.inputValue}a`,
        });
      },
      onCancel() {},
    });
  };

  onChangeMethod = checkedValues => {
    this.setState({
      methods: checkedValues,
    });
  };

  onChangeSepSymbol = e => {
    this.setState({
      selectedSepSymbol: e.target.value,
    });
  };

  hasErr = (list, maxPerLen, maxLen) => {
    if (list.length > maxLen) {
      return true;
    }
    for (const i in list) {
      const item = list[i];
      if (!this.isTitleOk(item, maxPerLen)) {
        return true;
      }
    }
    return false;
  };

  isTitleOk = (title, maxPerLen) => {
    const numberCn = /^[0-9]+$/im;
    const letterCn = /^[A-Za-z]+$/im;
    if (title.length > maxPerLen) {
      return false;
    }
    if (numberCn.test(title) || letterCn.test(title)) {
      return false;
    }
    return true;
  };

  checkTitleError = (title, maxPerLen) => {
    const numberCn = /^[0-9]+$/im;
    const letterCn = /^[A-Za-z]+$/im;
    if (title.length > maxPerLen) {
      return `不能超过${maxPerLen}字`;
    }
    if (numberCn.test(title)) {
      return '不能为纯数字';
    }
    if (letterCn.test(title)) {
      return '不能为纯字母';
    }
    return '';
  };

  render() {
    const {
      keywordList,
      titleList,
      alias,
      customList,
      methods,
      selectedSepSymbol,
      userId,
      productName,
      isSping,
      maxLen,
      itemList,
    } = this.state;
    const keywordCtrl = {
      text: keywordList ? keywordList.join('\r\n') : '',
      num: keywordList.filter(item => item.length > 0).length,
      hasErr: this.hasErr(keywordList, 20, 2000),
    };
    const aliasCtrl = {
      text: alias ? alias.join('\r\n') : '',
      num: alias.filter(item => item.length > 0).length,
      hasErr: this.hasErr(alias, 15, 100),
    };
    const titleCtrl = {
      text: titleList ? titleList.join('\r\n') : '',
      num: titleList.filter(item => item.length > 0).length,
      hasErr: this.hasErr(titleList, 15, 2000),
    };
    const customCtrl = {
      text: customList ? customList.join('\r\n') : '',
      num: customList.filter(item => item.length > 0).length,
      hasErr: this.hasErr(customList, 30, 2000),
    };
    // const = keywordList?keywordList.join("\r\n"):"";
    return (
      <div style={{ background: '#fff', padding: 24 }}>
        <div className={styles.mask} onClick={this.onClose}></div>
        <div className={styles.modalDlg}>
          <div style={{ color: '#000' }} className={styles.keyVoidView}>
            <div className={styles.keywordResult}>
              <h1>添加标题</h1>
              <p>
                使用帮助:
                填写标题组合元素后，勾选需要标题组合方式即可自动生成标题。标题最多可生成2000个，每个标题不超过30个字。
              </p>
              <Row gutter={25} justify="space-evenly">
                <Col flex="1 1 0%">
                  <h2>
                    关键词<span>【{keywordCtrl.num}】</span>
                  </h2>
                  <TextArea
                    rows={12}
                    placeholder="例如“小型洒水车厂家”"
                    style={{
                      border: keywordCtrl.hasErr ? '1px solid red' : '1px solid #d3d3d3',
                      width: '100%',
                      overflow: 'scroll',
                      overflowX: 'hidden',
                    }}
                    onChange={this.onKeywordsChange}
                    value={keywordCtrl.text}
                  />
                  <span className={styles.tip}>
                    换行分隔，每个关键词不超过20个字，且不能是纯数字或纯字母，最多可添加2000个。
                  </span>
                  <div>
                    <Button
                      type="primary"
                      style={{ marginLeft: '10px' }}
                      onClick={this.showDigKeywordForKeyword}
                    >
                      关键词挖掘
                    </Button>
                    <Button
                      type="primary"
                      style={{ marginLeft: '10px' }}
                      onClick={this.showAutoKeyword}
                    >
                      自动生成
                    </Button>
                  </div>
                </Col>
                <Col flex="1 1 0%">
                  <h2>
                    副标题<span>【{titleCtrl.num}】</span>
                  </h2>
                  <TextArea
                    rows={12}
                    placeholder="例如“小型洒水车厂家”"
                    onChange={this.onTitleChange}
                    style={{
                      border: titleCtrl.hasErr ? '1px solid red' : '1px solid #d3d3d3',
                      width: '100%',
                      overflow: 'scroll',
                      overflowX: 'hidden',
                    }}
                    value={titleCtrl.text}
                  />
                  <span className={styles.tip}>
                    换行分隔，每个副标题不超过15个字，且不能是纯数字或纯字母，最多可添加2000个。
                  </span>
                  <div>
                    <Button
                      type="primary"
                      style={{ marginLeft: '10px' }}
                      onClick={this.showDigKeywordForTitle}
                    >
                      关键词挖掘
                    </Button>
                    <Button
                      type="primary"
                      style={{ marginLeft: '10px' }}
                      onClick={this.showAutoTitle}
                    >
                      自动生成
                    </Button>
                  </div>
                </Col>
                <Col flex="1 1 0%">
                  <h2>
                    别名<span>【{aliasCtrl.num}】</span>
                  </h2>
                  <TextArea
                    rows={12}
                    placeholder="例如“喷洒车”"
                    onChange={this.onAliasChange}
                    style={{
                      border: aliasCtrl.hasErr ? '1px solid red' : '1px solid #d3d3d3',
                      width: '100%',
                      overflow: 'scroll',
                      overflowX: 'hidden',
                    }}
                    value={aliasCtrl.text}
                  />
                  <span className={styles.tip}>
                    换行分隔，每个别名不超过15个字，且不能是纯数字或纯字母，最多可添加100个。
                  </span>
                </Col>
                <Col flex="1 1 0%">
                  <h2>
                    自定义标题<span>【{customCtrl.num}】</span>
                  </h2>
                  <TextArea
                    rows={12}
                    placeholder="例如“小型洒水车厂家”"
                    onChange={this.onCustomChange}
                    style={{
                      border: customCtrl.hasErr ? '1px solid red' : '1px solid #d3d3d3',
                      width: '100%',
                      overflow: 'scroll',
                      overflowX: 'hidden',
                    }}
                    value={customCtrl.text}
                  />
                  <span className={styles.tip}>
                    换行分隔，每个自定义标题不超过30个字，且不能是纯数字或纯字母，最多可添加2000个。
                  </span>
                </Col>
              </Row>
            </div>
            <div style={{ marginTop: '15px' }}>
              <Row>
                标题组合方式:
                <Checkbox.Group
                  value={methods}
                  name="checkboxgroup"
                  onChange={this.onChangeMethod}
                  options={this.calOptions}
                />
              </Row>
              <Row>
                分隔符号:
                <Radio.Group
                  value={selectedSepSymbol}
                  name="checkboxgroup"
                  onChange={this.onChangeSepSymbol}
                  options={this.sepSymbols}
                />
              </Row>
            </div>
            <div style={{ marginTop: '15px' }}>
              <Row gutter={16}>
                <Col span={12}>
                  <Button type="primary" block onClick={this.onSaveBtn}>
                    确定
                  </Button>
                </Col>
                <Col span={12}>
                  <Button type="primary" block onClick={this.onClose}>
                    取消
                  </Button>
                </Col>
              </Row>
            </div>
          </div>
          {isSping && (
            <div className={styles.spin}>
              <Spin size="large" spinning={isSping} />
            </div>
          )}
        </div>
        {this.state.showDigKeywords && (
          <DigKeyword close={this.hideDigKeyword} onSave={this.onSaveFromDig} />
        )}
        {this.state.isShowKeyWord && (
          <KeyWord
            maxLen={maxLen}
            productName={productName}
            userId={userId}
            close={this.hideKeyWord}
            keywordList={itemList}
            onSaveBtn={item => {
              this.onKeywordListUpdated(item);
            }}
          />
        )}
      </div>
    );
  }
}

export default connect(({}) => ({}))(AddTitles);
