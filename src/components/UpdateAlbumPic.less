/* 遮罩层 */
.mask{
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  background: #000;
  z-index: 13;
  opacity: 0.5;
}

/* 弹出层 */
.modalDlg{
  width: 50%;
 
  position: fixed;
  top: 60px;
  left: 0;
  right: 0;
  z-index: 13;
  margin: 0 auto;
  background-color: #fff;
  border-radius:0px;
  display: flex;
  flex-direction: column;
  padding: 10px;
 border: #000 2px;
 
} 
.imagePic{
  overflow: hidden;
  width: 80%;
  height: 80%;
  object-fit: cover;
  margin: 0 auto;
  align-items: center;
  position: fixed;
  top: 60px;
  left: 10%;
  z-index: 9000;
}
 .closeIcon{
  position: fixed;
  top: 25px;
  right: 12%;
 font-size: 18px;
 color: #666666;
}
.itemView{
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: start;
  font-size: 16px;
  color: #000000;
  margin-top: 10px;
  margin-left: 10px;
}
.itemViewTip{
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: start;
  font-size: 12px;
  color: #818181;
  margin-top: 10px;
  margin-left: 10px;
}
.itemView2{
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: start;
  font-size: 16px;
  color: #000000;
  margin-bottom: 10px;
  margin-left: 10px;
}
.buttomButton{
  display: flex;
  flex-direction: row;
  justify-content: center;
  margin: 10px 10px ;
  align-items: center;
}