import React from 'react';
import { Button, Form, Select, Upload, message, Modal } from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import { connect } from 'dva';
import styles from './PopUploadimg.less';
import { baseUrl } from '@/configs/config';

// 下拉筛选
const { Option } = Select;

class PopUploadimg extends React.PureComponent {
  state = { loading: false, open: false, albumState: {}, selectedId: 0 };


  componentDidMount() {
    const { id } = this.props;
    this.setState(
      {
        selectedId: id,
      },
    )
    this.refresh();
  }

  componentDidUpdate(prevProps) {
    if (prevProps.id !== this.props.id) {
      // 进行相应的更新操作
      // eslint-disable-next-line react/no-did-update-set-state
      this.setState({ selectedId: this.props.id }, ()=>{
        this.refresh();
      })
    }
  }

  showModal = () => {
    this.setState({ open: true });
  };

  handleChange = value => {
    this.setState({
      selectedId: value,
    })
  }

  onFinish = () => {
    this.setState({ loading: false, open: false });
  };

  handleCancel = () => {
    this.setState({ open: false });
  }


  beforeUpload = file => {
    const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png' || file.type === 'image/jpg';
    if (!isJpgOrPng) {
      message.error({
        content: '图片上传格式必须为jpg/png',

        style: {
          zIndex: 9999,
        },
      });
    }
    const isLt2M = file.size / 1024 / 1024 < 1;
    if (!isLt2M) {
      message.error({
        content: '图片大小必须小于 1MB!',

        style: {
          zIndex: 9999,
        },
      });
    }
    return isJpgOrPng && isLt2M;
  }

  onUploadChanged = info => {
    const { file } = info;
    if (file && file.status === 'done') {
      if (file.response.data[0].msg === '该图片已存在') {
        message.error('图片重复，上传失败');
        return;
      }
      message.success('图片上传成功');
    }

    console.log(info)
    if (file && file.status === 'done') {
      this.refresh();
    }
  }

  refresh = () => {
    this.props.dispatch({
      type: 'picture/getAlbumsList',
    });
    this.props.dispatch({
      type: 'picture/getAlbumState',
      payload: {},
      callBack: albumState => {
        this.setState({
          albumState,
        })
      },
    });
    if (this.props.onChanged) {
      this.props.onChanged();
    }
  }

  render() {
    const { open, loading, albumState, selectedId } = this.state;
    const { Albums } = this.props;
    let selectedName = '默认相册';
    if (selectedId === 0) {
      if (Albums.length > 0) {
        selectedName = Albums[0].name;
      }
    } else if (Albums.length > 0) {
      const item = Albums.filter(item => item.id === selectedId)[0];
      if (item) {
        selectedName = item.name
      }
    }
    const Authorization = { Authorization: `Bearer ${window.sessionStorage.getItem('Authorization')}`, multiple: '' };

    return (
      <>
        <Button type="primary" onClick={this.showModal}>
          上传图片
        </Button>
        <Modal
          title="上传图片"
          open={open}
          onOk={this.onFinish}
          confirmLoading={loading}
          onCancel={this.handleCancel}
        >
          <div className={styles.pop_content}>
            <div className={styles.linebox}>
              <span>选择相册：</span>
              <div className={styles.linebox_right}>
                <p className={styles.firstwrap}>
                  <Form>
                    <Form.Item>
                      <Select
                        onChange={this.handleChange}
                        defaultValue={selectedName}
                        allowClear>
                        {Albums.map(item => (
                          <Option value={item.id}>{item.name}</Option>
                        ))}
                      </Select>
                    </Form.Item>
                  </Form>
                  <Upload
                    name="product"
                    multiple
                    action={`${baseUrl}/v2/album/upload/${selectedId}`}
                    beforeUpload={this.beforeUpload}
                    headers={Authorization}
                    data={file => ({ // data里存放的是接口的请求参数
                      file, // file 是当前正在上传的图片

                    })}
                    showUploadList={false}
                    onChange={this.onUploadChanged}
                  >
                    <Button icon={<UploadOutlined/>}>上传图片</Button>
                  </Upload>
                </p>
                <p className={styles.secondwrap}>
                  图片要求:每张图片小于1M，格式为jpg.jpeg.png格式，尺寸建议为750*750
                  温馨提示:为避免您上传的图片不符合使用要求，上传之前请先阅读<a
                  href="http://www.huangye88.com/help/wentis_247.html" target="_blank">图片规则</a>
                </p>
              </div>
            </div>
            <div className={styles.linebox}>
              <span>图片容量：</span>
              <div className={styles.linebox_right}>
                <p className={styles.imgnumber}>
                  可上传图片数 <span>{albumState.can_uploaded}</span> 张
                </p>
                <p className={styles.imgnumber}>
                  已使用图片数 <span>{albumState.can_uploaded + albumState.uploaded_cnt}</span> 张
                </p>
                <p className={styles.imgnumber}>
                  图库总图片数 <span>{albumState.uploaded_cnt}</span> 张
                </p>
              </div>
            </div>
          </div>
        </Modal>
      </>
    );
  }
}


// eslint-disable-next-line no-empty-pattern
export default connect(({ picture }) => ({
  ...picture,
}))(PopUploadimg);
