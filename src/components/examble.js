// @flow
import React, { PureComponent } from 'react';
import { connect } from 'dva';
import { ipc<PERSON><PERSON><PERSON>} from 'electron';
import {
  Form,
  Table,
 
  Divider,
  Select,
  Row,
  Col,
  Button,
  AutoComplete,
  Input,
  Layout,
  Modal,
} from 'antd';
import 'antd/dist/antd.css';
import { CloseOutlined } from '@ant-design/icons';
import styles from './examble.less';
 
const { Option } = Select;
var conn;
 
 
 class Examble extends PureComponent {
  state = {
    confirmDirty: false,
    autoCompleteResult: [],
    keyword:'',
    pub_type:'',
  };

  consoleCompany=()=>{
    this.props.close();
  }
 
  componentDidMount(){
    const that=this;
    ipcRenderer.send('wsData')
    ipcRenderer.on('wsData',(event,data) => {
      that.loadLog(data)
    });
  }
 
  appendLog=(item)=> {
    var doScroll = log.scrollTop > log.scrollHeight - log.clientHeight - 1;
    log.appendChild(item);
    if (doScroll) {
        log.scrollTop = log.scrollHeight - log.clientHeight;
    }
}
  loadLog=(wsData)=>{
      console.log(wsData)
    // var msg = document.getElementById("msg");
    // var log = document.getElementById("log");
    // const that=this;
    if (window["WebSocket"]) {
        conn = new WebSocket(wsData);
        conn.onclose = function (evt) {
            // var item = document.createElement("div");
            // item.innerHTML = "<b>Connection closed.</b>";
            // that.appendLog(item);
            console.log("Connection closed.");
        };
        conn.onmessage = function (evt) {
            console.log(evt.data);
            var messages = evt.data.split('\n');
            for (var i = 0; i < messages.length; i++) {
                console.log(messages[i]);
                // var item = document.createElement("div");
                // item.innerText = messages[i];
                // that.appendLog(item);
            }
        };
    } else {
        console.log("Your browser does not support WebSockets")
    //     var item = document.createElement("div");
    //     item.innerHTML = "<b>Your browser does not support WebSockets.</b>";
    //    this.appendLog(item);
    }
  }

  runIndex=()=> {
      const {userId}=this.props;
      var msg = '{"action":"runIndex","params":{"uid":'+userId+'}}';
      if (!conn) {
        console.log("未连接")
          return false;
      }
      conn.send(msg);
  }
  stopIndex=()=> {
      var msg = '{"action":"stopIndex"}';
      if (!conn) {
        console.log("未连接")
          return false;
      }
      conn.send(msg);
  }
  pauseIndex=()=> {
      var msg = '{"action":"pauseIndex"}';
      if (!conn) {
        console.log("未连接")
          return false;
      }
      conn.send(msg);
  }
  resumeIndex=()=> {
      var msg = '{"action":"resumeIndex"}';
      if (!conn) {
        console.log("未连接")
          return false;
      }
      conn.send(msg);
  }

  runRank=()=> {
    const {userId}=this.props;
      var msg = '{"action":"runRank","params":{"uid":'+userId+'}}';
      if (!conn) {
        console.log("未连接")
          return false;
      }
      conn.send(msg);
  }
  pauseRank=()=> {
      var msg = '{"action":"pauseRank"}';
      if (!conn) {
        console.log("未连接")
          return false;
      }
      conn.send(msg);
  }
  resumeRank=()=> {
      var msg = '{"action":"resumeRank"}';
      if (!conn) {
        console.log("未连接")
          return false;
      }
      conn.send(msg);
  }
  stopRank=()=> {
      var msg = '{"action":"stopRank"}';
      if (!conn) {
        console.log("未连接")
          return false;
      }
      conn.send(msg);
  }

  registerBlockNotify=()=> {
      var msg = '{"action":"registerBlockNotify"}';
      if (!conn) {
        console.log("未连接")
          return false;
      }
      conn.send(msg);
  }

  setInterval=()=> {

      var msg = '{"action":"setInterval","params":{"interval":2000}}';
      if (!conn) {
        console.log("未连接")
          return false;
      }
      conn.send(msg);
  }


  render() {
  
    return (
      <div  style={{ background: '#fff', padding: 24  }}>
        <div className={styles.mask} onClick={this.consoleCompany}></div>
        <div className={styles.modalDlg}>
        <div className={styles.log} id="log"></div>
            <div className={styles.action} id = "action">
            <button  className={styles.btn} onClick={this.runIndex} >启动查收录</button>
                <button className={styles.btn} onClick={this.pauseIndex}>暂停查收录</button>
                <button className={styles.btn} onClick={this.resumeIndex}>恢复查收录</button>
                <button  className={styles.btn} onClick={this.stopIndex}>停止查收录</button>
            <button className={styles.btn} onClick={this.runRank}>开始查排名</button>
                <button className={styles.btn} onClick={this.pauseRank}>暂停查排名</button>
                <button className={styles.btn} onClick={this.resumeRank}>恢复查排名</button>
                <button className={styles.btn} onClick={this.stopRank}>停止查排名</button>
            <button className={styles.btn} onClick={this.setInterval}>设置请求间隔时间</button>
            <button className={styles.btn} onClick={this.registerBlockNotify}>注册搜索引擎封禁通知。注册才给消息</button>
            </div>
        </div>
      </div>
   );
  }
}
 
export default connect(({rancking,home}) => ({...rancking,...home
}))( Examble);