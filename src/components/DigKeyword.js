import React, { PureComponent } from 'react';
import { connect } from 'dva';
// import zhCn from 'antd/lib/locale-provider/zh_CN';
// import { ConfigProvider, Table, Input } from 'antd';
import { CloseOutlined } from '@ant-design/icons';
import { Button, Col, ConfigProvider, Input, Row, Table, Modal, notification, Space } from 'antd';
import zhCN from 'antd/lib/locale-provider/zh_CN';
import styles from './DigKeyword.less';

const { confirm } = Modal;

class DigKeyword extends PureComponent {
  state = {
    list: [],
    kw: '',
    num: 0,
    pagination: { current: 1, pageSize: 50, total: 0 },
    selected: [],
    selectedRowKeys: [],
    chargeUrl: 'https://fafa.huangye88.com/dig/',
    searched: false,
    savedKeywords: [], // 已保存的关键词，用于置灰显示
  };
  componentDidMount() {}
  onSaveBtn = () => {
    const selectedKeywords = this.state.selected.map(item => item.data);
    this.props.onSave(selectedKeywords);
    Modal.success({ title: '提示', content: '添加成功', zIndex: 9999 });

    // 将已保存的关键词添加到savedKeywords数组中，用于置灰显示
    const newSavedKeywords = [...this.state.savedKeywords, ...selectedKeywords];
    this.setState({
      selected: [],
      selectedRowKeys: [],
      savedKeywords: newSavedKeywords,
    });
    // 不关闭页面，用户可以继续选择关键词
  };

  didSearch = () => {
    this.doSearch({ kw: this.state.kw, ...this.state.pagination });
  };
  selectRow = record => {
    // 检查是否是已保存的关键词，如果是则不允许选择
    if (this.state.savedKeywords.includes(record.data)) {
      return;
    }

    const selectedRows = [...this.state.selected];
    const selectedRowKeys = [...this.state.selectedRowKeys];
    let found = false;

    for (let i in selectedRows) {
      if (selectedRows[i].key === record.key) {
        selectedRows.splice(i, 1);
        selectedRowKeys.splice(i, 1);
        found = true;
        break;
      }
    }
    if (!found) {
      selectedRows.push(record);
      selectedRowKeys.push(record.key);
    }
    this.setState({ selected: selectedRows, selectedRowKeys });
  };
  doSearch = payload => {
    this.props
      .dispatch({
        type: 'digKeywords/getKeywordList',
        payload,
      })
      .then(({ list, pagination, kw, num, chargeUrl }) => {
        this.setState({ searched: true, list, kw, num, chargeUrl, pagination });
      })
      .catch(e => {
        if (e.msg === '额度已用完') {
          this.setState({ chargeUrl: e.data });
          confirm({
            content: '搜索次数已用完，请先购买搜索次数。',
            zIndex: 9999,
            cancelText: '取消',
            okText: '充值',
            onOk() {
              window.open(e.data, '_blank');
            },
            onCancel() {},
          });
        } else {
          Modal.warning({ title: '提示', content: e.msg || e.message, zIndex: 9999 });
        }
      });
  };

  notifyClose = () => {
    this.props.close();
  };

  handleChangeKey = e => {
    this.setState({
      kw: e.target.value,
    });
  };

  pageChange = page => {
    this.doSearch({ kw: this.state.kw, ...this.state.pagination, current: page });
  };

  render() {
    const { list = [], pagination = {}, num = 0, kw = '', selectedRowKeys } = this.state;
    // if (kw !== '' && !this.state.searched) {
    //   this.setState({ kw, searched: true });
    // }
    const paginationProps = {
      ...pagination,
      showSizeChanger: false,
      showQuickJumper: true,
      hideOnSinglePage: true,
      pageSizeOptions: ['10', '20', '30', '100'],
      onChange: this.pageChange,
    };
    const rowSelection = {
      selectedRowKeys,
      onChange: (selectedRowKeys, selectedRows) => {
        debugger;
        console.log(`selectedRowKeys: ${selectedRowKeys}`, 'selectedRows: ', selectedRows);
        this.setState({ selected: selectedRows, selectedRowKeys });
      },
      getCheckboxProps: record => ({
        disabled: this.state.savedKeywords.includes(record.data), // 已保存的关键词禁用选择
      }),
    };
    const columns = [
      {
        title: '序号',
        width: '10%',
        render: value => {
          return value.key + 1;
        },
      },
      {
        title: '关键词',
        width: '90%',
        render: value => {
          return value.data;
        },
      },
    ];

    return (
      <div>
        <div style={{ background: '#fff', padding: 24 }}>
          <div className={styles.mask} />
          <div className={styles.modalDlg}>
            <div className={styles.titleName}>关键词挖掘</div>
            <CloseOutlined className={styles.closeIcon} onClick={this.notifyClose} />
            <Row style={{ padding: '10px 0px' }}>
              <Col span={22}>
                <Input
                  placeholder="请输入具体产品名称或服务项目"
                  value={this.state.kw}
                  style={{ width: '100%' }}
                  onChange={this.handleChangeKey}
                  size="large"
                />
              </Col>
              <Col span={2}>
                <Button type="primary" size="large" onClick={this.didSearch}>
                  搜索
                </Button>
              </Col>
            </Row>
            {this.state.searched && (
              <Row className={styles.searchtip}>
                今日还可搜索<span>{num}</span>次,次数不够?点击
                <Button
                  type="link"
                  onClick={() => {
                    window.open(this.state.chargeUrl, '_blank');
                  }}
                >
                  充值
                </Button>
              </Row>
            )}
            <div className={styles.tableView}>
              <ConfigProvider locale={zhCN}>
                <Table
                  rowSelection={{
                    type: 'checkbox',
                    ...rowSelection,
                  }}
                  columns={columns}
                  dataSource={list}
                  bordered
                  pagination={paginationProps}
                  onRow={record => ({
                    onClick: () => {
                      // 如果是已保存的关键词，不响应点击
                      if (!this.state.savedKeywords.includes(record.data)) {
                        this.selectRow(record);
                      }
                    },
                    style: this.state.savedKeywords.includes(record.data)
                      ? {
                          backgroundColor: '#f5f5f5',
                          color: '#ccc',
                          cursor: 'not-allowed',
                        }
                      : {},
                  })}
                />
              </ConfigProvider>
            </div>

            <div style={{ marginTop: '10px' }}>
              <Row gutter={16} justify="end">
                <Col span={24} style={{ textAlign: 'right' }}>
                  <Space>
                    <Button onClick={this.notifyClose}>取消</Button>
                    <Button type="primary" onClick={this.onSaveBtn}>
                      确定
                    </Button>
                  </Space>
                </Col>
              </Row>
            </div>
          </div>
        </div>
      </div>
    );
  }
}

export default connect(state => ({
  ...state.digKeywords,
}))(DigKeyword);
