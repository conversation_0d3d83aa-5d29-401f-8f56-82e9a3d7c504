// @flow
import React, { PureComponent } from 'react';
import { connect } from 'dva';
import {
  Form,
  Table,

  Divider,
  Select,
  Row,
  Col,
  Button,
  AutoComplete,
  Input,
  Upload,
  Layout,
  message,
  Modal,
  Pagination
} from 'antd';

import { CloseOutlined, CloudUploadOutlined } from '@ant-design/icons';
import 'antd/dist/antd.css';
import styles from './PhotoWall.less';
import MyCheckVideoBox from './MyCheckVideoBox';

import { baseUrl } from '../configs/config';
const { Option } = Select;

const { Header, Content } = Layout;
const columnCount = 5;
const pageSize = 30;
class VideoWall extends PureComponent {
  state = {
    selectedVideos: [], // 已选择的视频
    videos: [], // 所有视频
    changed: false,
    cur:1,
    pagination:{
      next:2,
      pre:1,
      hasNext:false,
      hasPre:false,
      items:[],
    }
  };
  componentDidMount() {
    const that = this;
    const {changed }=this.state;
    that.props.dispatch({
      type: 'video/getVideos', payload: { offset:0,limit:200 }
    }).then(items=>{
      let pagination = that.state.pagination;
      pagination.items = items.slice(0,pageSize);
      if (items.length>pageSize) {
        pagination.hasNext = true;
      }
      that.setState({videos:items, pagination});
    }).catch(error => {
      Modal.error({title: '提示', content: error.msg || error.message, zIndex: 9999});
    });
  }

  selectNext = ()=>{
    let items = this.state.videos;
    let pagination = this.state.pagination;
    pagination.hasPre = true;
    let cur = pagination.next;
    pagination.items = items.slice((cur-1)*pageSize,cur*pageSize);
    console.log(cur, pagination.items);
    pagination.hasNext = items.length > cur*pageSize;
    if (pagination.hasNext) {
      pagination.next = cur+1;
    }
    this.setState({pagination,cur})
  }

  selectPre = ()=>{
    let items = this.state.videos;
    let cur= this.state.cur;
    let pagination = this.state.pagination;
    pagination.hasPre = cur>1;
    cur = cur-1;
    pagination.items = items.slice((cur-1)*pageSize,cur*pageSize);
    console.log(cur, pagination.items);
    pagination.next = cur+1;
    pagination.hasNext = true;
    this.setState({pagination,cur})
  }
  close= () => {
    this.props.close();
  }

  jmp2Upload = ()=>{
    window.open('https://my.huangye88.com/video/edit/', '_blank');
  }

  onItemChange = (item) =>  {
    const { selectedVideos} = this.state;
    const idx  = this.indexOf(item.path);
    if (idx!==-1) {
      selectedVideos.splice(idx, 1);
    } else {
      selectedVideos.push(item);
    }
    let list = [].concat(selectedVideos);
    this.setState({
      selectedVideos:list,
    })
  };

  selectAll = () => {
    const { changed,  videos} = this.state;

    let list =[];
    for(let i=0;i<videos.length;i++){
      list.push(videos[i])
    }
    this.setState({
      selectedVideos:list,
      changed:!changed
    })
  }
  unSelectAll= () => {
    const { changed} = this.state;

    this.setState({
      selectedVideos:[],
      changed:!changed
    })
  }
  selReverseAll = () => {
    const { changed,  videos, selectedVideos} = this.state;

    let list =[];
    for(let i=0;i<videos.length;i++){
      if (this.indexOf(videos[i].path)==-1) {
        list.push(videos[i])
      }
    }
    this.setState({
      selectedVideos:list,
      changed:!changed
    })
  }


  close = () => {
    this.props.close();
  }
  onSaveBtn = () => {
    const { selectedVideos = [] } = this.state;
    if ((selectedVideos.length) > 6) {
      Modal.error({
        content: '视频不能超过6个',
        zIndex: 9999
      });
      return;
    }

    this.props.onVideoAdded(selectedVideos);

    this.props.close();
  }

  indexOf = (path)=>{
    const { selectedVideos} = this.state;
    for(let idx in selectedVideos) {
      const video = selectedVideos[idx];
      if (video.path == path) {
        return idx;
      }
    }
    return -1;
  }


  render(){
    const { videos,selectedVideos, pagination,cur} = this.state;


    return (
      <div style={{ background: '#fff', padding: 24 }}>
        <div className={styles.mask} onClick={this.close}></div>
        <div className={styles.modalDlg}>
          <div className={styles.titleName}>视频</div>
          <CloseOutlined className={styles.closeIcon} onClick={this.close} />
          <div className={styles.itemView}>
            <div style={{ marginLeft: '10px' }}>
                <Button onClick={this.jmp2Upload}>
                  <CloudUploadOutlined />
             上传
          </Button>
            </div>
          </div>

          <div className={styles.cityChexBox} style={{ height: '100%' }}>
            <Row style={{ justifyContent: 'start' }}>

            {pagination.items.map((item, index) => {
                return <Col key={item.title+index}>
                  <MyCheckVideoBox
                    checked={this.indexOf(item.path)!==-1}
                    value={item.path}
                    name={item.title}
                    item={item}
                    id={item.id}
                    index={index}
                    disable={false}
                    onChange={(item) => this.onItemChange(item)}

                  >
                  </MyCheckVideoBox> </Col>
              })}
            </Row>
          </div>
          <Row style={{display:'flex',justifyContent:'space-between',marginTop:'10px'}}>
            <Col span={16}>
              <Row>
                <Col  style={{color:'black',lineHeight:'30px',justifyContent:'center'}}>
                 共{videos.length}个视频
                </Col>
      <Col  >
                <Button  appearance="default" size="sm" style={{ marginLeft: '10px' }} onClick={this.selectAll}>全选</Button>
                </Col>
                <Col  >
                <Button  appearance="default" size="sm" style={{ marginLeft: '10px' }} onClick={this.unSelectAll}>全不选</Button>
                </Col>
                <Col  >
                <Button  appearance="default" size="sm" style={{ marginLeft: '10px' }} onClick={this.selReverseAll}>反选</Button>
                </Col>
              </Row>
            </Col>
            <Col span={8}>
              <Row>
                <Col>
                  <Button  appearance="default" size="sm" style={{ marginLeft: '10px' }} disabled={!pagination.hasPre} onClick={this.selectPre}>上一页</Button>
                  <span style={{display:"inline-block", lineHeight:'32px', textAlign:'center', marginLeft:'10px'}}>第{cur}页</span>
                  <Button  appearance="default" size="sm" style={{ marginLeft: '10px' }} disabled={!pagination.hasNext} onClick={this.selectNext}>下一页</Button>
                </Col>
              </Row>
            </Col>
          </Row>
           <div style={{ marginTop: '10px', display: 'flex', flexDirection: 'row', justifyContent: 'center' }}>
            <Row gutter={16}>
              <Col span={12}>
                <Button type="primary" style={{ width: '120px' }} block onClick={this.onSaveBtn}>
                  确定
              </Button>
              </Col>
              <Col span={12}>
                <Button type="primary" style={{ width: '120px' }} block onClick={this.close}>
                  取消
                </Button>
              </Col>
            </Row>
          </div>
        </div>
      </div>
    );
  }
}

export default connect(({ selectedVideos}) => ({
  ...selectedVideos
}))(VideoWall);
