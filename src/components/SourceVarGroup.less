.customright {
  padding: 10px;
  font-size: 13px;
  border: 1px solid #e6e6e6;
}

.error {
  border: 2px solid #f00;
}

.title_head {
  box-sizing: content-box;
  height: 24px;
  padding: 10px 5px;
  color: #333;
  font-size: 14px;
  line-height: 24px;
  background: #ffebca;
  border-top: 1px solid #e6e6e6;
  border-right: 1px solid #e6e6e6;
  border-left: 1px solid #e6e6e6;
}
.last_title_head {
  font-size: 14px;
}
.title_head span {
  display: inline-block;
  height: 24px;
  margin-left: 5px;
  padding: 0 5px;
  color: #fff;
  font-size: 12px;
  line-height: 24px;
  background: #999;
  border-radius: 5px;
}

.rule {
  color: #999;
  font-size: 12px;
  line-height: 20px;
}

.generatetitle_butn {
  margin: 0 auto;
  padding: 5px;
}

.added_bottom {
  margin: 0 auto;
  padding: 10px 0;
}
.added_bottom > span {
  color: #666;
  font-size: 14px;
}
.added_bottom > span font {
  color: #f00;
}

.addtitle_submit {
  margin: 0 auto;
  padding: 10px 0;
  text-align: center;
}
.top10 {
  margin-top: 10px;
}
.sourcetitle {
  background-color: #ffebca;
  height: 40px;
  line-height: 40px;
  padding: 0 10px;
  font-size: 14px;
  color: #333;
}
