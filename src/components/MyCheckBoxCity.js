import React, { PureComponent } from 'react';
import { connect } from 'dva';
import {
    Checkbox,
  } from 'antd';


import styles from './MyCheckBoxCity.less';

class MyCheckbox extends PureComponent {

    constructor(props) {
        super(props);
        const { checked } = props;
        this.state = {
            checked: checked
        };
    }

    componentWillReceiveProps(nextProps, nextContext) {
        const {checked,value}=nextProps;
        this.setState({
            checked:checked,
            value:value
        })
        if (this.props.isSelectAll !== nextProps.isSelectAll) {
            // 全选之间的切换, 如果目标结果是全选, 全部设置checked
            if (nextProps.isSelectAll) {
                return this.setState({
                    checked: true
                });
            }
        }
        if (this.props.isSelectAll === true) {
            // 如果现在是全选状态, 目标操作是取消全选 并且已经明确(indeterminate === false)是全部取消(取消单行也是取消全选,但不是全部取消) 
            if (nextProps.isSelectAll === false && nextProps.indeterminate === false) 
                return this.setState({
                    checked: false
                });
        }
    }
    shouldComponentUpdate(nextProps,nextState){
        if(nextProps.checked==this.state.checked&&nextProps.value==this.state.value){
            return false;
        }else{
            return true;
        }
    }

    render(){
        return (
            <div className={styles.noadmin}>
            <Checkbox
                disabled={this.props.disabled}
                onChange={() => {
                    this.setState( { checked: !this.state.checked } );
                    this.props.onChange(this.props.value,this.props.index);
                }}
                checked={this.state.checked}
                value= {this.props.value}
            >
                {this.props.value}
                </Checkbox>
            </div>
        );
    }
}
export default connect(({home }) => ({...home
}))(MyCheckbox);