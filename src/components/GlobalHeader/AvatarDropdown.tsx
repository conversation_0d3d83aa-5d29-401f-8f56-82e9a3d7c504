import { LogoutOutlined, UserOutlined, PhoneOutlined, QqOutlined } from '@ant-design/icons';
import { Avatar, Menu, Space, Spin } from 'antd';
import { ClickParam } from 'antd/es/menu';
import { FormattedMessage } from 'umi-plugin-react/locale';
import React from 'react';
import { connect } from 'dva';
import router from 'umi/router';

import { ConnectProps, ConnectState } from '@/models/connect';
import { CurrentUser } from '@/models/user';
import HeaderDropdown from '../HeaderDropdown';
import wechat from '@/assets/wechat.png';
import styles from './index.less';

export interface GlobalHeaderRightProps extends ConnectProps {
  currentUser?: CurrentUser;
  menu?: boolean;
}

class AvatarDropdown extends React.Component<GlobalHeaderRightProps> {
  onMenuClick = (event: ClickParam) => {
    const { key } = event;
    const { dispatch } = this.props;
    if (key === 'logout') {
      // eslint-disable-next-line no-unused-expressions
      dispatch && dispatch({ type: 'home/loginOut' });
      return;
    }
    if (key === 'help') {
      // eslint-disable-next-line no-unused-expressions
      dispatch &&
        dispatch({
          type: 'home/getUrl',
          payload: {
            url: 'http://www.huangye88.com/help/fafazhushou_43.html',
          },
          callBack: url => {
            window.open(url, '_blank');
          },
        });
      return;
    }
  };

  render(): React.ReactNode {
    const { currentUser = {}, menu } = this.props;
    // if (!menu) {
    //   return (
    //     <span className={`${styles.action} ${styles.account}`}>
    //       <Avatar size="small" className={styles.avatar} src={currentUser.avatar} alt="avatar" />
    //       <span className={styles.name}>{currentUser.name}</span>
    //     </span>
    //   );
    // }
    const menuHeaderDropdown = (
      <Menu className={styles.menu} selectedKeys={[]} onClick={this.onMenuClick}>
        <Menu.Item key="help">
          <UserOutlined />
          <FormattedMessage id="layout.user.link.help" defaultMessage="help" />
        </Menu.Item>
        <Menu.Divider />
        <Menu.Item key="logout">
          <LogoutOutlined />
          <FormattedMessage id="menu.logout" defaultMessage="logout" />
        </Menu.Item>
      </Menu>
    );

    const menuZixunDropdown = (
      <Menu className={styles.menu} selectedKeys={[]} onClick={this.onMenuClick}>
        <Menu.Item key="phone">
          <PhoneOutlined />
          ************
        </Menu.Item>
        <Menu.Divider />
        <Menu.Item key="qq">
          <QqOutlined />
          1643503640
        </Menu.Item>
        <Menu.Item key="wx">
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <div style={{ display: 'flex', flexDirection: 'column', marginRight: '10px' }}>
              <span>微</span>
              <span>信</span>
              <span>咨</span>
              <span>询</span>
            </div>
            <img src={wechat} alt="wechat" style={{ width: '80px', height: '80px' }} />
          </div>
        </Menu.Item>
      </Menu>
    );

    return currentUser ? (
      <Space>
        <span className={`${styles.action} ${styles.account}`}>
          <a href="https://www.huangye88.com/help/fafazhushous_245.html" target="_blank">
            <span className={styles.name}>操作视频</span>
          </a>
        </span>
        <HeaderDropdown overlay={menuZixunDropdown}>
          <span className={`${styles.action} ${styles.account}`}>
            <span className={styles.name}>咨询客服</span>
          </span>
        </HeaderDropdown>
        <HeaderDropdown overlay={menuHeaderDropdown}>
          <span className={`${styles.action} ${styles.account}`}>
            <span className={styles.name}>{currentUser.username}</span>
          </span>
        </HeaderDropdown>
      </Space>
    ) : (
      <Spin size="small" style={{ marginLeft: 8, marginRight: 8 }} />
    );
  }
}
export default connect(({ home }: ConnectState) => ({
  currentUser: home.currentUser,
}))(AvatarDropdown);
