// @flow
import React, { PureComponent } from 'react';
import { connect } from 'dva';
import {
  Form,
  Table,

  Divider,
  Select,
  Row,
  Col,
  Button,
  Checkbox,
  Input,
  Modal,
  Layout,
} from 'antd';
import 'antd/dist/antd.css';
import { CloseOutlined } from '@ant-design/icons';
const { TextArea } = Input;
import styles from './UpdatePicName.less';

const { confirm } = Modal;

const { Option } = Select;
const CheckboxGroup = Checkbox.Group;

const { Header, Content } = Layout;


class updatePicName extends PureComponent {
  state = {
    newItemList: [],
    item: this.props.value,
  };

  consoleAlertInput = () => {
    this.props.close();
  }

  onChange = (e) => {
   
    this.setState({
      item: e.target.value
    })
  }



  onSaveBtn = () => {
    const {item} =this.state;
    if(item.length>=8){
      Modal
      
      .error({
        title: '字数超出限制',
        content: '长度不得超过8个字',
        zIndex:9999
      });
    }else{
      this.props.onSaveBtn(this.state.item)
    }
    
   }
  render() {
    const { type } = this.props;
    let content = "";
     
    return (
      <div style={{ background: '#fff', padding: 24 }}>

        <div className={styles.mask} onClick={this.consoleAlertInput}></div>
        <div className={styles.modalDlg}>

          <CloseOutlined className={styles.closeIcon} onClick={this.consoleAlertInput} />
          <div className={styles.itemView}> 修改名称 </div>
          <div className={styles.itemView2}>{content}</div>
         <Input  
            placeholder="长度不得超过8个字"
            onChange={this.onChange}
            value={this.state.item} /> 
          <div className={styles.buttomButton} >
            <Row>
              <Button type="primary" style={{ marginLeft: '10px' }} onClick={this.consoleAlertInput}>取消</Button>
              <Button type="primary" style={{ marginLeft: '10px' }} onClick={this.onSaveBtn}>确认</Button>
            </Row>
          </div>

        </div>
      </div>
    );
  }
}

export default connect(({ }) => ({
}))(updatePicName);