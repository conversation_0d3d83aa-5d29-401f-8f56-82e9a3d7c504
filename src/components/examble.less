/* 遮罩层 */
.mask{
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  background: #000;
  z-index: 9000;
  opacity: 0.5;
}

/* 弹出层 */
.modalDlg{
  width: 80%;
  height: 90%;
  position: fixed;
  top: 20px;
  left: 0;
  right: 0;
  z-index: 9999;
  margin: 0 auto;
  background-color: #fff;
  border-radius:5px;
  display: flex;
  flex-direction: column;
  padding: 10px;
  overflow:scroll;
  overflow-x:hidden;
  :global {
  
    .ant-form-item-label{
        width: 80px;
    }
     
    }
}
.itemView{
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: start;
  font-size: 16px;
  color: #000000;
  margin: 10px 10px ;
  margin-top: 20px;
}
  .titleName{
      font-size: 18px;
      color: #666666;
  }
.searchDiv{
  display: flex;
  flex-direction: row;
  width: 100vh
}
.closeIcon{
  position: fixed;
  top: 25px;
  right: 12%;
 font-size: 18px;
 color: #666666;
}
 
.btn{
  color:#000;
}
.log {
  background: white;
  margin: 0;
  padding: 0.5em 0.5em 0.5em 0.5em;
  position: absolute;
  top: 0.5em;
  left: 0.5em;
  right: 0.5em;
  bottom: 3em;
  overflow: auto;
}
.action {
  padding: 0 0.5em 0 0.5em;
  margin: 0;
  position: absolute;
  bottom: 1em;
  left: 0px;
  width: 100%;
  overflow: hidden;
}