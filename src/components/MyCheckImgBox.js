import React, { PureComponent } from 'react';
import { connect } from 'dva';
import {
    Checkbox,
    Image,
    Row,
    Col
} from 'antd';


import styles from './MyCheckImgBox.less';
import { EditOutlined, DeleteOutlined, EyeOutlined } from '@ant-design/icons';

class MyCheckImgbox extends React.Component {

    constructor(props) {
        super(props);
        const { checked, disable } = props;
        this.state = {
            checked: checked,
            disable: disable,
            value: "",
        };
    }

    componentWillReceiveProps(nextProps, nextContext) {
        const { checked, value } = nextProps;
        this.setState({
            checked: checked,
            value: value,
            disable: false
        })

    }
    shouldComponentUpdate(nextProps, nextState) {
        if (nextProps.checked == this.state.checked && nextProps.value == this.state.value) {
            return false;
        } else {
            return true;
        }
    }
    ChangeName = () => {
        const { name, id } = this.props;
        this.props.ChangeName({ name, id });
    }
    delAlbum = () => {
        const {  id } = this.props;
        this.props.delAlbum({id });
    }
    disableView = () => {
        this.setState({
            disable: true
        })
        this.forceUpdate();
    }
    //onerror="this.src=http://oss.huangye88.net/live/user/1530371/1533886544033995600-5.jpg;this.οnerrοr=null'"
    render() {
        const { name, id,item } = this.props;
        const { disable } = this.state;
        return (
            <div className={styles.borderDiv}   >
                <div className={styles.noadmin}>
                <div className={styles.demo}>
                    <Image className={styles.imagePic} src={disable ? 'http://mentalroad-apk-release.oss-cn-shanghai.aliyuncs.com/error.png' : this.props.value} alt="avatar" onError={this.disableView} />
                    </div>
                   
                   
                    <EyeOutlined className={styles.eyesDelete} />

                    <Checkbox
                        className={styles.CheckImgbox}
                        disabled={disable}
                        onChange={() => {
                            this.setState({ checked: !this.state.checked });
                            this.props.onChange(item);
                        }}
                        checked={this.state.checked}
                        value={this.props.value}
                    >
                    </Checkbox>
                </div>
                 

            </div>
        );
    }
}
export default connect(({ }) => ({
}))(MyCheckImgbox);