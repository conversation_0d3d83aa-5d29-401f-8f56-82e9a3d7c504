//* 组合生成标题 *//

.customright {
  padding: 10px;
  font-size: 13px;
  border: 1px solid #e6e6e6;
}

.title_head {
  box-sizing: content-box;
  height: 24px;
  padding: 10px 5px;
  color: #333;
  font-size: 14px;
  line-height: 24px;
  background: #ffebca;
  border-top: 1px solid #e6e6e6;
  border-right: 1px solid #e6e6e6;
  border-left: 1px solid #e6e6e6;
}
.last_title_head {
  font-size: 14px;
}
.title_head span {
  display: inline-block;
  height: 24px;
  margin-left: 5px;
  padding: 0 5px;
  color: #fff;
  font-size: 12px;
  line-height: 24px;
  background: #999;
  border-radius: 5px;
}

.rule {
  color: #999;
  font-size: 12px;
  line-height: 20px;
}
