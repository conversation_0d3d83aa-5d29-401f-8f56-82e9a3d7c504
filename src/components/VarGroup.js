import React from 'react';

import { connect } from 'dva';
import { Button, Modal, Form, Row, Col, Input } from 'antd';
import styles from './VarGroup.less';
import CustomUploadimg from '@/components/CustomUploadimg';
// todo: 已作废
class VarGroup extends React.Component {
  state = {
    open: false,
    confirmLoading: false,
    product: {
      vars: {
        关键词组: [],
        别名组: [],
        副标题组: [],
      },
      cache: {
        keywords: [],
        alias: [],
        subtitles: [],
      },
    },
    otherKeys: {},
    otherValues: {},
  };

  // mounted
  componentDidMount() {
    console.log('VarGroup loaded', this.props.product);
  }

  componentDidUpdate(prevProps) {
    console.log('VarGroup updated', prevProps.product, this.props.product);
    if (prevProps.product !== this.props.product) {
      // 进行相应的更新操作
      // eslint-disable-next-line react/no-did-update-set-state
      this.setState(({ product }) => {
        if (this.props.product.vars) {
          product.vars = this.props.product.vars;
        }
        product.cache = this.props.product.cache;
        const others = { ...product.vars }; // 复制一个对象
        delete others['关键词组'];
        delete others['别名组'];
        delete others['副标题组'];
        const otherKeys = {};
        const otherValues = {};
        // 遍历others, key给otherKeys, value给otherValues, 用于后续的更新操作, otherKeys和otherValues 的内容是对象{idx:value}
        let idx = 0;
        for (const key in others) {
          otherKeys[idx] = key;
          otherValues[idx] = others[key];
          idx++;
        }
        otherKeys[idx] = '';
        otherValues[idx] = [];
        return { product, otherKeys, otherValues };
      });
    }
  }

  showModal = () => {
    this.setState({ open: true });
  };

  handleOk = () => {
    this.setState({ open: false });
    if (this.props.onVarsChange) {
      // 这里需要处理 otherKeys 和 otherValues, 合并到 product.vars中
      const { otherKeys, otherValues, product } = this.state;
      for (const i in otherKeys) {
        // 都不为空才保存
        if (otherKeys[i] && otherValues[i].length > 0) {
          product.vars[otherKeys[i]] = otherValues[i];
        }
      }
      this.props.onVarsChange(product.vars);
    }
  };

  syncVarGroup = (from, to) => {
    console.log('同步变量组', from, to);
    this.setState(
      ({ product }) => {
        product.vars[to] = product.cache[from];
        return { product };
      },
      () => {
        this.forceUpdate();
      },
    );
  };

  handleCancel = () => {
    console.log('Clicked cancel button');
    this.setState({ open: false });
  };

  onTitleChange = e => {
    const v = e.target.value;
    const newlist = v.split(/\r\n|\r|\n|\s+/);
    this.setState(({ product }) => ({
      product: {
        ...product,
        vars: {
          ...product.vars,
          副标题组: newlist,
        },
      },
    }));
  };

  onAliasChange = e => {
    const v = e.target.value;
    const newlist = v.split(/\r\n|\r|\n|\s+/);
    this.setState(({ product }) => ({
      product: {
        ...product,
        vars: {
          ...product.vars,
          别名组: newlist,
        },
      },
    }));
  };

  onKeywordsChange = e => {
    const v = e.target.value;
    const newlist = v.split(/\r\n|\r|\n|\s+/);
    this.setState(({ product }) => ({
      product: {
        ...product,
        vars: {
          ...product.vars,
          关键词组: newlist,
        },
      },
    }));
  };

  isTitleOk = (title, maxPerLen) => {
    const numberCn = /^[0-9]+$/im;
    const letterCn = /^[A-Za-z]+$/im;
    if (title.length > maxPerLen) {
      return false;
    }
    if (numberCn.test(title) || letterCn.test(title)) {
      return false;
    }
    return true;
  };

  checkTitleError = (title, maxPerLen) => {
    const numberCn = /^[0-9]+$/im;
    const letterCn = /^[A-Za-z]+$/im;
    if (title.length > maxPerLen) {
      return `不能超过${maxPerLen}字`;
    }
    if (numberCn.test(title)) {
      return '不能为纯数字';
    }
    if (letterCn.test(title)) {
      return '不能为纯字母';
    }
    return '';
  };

  hasErr = (list, maxPerLen, maxLen) => {
    if (list.length > maxLen) {
      return true;
    }
    for (const i in list) {
      const item = list[i];
      if (!this.isTitleOk(item, maxPerLen)) {
        return true;
      }
    }
    return false;
  };

  onKeyChanged = (e, idx) => {
    const v = e.target.value;
    this.setState(({ otherKeys }) => {
      otherKeys[idx] = v;
      return { otherKeys };
    });
  };

  onValueChanged = (e, idx) => {
    const v = e.target.value;
    this.setState(({ otherValues }) => {
      // 这里的v 是一个字符串，需要转换成数组
      otherValues[idx] = v.split(/\r\n|\r|\n|\s+/);
      return { otherValues };
    });
  };

  addAnotherVar = () => {
    this.setState(({ otherKeys, otherValues }) => {
      const idx = Object.keys(otherKeys).length;
      otherKeys[idx] = '';
      otherValues[idx] = [];
      return { otherKeys, otherValues };
    });
  };

  addImageVar = () => {
    const { otherKeys } = this.state;
    // 遍历otherKeys, 如果有图片组，就不添加, 否则添加
    for (const i in otherKeys) {
      if (otherKeys[i] === '图片组') {
        return;
      }
    }
    this.setState(({ otherKeys, otherValues }) => {
      const idx = Object.keys(otherKeys).length;
      otherKeys[idx] = '图片组';
      otherValues[idx] = [];
      return { otherKeys, otherValues };
    });
  };

  onPicsChanged = selected => {
    const { otherKeys, otherValues } = this.state;
    const idx = Object.keys(otherKeys).length;
    // 需要遍历otherKeys，找到图片组
    for (const i in otherKeys) {
      if (otherKeys[i] === '图片组') {
        // selected 是个对象数组，这里指需要url属性
        otherValues[i] = selected.map(item => item.url);
        this.setState({ otherValues });
        return;
      }
    }
  };

  render() {
    const { open, confirmLoading, product, otherKeys, otherValues } = this.state;
    console.log(product, 'product');
    const keywords = product.vars && product.vars['关键词组'] ? product.vars['关键词组'] : [];
    const alias = product.vars && product.vars['别名组'] ? product.vars['别名组'] : [];
    const subtitles = product.vars && product.vars['副标题组'] ? product.vars['副标题组'] : [];

    const keywordCtrl = {
      text: keywords ? keywords.join('\r\n') : '',
      num: keywords.filter(item => item.length > 0).length,
      hasErr: this.hasErr(keywords, 20, 2000),
    };
    const aliasCtrl = {
      text: alias ? alias.join('\r\n') : '',
      num: alias.filter(item => item.length > 0).length,
      hasErr: this.hasErr(alias, 15, 2000),
    };
    const titleCtrl = {
      text: subtitles ? subtitles.join('\r\n') : '',
      num: subtitles.filter(item => item.length > 0).length,
      hasErr: this.hasErr(subtitles, 15, 2000),
    };

    return (
      <div style={{ margin: '0px 10px 0px 0px', float: 'left' }}>
        <Button type="primary" onClick={this.showModal}>
          编辑变量组
        </Button>
        <Modal
          title="变量组"
          visible={open}
          onOk={this.handleOk}
          confirmLoading={confirmLoading}
          onCancel={this.handleCancel}
          footer={[
            <Button onClick={this.handleCancel}>取消</Button>,
            <Button type="primary" onClick={this.handleOk}>
              保存
            </Button>,
          ]}
          width={1000}
        >
          <Form>
            <Form.Item
              label={<span>默认变量组</span>}
              labelCol={{ span: 4 }}
              wrapperCol={{ span: 20 }}
              name="defaultGroup"
            >
              <Row gutter={[16, 16]} justify="space-between" className={styles.customright}>
                <Col span={8}>
                  <div className={styles.title_head}>
                    关键词组[{keywordCtrl.num}]
                    <span onClick={() => this.syncVarGroup('keywords', '关键词组')}>
                      同步已设置关键词组
                    </span>
                  </div>
                  <Input.TextArea
                    value={keywordCtrl.text}
                    onChange={this.onKeywordsChange}
                    style={{
                      border: keywordCtrl.hasErr ? '1px solid red' : '1px solid #d3d3d3',
                      width: '100%',
                      overflow: 'scroll',
                      overflowX: 'hidden',
                    }}
                    rows={4}
                    placeholder="请输入关键词"
                  />
                  <div className={styles.rule}>
                    换行分隔，每个关键词不超过20个字，且不能是纯数字或纯字母，最多可添加2000个。
                  </div>
                </Col>
                <Col span={8}>
                  <div className={styles.title_head}>
                    副标题组[{titleCtrl.num}]
                    <span onClick={() => this.syncVarGroup('subtitles', '副标题组')}>
                      同步已设置副标题组
                    </span>
                  </div>
                  <Input.TextArea
                    rows={4}
                    onChange={this.onTitleChange}
                    value={titleCtrl.text}
                    style={{
                      border: titleCtrl.hasErr ? '1px solid red' : '1px solid #d3d3d3',
                      width: '100%',
                      overflow: 'scroll',
                      overflowX: 'hidden',
                    }}
                    placeholder="请输入副标题"
                  />
                  <div className={styles.rule}>
                    换行分隔，每个关键词不超过15个字，且不能是纯数字或纯字母，最多可添加2000个。
                  </div>
                </Col>
                <Col span={8}>
                  <div className={styles.title_head}>
                    别名组[{aliasCtrl.num}]
                    <span onClick={() => this.syncVarGroup('alias', '别名组')}>
                      同步已设置别名组
                    </span>
                  </div>
                  <Input.TextArea
                    rows={4}
                    onChange={this.onAliasChange}
                    value={aliasCtrl.text}
                    style={{
                      border: aliasCtrl.hasErr ? '1px solid red' : '1px solid #d3d3d3',
                      width: '100%',
                      overflow: 'scroll',
                      overflowX: 'hidden',
                    }}
                    placeholder="请输入别名"
                  />
                  <div className={styles.rule}>
                    换行分隔，每个关键词不超过15个字，且不能是纯数字或纯字母，最多可添加2000个。
                  </div>
                </Col>
              </Row>
            </Form.Item>

            <Form.Item
              label={<span>添加其他变量组</span>}
              labelCol={{ span: 4 }}
              wrapperCol={{ span: 20 }}
              name="otherGroup"
            >
              <div>
                <Button onClick={this.addAnotherVar}>添加其他变量</Button>
                <span onClick={this.addImageVar}>
                  {' '}
                  <CustomUploadimg
                    onConfirm={selected => {
                      this.onPicsChanged(selected);
                    }}
                  />
                </span>
              </div>
              {Object.keys(otherKeys).map((item, index) => (
                // eslint-disable-next-line react/no-array-index-key
                <React.Fragment key={index}>
                  <div className={styles.top10}>
                    <Input
                      value={otherKeys[item]}
                      onChange={e => this.onKeyChanged(e, item)}
                      placeholder="输入变量名称"
                    />
                  </div>
                  <div className={styles.top10}>
                    <Input.TextArea
                      rows={4}
                      onChange={e => this.onValueChanged(e, item)}
                      value={otherValues[item].join('\r\n')}
                      placeholder="请输入内容"
                    />
                  </div>
                  <div className={styles.rule}>
                    每个内容之间使用空白行分隔，每个内容不超过500字，当前共
                    {otherValues[item].length}个内容，最多可添加100个内容。
                  </div>
                </React.Fragment>
              ))}
            </Form.Item>
          </Form>
        </Modal>
      </div>
    );
  }
}

export default connect()(VarGroup);
