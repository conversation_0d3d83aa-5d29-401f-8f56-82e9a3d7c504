// @flow
import React, { PureComponent } from 'react';
import { connect } from 'dva';
import {
  Form,
  Table,
  Image,
  Divider,
  Select,
  Row,
  Col,
  Button,
  Checkbox,
  Input,
  Modal,
  Layout,
  Upload
} from 'antd';
import 'antd/dist/antd.css';
import { CloseOutlined, CloudUploadOutlined } from '@ant-design/icons';
const { TextArea } = Input;
import styles from './UpdateAlbumPic.less';
import { baseUrl } from '../configs/config';
const { confirm } = Modal;
const { Option } = Select;
const CheckboxGroup = Checkbox.Group;

const { Header, Content } = Layout;


class UpdateAlbumPic extends PureComponent {
  state = {
    selAlbumsId: 0,
    albumsList: [],
    isShowTip: false,
    albumState:{}
  };
  componentDidMount() {
    const { value } = this.props;

    this.setState(
      {
        selAlbumsId: value
      }
    )
    const that = this;
    this.props.dispatch({
      type: 'picture/getAlbumsList', payload: {}, callBack: (albumsList) => {

        that.setState({
          albumsList: albumsList,
        })
      }
    });
    this.props.dispatch({
      type: 'picture/getAlbumState', payload: {}, callBack: (albumState) => {

        that.setState({
          albumState: albumState,
        })
      }
    });


  }



  consoleAlertInput = () => {
    this.props.close();
  }

  onChange = (e) => {
    this.setState({
      item: e.target.value
    })
  }

  handleChange = (value) => {
    const that = this;

    this.setState({
      selAlbumsId: value

    })

  }
  ShowTip = () => {
    window.open('http://www.paihang8.com/fafa.html', '_blank');
  }
  closeTip = () => {
    this.setState({
      isShowTip: false,
    })
  }
  openFeatures =()=>{


  }
  handleChangeQrcode = info => {
    const { selAlbumsId = 0 } = this.props;
    const { file } = info;
    // if (file && file.status == 'done') {
    //   if(file.response.data[0].url=="该图片已存在"){
    //     Modal.warn({
    //       content: '该图片已存在',
    //       zIndex: 9999
    //     });
    //   }
    // }
    console.log(info)
    console.log(this.props.id)
    const that = this;
        if (file && file.status == 'done') {

    console.log(this.props.id)
    this.props.dispatch({
      type: 'merchant/updateMerchant', payload: {id:this.props.id,pic:file.response.data[0].url}, callBack: (albumState) => {

        that.props.close();
      }
    });
    }
  }

  render() {
    const { selAlbumsId = 0, albumsList,albumState } = this.state;

    let selAlbumsName = "默认相册";
    if (selAlbumsId == 0) {
      if (albumsList.length > 0) {
        selAlbumsName = albumsList[0].name;
      }

    } else {
      if (albumsList.length > 0) {
        let item = albumsList.filter(item => item.id == selAlbumsId)[0];
        if (item) {
          selAlbumsName = item.name
        }

      }

    }
    let Authorization = { "Authorization": 'Bearer ' + window.sessionStorage.getItem("Authorization"), "multiple": "" };
    return (
      <div style={{ background: '#fff', padding: 24 }}>

        <div className={styles.mask} onClick={this.consoleAlertInput}></div>
        <div className={styles.modalDlg}>

          <CloseOutlined className={styles.closeIcon} onClick={this.consoleAlertInput} />
          <div className={styles.itemView}>企业声明书</div>
          <div className={styles.itemView}>


            <div style={{ marginLeft: '10px' }}>
              <Upload
                name="product"
                multiple
                action={baseUrl + '/v2/album/upload/' + selAlbumsId}
                withCredentials
                headers={Authorization}
                data={file => ({ // data里存放的是接口的请求参数
                  file: file, // file 是当前正在上传的图片

                })}
                showUploadList={false}
                className="avatar-uploader"
                onChange={this.handleChangeQrcode}
              >
                <Button>
                  <CloudUploadOutlined />
             上传
          </Button>
              </Upload>
            </div>



          </div>

          <div className={styles.itemViewTip}>企业声明书说明：企业声明书为八方资源要求 </div>
          <div className={styles.itemViewTip}>必须上传，其流程如下： </div>
          <div className={styles.itemViewTip}>1.下载企业声明书模板，<a style={{color:'blue'}} onClick={this.ShowTip}>下载</a>； </div>
          <div className={styles.itemViewTip}>2.打印并加盖公章； </div>
          <div className={styles.itemViewTip}>3.上传企业声明书图片，图片支持jpg、png格式，不大于1M；
          </div>


          <div   style={{justifyContent:'center',marginTop:'20px',width:'100%',display:'flex'}}><Button type="primary" onClick={this.consoleAlertInput}>关闭</Button></div>

        </div>


      </div>
    );
  }
}

export default connect(({ picture,merchant }) => ({
  ...picture,...merchant
}))(UpdateAlbumPic);
