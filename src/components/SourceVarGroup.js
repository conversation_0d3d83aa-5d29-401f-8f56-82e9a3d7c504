import React, { useState } from 'react';
import { connect } from 'dva';
import { Button, Modal, Form, Row, Col, Input } from 'antd';
import styles from './SourceVarGroup.less';
import CustomUploadimg from '@/components/CustomUploadimg';
import CustomUploadDisplaypic from '@/components/CustomUploadDisplaypic';

class SourceVarGroup extends React.PureComponent {
  state = {
    open: false,
    confirmLoading: false,
    product: {
      vars: {
        图片组: [],
      },
    },
    errors: {
      图片组: false,
    },
    otherKeys: {},
    otherValues: {},
  };

  componentDidUpdate(prevProps) {
    console.log('SourceVarGroup updated', prevProps.product, this.props.product);
    if (prevProps.product !== this.props.product) {
      // 进行相应的更新操作
      // eslint-disable-next-line react/no-did-update-set-state
      this.setState(({ product, errors }) => {
        if (this.props.product.vars) {
          product.vars = this.props.product.vars;
        }
        product.cache = this.props.product.cache;
        const others = { ...product.vars }; // 复制一个对象
        delete others['图片组'];

        const otherKeys = {};
        const otherValues = {};
        // 遍历others, key给otherKeys, value给otherValues, 用于后续的更新操作, otherKeys和otherValues 的内容是对象{idx:value}
        let idx = 0;
        for (const key in others) {
          otherKeys[idx] = key;
          errors[key] = false;
          otherValues[idx] = others[key];
          idx++;
        }

        return { product, otherKeys, otherValues, errors };
      });
    }
  }

  setOpen(open) {
    this.setState({ open });
  }

  onPicsChanged = selected => {
    this.setState(({ product }) => {
      // 获取现有的图片组
      const currentImages = product.vars['图片组'] || [];
      // 创建 Set 来去重
      const uniqueImages = new Set([...currentImages, ...selected]);
      // 转换回数组
      const mergedImages = Array.from(uniqueImages);

      return {
        product: {
          ...product,
          vars: {
            ...product.vars,
            图片组: mergedImages,
          },
        },
      };
    });
  };

  onValueChanged = (e, idx) => {
    const v = e.target.value;
    this.setState(({ otherValues, otherKeys, errors }) => {
      const newOtherValues = { ...otherValues };
      const newErrors = { ...errors };
      // 只过滤中间的空值，保留末尾的空值
      newOtherValues[idx] = v
        .split(/(?:\r\n|\r|\n){2,}/)
        .map((item, index, array) => {
          // 如果是最后一个元素，返回原值；否则过滤空白
          return index === array.length - 1 ? item : item.trim();
        })
        .filter((item, index, array) => {
          // 如果是最后一个元素，始终保留；否则过滤空字符串
          return index === array.length - 1 || item !== '';
        });
      if (newOtherValues[idx].length > 5) {
        newErrors[otherKeys[idx]] = true;
      } else {
        newErrors[otherKeys[idx]] = false;
      }
      return { otherValues: newOtherValues, errors: newErrors };
    });
  };

  handleOk = () => {
    if (this.props.onVarsChange) {
      // 这里需要处理 otherKeys 和 otherValues, 合并到 product.vars中
      const { otherKeys, otherValues, product } = this.state;
      for (const i in otherKeys) {
        // 都不为空才保存
        if (otherKeys[i] && otherValues[i].length > 0) {
          product.vars[otherKeys[i]] = otherValues[i];
        }
      }

      for (const key in product.vars) {
        var maxSize = key == '图片组' ? 50 : 5;
        if (product.vars[key].length > maxSize) {
          // 提示用户修改
          Modal.confirm({
            title: '提示',
            content: key + '超出限制，请修改。',
            zIndex: 9999,
            cancelText: '取消',
            okText: '确定',
            onOk() {},
            onCancel() {},
          });
          return;
        }
      }
      this.setState({ open: false });
      this.props.onVarsChange(product.vars);
    }
  };

  render() {
    const { open, confirmLoading, product, otherKeys, otherValues, errors } = this.state;
    const imgs = product.vars && product.vars['图片组'] ? product.vars['图片组'] : [];
    return (
      <div style={{ margin: '0px 10px 0px 0px', float: 'left' }}>
        <Button type="primary" onClick={() => this.setOpen(true)}>
          编辑变量组
        </Button>
        <Modal
          title="变量组"
          open={open}
          onOk={this.handleOk}
          onCancel={() => this.setOpen(false)}
          width={1000}
        >
          <Form>
            <Form.Item label={<span>图片组</span>} labelCol={{ span: 2 }} wrapperCol={{ span: 22 }}>
              <Row>
                <Col span={4}>
                  <CustomUploadimg
                    onConfirm={selected => {
                      this.onPicsChanged(selected);
                    }}
                  />
                </Col>
                <Col span={20}>
                  <CustomUploadDisplaypic
                    images={imgs}
                    onRemoved={item => {
                      // 从 titlepic 中移除item, 从coverImages中移除item
                      this.setState(({ product }) => {
                        const imgs = product.vars['图片组'].filter(t => t !== item);
                        const updatedProduct = {
                          ...product,
                          vars: {
                            ...product.vars,
                            图片组: imgs,
                          },
                        };
                        return {
                          product: updatedProduct,
                        };
                      });
                    }}
                  />
                </Col>
              </Row>
              <div className={styles.rule}>
                图片清晰、完整，当前共{imgs.length}张图片，最多上传50张图片。
              </div>
            </Form.Item>
            <Form.Item
              label={<span>素材内容组</span>}
              labelCol={{ span: 2 }}
              wrapperCol={{ span: 22 }}
            >
              {Object.keys(otherKeys).map((item, index) => (
                // eslint-disable-next-line react/no-array-index-key
                <div className={styles.top10}>
                  <div className={styles.sourcetitle}>{otherKeys[item]}</div>
                  <Input.TextArea
                    rows={4}
                    onChange={e => this.onValueChanged(e, item)}
                    value={otherValues[item].join('\r\n\r\n')}
                    placeholder="请输入内容"
                    className={errors[otherKeys[item]] ? styles.error : ''}
                  />
                  <div className={styles.rule}>
                    空白行分隔，每个内容不超过500个字，最多可添加5个内容。
                  </div>
                </div>
              ))}
            </Form.Item>
          </Form>
        </Modal>
      </div>
    );
  }
}
export default connect(({}) => ({}))(SourceVarGroup);
