/* 遮罩层 */
.mask{
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  background: #000;
  z-index: 12;
  opacity: 0.5;
}

/* 弹出层 */
.modalDlg{
  width: 80%;
  height: 90%;
  position: fixed;
  top: 20px;
  left: 0;
  right: 0;
  z-index: 12;
  margin: 0 auto;
  background-color: #fff;
  border-radius:5px;
  display: flex;
  flex-direction: column;
  padding: 10px;
 
  :global {
  
    .ant-form-item-label{
        width: 80px;
    }
     
    }
}
.itemView{
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: start;
  font-size: 16px;
  color: #000000;
  margin: 10px 10px ;
  margin-top: 20px;
}
  .titleName{
      font-size: 18px;
      color: #666666;
  }
.searchDiv{
  display: flex;
  flex-direction: row;
  width: 100vh
}
.closeIcon{
  position: fixed;
  top: 25px;
  right: 12%;
 font-size: 18px;
 color: #666666;
}

.cityChexBox{
  padding-left: 5px;
  height: 50vh;
  display: flex;
  border:1px solid #E6E6E6;
  flex-direction: column;
  color: #000;
  overflow:scroll;
  overflow-x:hidden;
}
 
.chexBoxChildItem{
  
  justify-content: space-between;
  display: flex;
  flex-direction: row;
  align-content: center;
  align-items: center;
  line-height: 60ox;
  padding-left:5px;
  :global {
  
    .ant-checkbox-wrapper{
      white-space: nowrap;
      max-width: 120px;
      overflow: hidden;
      text-overflow: ellipsis;
      animation:antCheckboxEffect 0 ease 0 1 normal;
    }
    .ant-checkbox-checked::after{
      transition: unset;
      animation:antCheckboxEffect 0 ease 0 1 normal;
    }
    .ant-checkbox-checked .ant-checkbox-inner:after {
      transition: unset;
      animation:antCheckboxEffect 0 ease 0 1 normal;
    }
    .ant-checkbox-inner:after {
      transition: unset;
      animation:antCheckboxEffect 0 ease 0 1 normal;
    }
    .ant-checkbox-inner {
      transition: unset;
      animation:antCheckboxEffect 0 ease 0 1 normal;
    }
    
    }
}
