import { Table } from 'antd';
import React from 'react';

const columns = [
  {
    title: '关键词',
    dataIndex: 'keyword',
    key: 'keyword',
  },
  {
    title: '搜索引擎',
    dataIndex: 'seo',
    key: 'seo',
    responsive: ['md'],
  },
  {
    title: '位置',
    dataIndex: 'position',
    key: 'position',
    responsive: ['lg'],
  },
  {
    title: '操作',
    dataIndex: 'operate',
    key: 'operate',
    render: text => <a>{text}</a>,
  },
];
const data = [
  {
    key: '1',
    keyword: '关键词',
    seo: '百度',
    position: '第1页',
    operate: '查看快照',
  },
];
const App = () => <Table pagination={false} columns={columns} dataSource={data} />;
export default App;
