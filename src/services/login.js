import Request from '../utils/request';
import RequestUrl from '../utils/requestUrl';

export const login = async data => {
  return await Request({
    url: '/auth/',
    method: 'POST',
    data,
  });
};

export const getMeCompany = async data => {
  return await Request({
    url: '/company/me',
    method: 'Get',
    data,
  });
};
export const getUserInfo = async () => {
  return await Request({
    url: '/user',
    method: 'GET',
  });
};

export const getFunList = async () => {
  return await Request({
    url: '/user/func_list',
    method: 'GET',
  });
};

export const getCateById = async data => {
  const { id } = data;
  return await Request({
    url: `/category/${id}`,
    method: 'GET',
    data,
  });
};

export const getProductState = async data => {
  return await Request({
    url: '/product/stat',
    method: 'GET',
    data,
  });
};

export const getAllCate = async data => {
  return await RequestUrl({
    url: `http://static.huangye88.cn/js/apicat.js?v=${new Date().getTime()}`,
    method: 'GET',
    data,
  });
};
export const getMyAllCate = async data => {
  return await Request({
    url: '/v2/category/tree',
    method: 'GET',
    data,
  });
};
export const getAllCity = async data => {
  return await Request({
    url: '/v2/areas/all',
    method: 'GET',
    data,
  });
};

export const getCityByPid = async data => {
  const { id } = data;
  return await Request({
    url: `/areas/${id}`,
    method: 'GET',
    data,
  });
};
export const getChildCateById = async data => {
  const { id } = data;
  return await Request({
    url: `/category/${id}/subs`,
    method: 'GET',
    data,
  });
};
