import Request from '../utils/request';

export const updateCompany = async data => {
  let { cate, areaids } = data;
  console.log(areaids);
  cate = cate.map(item => {
    return `${item}`;
  });
  areaids = areaids.map(item => {
    return `${item}`;
  });
  return await Request({
    url: '/company',
    method: 'PUT',
    data: { ...data, areaids, cate },
  });
};

export const getMeCompany = async data => {
  return await Request({
    url: '/company/me',
    method: 'Get',
    params: data,
  });
};
export const getMeAiCompany = async data => {
  return await Request({
    url: '/aicaigou/me',
    method: 'Get',
    params: data,
  });
};

export const updateAiCompany = async data => {
  const response =  await Request({
    url: '/aicaigou/save',
    method: 'POST',
    data: { ...data },
  });
  return response;
};

export const bindKuyiso = async ({ mobile, password }) => {
  console.log(mobile, password);
  const response = await Request({
    url: `/merchant/bind/kuyiso`,
    method: 'POST',
    data: {
      mobile: parseInt(mobile, 10),
      password,
    },
  });
  return response;
};
