import Request from '@/utils/request';
import RequestFile from '@/utils/requestfile';
import RequestUrl from '../utils/requestUrl';
export const login = async (data: {}) => {
  console.log(data);
  return await Request({
    url: '/auth/',
    method: 'POST',
    data,
  });
};

export const uploadPic = async (data: any) => {
  return await RequestFile({
    url: '/sys/album/upload/' + data.id,
    method: 'POST',
    data: data.file,
  });
};

export const getUserInfo = async () => {
  return await Request({
    url: '/user',
    method: 'GET',
    data: {},
  });
};

export const queryCurrent = async () => {
  return await Request({
    url: '/sys/user',
    method: 'GET',
    data: {},
  });
};

export async function query(): Promise<any> {
  return Request({
    url: '/api/users',
    method: 'POST',
    data: {},
  });
}

export async function queryNotices(): Promise<any> {
  return Request({
    url: '/api/notices',
    method: 'POST',
    data: {},
  });
}

export const getAllCate = async () => {
  const response = await RequestUrl({
    url: `https://static.huangye88.cn/js/cat.js?v=${new Date().getTime()}`,
    method: 'GET',
    data: {},
  });
  return response;
};

export const getOverview = async () => {
  const response = await Request({
    url: '/user/overview',
    method: 'GET',
  });
  return response;
};
