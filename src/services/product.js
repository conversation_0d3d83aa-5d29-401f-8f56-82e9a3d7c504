import Request from '../utils/request';

export const updateProduct = async data => {
  const { product_id } = data;
  delete data.product_id;
  let { cate, areaids } = data;
  console.log(cate);
  console.log(areaids);
  cate =
    cate &&
    cate.map(item => {
      return `${item}`;
    });

  areaids =
    areaids &&
    areaids.map(item => {
      return `${item}`;
    });
  const body = { ...data, areaids, cate };
  for (const key in body) {
    if (body[key] === undefined || body[key] === null) {
      // eslint-disable-next-line no-param-reassign
      delete body[key];
    }
  }
  console.log(JSON.stringify(body));
  return await Request({
    url: `/product/${product_id}`,
    method: 'PUT',
    data: body,
  });
};
export const updateProductShop = async data => {
  const { product_id } = data;
  delete data.product_id;

  return await Request({
    url: `/product/${product_id}`,
    method: 'PUT',
    data: { ...data },
  });
};

export const updateKeywordProduct = async data => {
  const { product_id } = data;
  delete data.product_id;

  console.log(JSON.stringify({ ...data }));
  return await Request({
    url: `/product/${product_id}`,
    method: 'PUT',
    data: { ...data },
  });
};

export const ProductSimilar = async data => {
  return await Request({
    url: '/tools/similar',
    method: 'POST',
    data,
  });
};

export const getProductState = async data => {
  return await Request({
    url: '/product/stat',
    method: 'GET',
    data,
  });
};

export const getProductbyId = async data => {
  const { product_id } = data;
  delete data.product_id;
  return await Request({
    url: `/product/${product_id}`,
    method: 'GET',
    data,
  });
};

export const addProduct = async data => {
  delete data.created_at;
  delete data.status;
  delete data.updated_at;
  delete data.company_id;
  delete data.id;
  delete data.audit_res;
  delete data.modify_fields;
  let { cate, areaids } = data;
  if (cate != null) {
    cate = cate.map(item => {
      return `${item}`;
    });
  }

  areaids =
    areaids &&
    areaids.map(item => {
      return `${item}`;
    });

  return await Request({
    url: '/product',
    method: 'POST',
    data: { ...data, areaids, cate },
  });
};
export const optionProductById = async data => {
  const { product_id, opt } = data;

  return await Request({
    url: `/product/opt/${product_id}?opt=${opt}`,
    method: 'GET',
  });
};
export const getProductList = async data => {
  console.log(data);
  const { limit, offset, status } = data;
  if (status != undefined) {
    return await Request({
      url: `/product/query?limit=${limit}&offset=${offset}&status=${status}&count=1`,
      method: 'GET',
      data,
    });
  } else {
    return await Request({
      url: `/product/query?limit=${limit}&offset=${offset}&count=1`,
      method: 'GET',
      data,
    });
  }
};

export const getCateById = async data => {
  const { id } = data;
  return await Request({
    url: `/v2/category/${id}`,
    method: 'GET',
    data,
  });
};

export const getChildCateById = async data => {
  const { id } = data;
  return await Request({
    url: `/v2/category/${id}/subs`,
    method: 'GET',
    data,
  });
};

export const getCateProperty = async data => {
  const { id, merge } = data;
  return await Request({
    url: `/v2/category/${id}/properties?merge=${merge}`,
    method: 'GET',
    data,
  });
};

// 检查当前公司有哪些平台不支持这个城市
export const getCityNotSupportPlatform = async data => {
  const { cid, id } = data;
  delete data.cid;
  delete data.id;
  return await Request({
    url: `/v2/areas/check?id=${id}&cid=${cid}`,
    method: 'GET',
    data,
  });
};

// 检查当前公司有哪些平台不支持这个分类
export const getCateNotSupportPlatform = async data => {
  const { cid, id } = data;
  delete data.cid;
  delete data.id;
  return await Request({
    url: `/v2/category/check?id=${id}&cid=${cid}`,
    method: 'GET',
    data,
  });
};

export const getCityByPid = async data => {
  const { id } = data;
  return await Request({
    url: `/v2/areas/${id}`,
    method: 'GET',
    data,
  });
};
export const getCateByPid = async data => {
  const { id } = data;
  return await Request({
    url: `/v2/category/${id}`,
    method: 'GET',
    data,
  });
};
