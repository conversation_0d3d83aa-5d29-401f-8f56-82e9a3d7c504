/**
 * 挖掘： 关键词挖掘 和 素材挖掘
 */

import Request from '../utils/request';

export const getDigKeywords = async (data) => {
  return await Request({
    url: '/tools/dig/keywords',
    method: 'GET',
    params: data,
  });
};

export const getDigMaterials = async (data) => {
  return await Request({
    url: '/tools/dig/materials',
    method: 'GET',
    params: data,
  });
};

export const ask = async (data) => {
  return await Request({
    url: '/tools/chat/ask',
    method: 'GET',
    params: data,
  });
};

function parseChunkedData(chunkedData) {
  let result = '';
  let pos = 0;
  while (pos < chunkedData.length) {
    let sizeLineEnd = chunkedData.indexOf('\r\n', pos);
    let chunkSize = parseInt(chunkedData.slice(pos, sizeLineEnd), 16);
    if (chunkSize === 0) {
      break;
    }
    pos = sizeLineEnd + 2;
    result += chunkedData.slice(pos, pos + chunkSize);
    pos += chunkSize + 2;
  }
  console.log('parseChunkedData',result);
  return result;
}

export const askWithStream = async (data) => {
  return await Request.get(
     '/tools/chat/stream/ask',
    {
    method: 'GET',
    params: data,
      responseType:'arraybuffer',
      transformResponse: [function (data) {
        return parseChunkedData(new TextDecoder("utf-8").decode(new Uint8Array(data)));
      }]
  });
};

export const chatHistories= async (data) => {
  return await Request({
    url: '/tools/chat/histories',
    method: 'GET',
    params: data,
  });
};
