import Request from '../utils/request';


export const updateInfo = async (data) => {
  const { id } = data;
  delete data.id;
  return await Request({
    url: `/info/${id}`,
    method: 'PUT',
    data,
  });
};


export const addInfo = async (data) => {
  return await Request({
    url: '/info',
    method: 'POST',
    data,
  });
};
export const getInfoById = async (data) => {
  const { id } = data;
  delete data.id;
  return await Request({
    url: `/info/opt/${id}`,
    method: 'GET',
    params: data,
  });
};
export const getInfoList = async (data) => {
  const { keyword = '', pub_type = '', status = '', platform = '' } = data;
  const { limit, offset } = data;
  console.log(data);
  if (status == 5) {
    return await Request({
      url: encodeURI(`/info/items?limit=${limit}&offset=${offset}&keyword=${keyword}&pub_type=${pub_type}&status=2` + `&platform=${platform}&synced=1`),
      method: 'GET',
      data,
    });
  } else {
    return await Request({
      url: encodeURI(`/info/items?limit=${limit}&offset=${offset}&keyword=${keyword}&pub_type=${pub_type}&status=${status}&platform=${platform}`),
      method: 'GET',
      data,
    });
  }
};

export const getInfoDetailById = async (data) => {
  const { id } = data;
  delete data.id;
  return await Request({
    url: `/info/${id}`,
    method: 'GET',
    data,
  });
};

export const getInfoStatu = async (data) => {
  return await Request({
    url: '/info/stat',
    method: 'GET',
    data,
  });
};
