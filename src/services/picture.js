import Request from '../utils/request';



export const getPicturesByAlbumId = async(data) =>{
  const{id,pagination}=data
  delete data.id;
  return await Request({
     url: '/v2/album/'+id+'?limit='+pagination.pageSize+'&offset='+(pagination.current-1)*pagination.pageSize,
     method: 'GET',
     data
   });
 }
 export const getAlbums = async() =>{

  return await Request({
     url: '/v2/album',
     method: 'GET',
     data:{}
   });
 }
 export const getAlbumState = async() =>{

  return await Request({
     url: '/v2/album/stat',
     method: 'GET',
     data:{}
   });
 }


 export const addAlbums = async(data) =>{

  return await Request({
     url: '/v2/album',
     method: 'POST',
     data:{...data}
   });
 }

 export const updatePicName = async(data) =>{
  const{id}=data
  delete data.id;
  return await Request({
     url: '/v2/album/images/'+id,
     method: 'PUT',
     data:{...data}
   });
 }
 export const updateAlbums = async(data) =>{
  const{id}=data
  delete data.id;
  return await Request({
     url: '/v2/album/'+id,
     method: 'PUT',
     data:{...data}
   });
 }

 export const DeleteAlbums = async(data) =>{
  const{id}=data
  delete data.id;
  return await Request({
     url: '/v2/album/'+id,
     method: 'DELETE',
     data:{...data}
   });
 }
 export const DeletePics = async(data) =>{

  return await Request({
     url: '/v2/album/images',
     method: 'DELETE',
     data:{...data}
   });
 }
 export const movePics = async(data) =>{
  const {toId}=data;
  delete data.toId;
  return await Request({
     url: '/v2/album/images/moveto/'+toId,
     method: 'POST',
     data:{...data}
   });
 }
