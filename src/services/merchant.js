import Request from '../utils/request';
 
 


export const queryMerchant = async(data) =>{
 return await Request({
    url: '/merchant/query',
    method: 'GET',
    data
  });
}

export const updateMerchant = async(data) =>{
  const {id}=data;
  delete data.id;
  return await Request({
     url: '/merchant/'+id,
     method: 'PUT',
     data
   });
 }
 
 export const queryMerchantStatus = async(data) =>{
  return await Request({
     url: '/merchant/status',
     method: 'GET',
     data
   });
 }

