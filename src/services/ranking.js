import Request from '../utils/request';

 export const getRankHy88 = async data => {
  const { eg, page, count } = data;
  const response = await Request({
     url: `/ranks?eg=${eg}&page=${page}&count=${count}`,
     method: 'GET',
     data,
   });
  return response
 }

export const getRankOther = async data => {
  const { eg, pf, page, count } = data;
  const response = await Request({
    url: `/v2/ranks?eg=${eg}&page=${page}&count=${count}&pf=${pf}`,
    method: 'GET',
    data,
  });
  return response
}
