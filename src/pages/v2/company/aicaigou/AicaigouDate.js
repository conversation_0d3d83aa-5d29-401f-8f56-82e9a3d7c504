import React from 'react';

import { Form, Row, Col, Input, Button, DatePicker, Modal, message, Cascader, Select } from 'antd';
import styles from './AicaigouDate.less';
import UploadFile from '../../../../components/UploadFile';
import { connect } from 'dva';
import moment from 'moment/moment';
import ImgUpload from '@/components/ImgUpload';

// 选择日期
const { RangePicker } = DatePicker;

class AicaigouDate extends React.PureComponent {
  formRef = React.createRef();

  state = {
    company: {},
  };

  componentDidMount() {
    // 这个用于开关选项
    this.props.dispatch({ type: 'merchant/queryMerchant', payload: {} });
    this.props.dispatch({ type: 'home/getCity', payload: {} });
    this.loadData();
  }

  loadData = () => {
    this.props
      .dispatch({
        type: 'company/getMyAiCompany',
        payload: {},
      })
      .then(res => {
        this.setState({
          company: res,
        });
        const form = this.formRef.current;
        form.resetFields();
      })
      .catch(err => {
        console.log(err);
        message.error(err.msg);
      });
  };

  handleFailed = err => {
    message.error(err.errorFields[0].errors[0]);
  };

  handleSubmit = values => {
    const payload = {
      ...values,
      contract_begin_date: values.contract_date[0].format('YYYY-MM-DD'),
      contract_end_date: values.contract_date[1].format('YYYY-MM-DD'),
    };
    const { company_areaids } = values;
    if (company_areaids.length < 2) {
      Modal.error({
        content: '公司地址不能为空，且至少选到第二级',
        zIndex: 9999,
      });
      return;
    }
    const { bank_areaids } = values;
    if (bank_areaids.length < 2) {
      Modal.error({
        content: '银行地址不能为空，且至少选到第二级',
        zIndex: 9999,
      });
      return;
    }

    this.props
      .dispatch({
        type: 'company/changeAiCompany',
        payload,
      })
      .then(() => {
        message.success('保存成功');
      })
      .catch(err => {
        console.log(err);
        message.error(`保存失败,${err.msg}`);
      });
  };

  beforeUploadbusiness_img = file => {
    const isJPG = file.type === 'image/jpeg';
    if (!isJPG) {
      message.error('图片上传格式必须为jpg');
    } else if (
      file.name &&
      file.name.substr(file.name.lastIndexOf('.') + 1, file.name.length) != 'jpg'
    ) {
      message.error('图片名称后缀必须为.jpg');
      return false;
    }
    return isJPG;
  };

  onChangeLogo = img => {
    this.formRef.current.setFieldsValue({
      company_logo: img,
    });
  };

  onChangeLicense = img => {
    this.formRef.current.setFieldsValue({
      business_img: img,
    });
  };

  onChangeContractFile = img => {
    this.formRef.current.setFieldsValue({
      contract_file: img,
    });
  };

  render() {
    const { company = {} } = this.state;
    const { cityOptions } = this.props;
    console.log('company', company);
    const dateFormat = 'YYYY-MM-DD';
    return (
      <div className={styles.aicaigoubox}>
        <div className={styles.alltitle}>
          <span>爱采购</span>
        </div>
        <div className={styles.commoncont}>
          <Form
            ref={this.formRef}
            onFinish={this.handleSubmit}
            onFinishFailed={this.handleFailed}
            initialValues={{
              company_name: company.company_name,
              company_type: company.company_type || '企业',
              company_areaids:
                company && company.company_areaids && company.company_areaids[0] === '0'
                  ? []
                  : company.company_areaids,
              bank_areaids:
                company && company.bank_areaids && company.bank_areaids[0] === '0'
                  ? []
                  : company.bank_areaids,
              contract_date: [
                company.contract_begin_date
                  ? moment(company.contract_begin_date, dateFormat)
                  : null,
                company.contract_end_date ? moment(company.contract_end_date, dateFormat) : null,
              ],
              brank_name: company.brank_name,
              card_number: company.card_number,

              company_web: company.company_web,
              link_email: company.link_email,
              link_person: company.link_person,
              open_branch: company.open_branch,
              social_credit_code: company.social_credit_code,
              LinkPhone: company.LinkPhone,
              contract_file: company.contract_file,
              company_logo: company.company_logo,
              business_img: company.business_img,
            }}
          >
            <Row>
              <Col span={24}>
                <Form.Item
                  label={<span>公司名称</span>}
                  labelCol={{ span: 4 }}
                  wrapperCol={{ span: 20 }}
                  name="company_name"
                  rules={[
                    { required: true, message: '请输入公司名称' },
                    { max: 50, message: '最大长度为50个字符' },
                  ]}
                >
                  <Input placeholder="请输入公司名称" />
                </Form.Item>
              </Col>

              <Col span={24}>
                <Form.Item
                  label={<span>公司地址</span>}
                  labelCol={{ span: 4 }}
                  wrapperCol={{ span: 20 }}
                  name="company_areaids"
                  rules={[{ required: true, message: '请输选择公司地址' }]}
                >
                  <Cascader
                    options={cityOptions}
                    placeholder="全国"
                    defaultValue={company.company_areaids}
                    changeOnSelect
                  />
                  {/* 引入级联选择 */}
                </Form.Item>
              </Col>

              <Col span={24}>
                <Form.Item
                  label={<span>公司网址</span>}
                  labelCol={{ span: 4 }}
                  wrapperCol={{ span: 20 }}
                  name="company_web"
                  rules={[
                    { required: true, message: '请输入公司网址' },
                    {
                      pattern: /(((^https?:(?:\/\/)?)(?:[-;:&=\+\$,\w]+@)?[A-Za-z0-9.-]+(?::\d+)?|(?:www.|[-;:&=\+\$,\w]+@)[A-Za-z0-9.-]+)((?:\/[\+~%\/.\w-_]*)?\??(?:[-\+=&;%@.\w_]*)#?(?:[\w]*))?)$/,
                      message: '请输入有效的公司网址',
                    },
                  ]}
                >
                  <Input placeholder="请输入公司网址" />
                </Form.Item>
              </Col>

              <Col span={24}>
                <Form.Item
                  label={<span>统一社会信用代码</span>}
                  labelCol={{ span: 4 }}
                  wrapperCol={{ span: 20 }}
                  name="social_credit_code"
                  rules={[
                    { required: true, message: '请输入统一社会信用代码' },
                    {
                      max: 20,
                      pattern: /[^_IOZSVa-z\W]{2}\d{6}[^_IOZSVa-z\W]{10}$/g,
                      message: '请输入有效的统一社会信用代码',
                    },
                  ]}
                >
                  <Input placeholder="请输入统一社会信用代码" />
                </Form.Item>
              </Col>

              <Col span={24}>
                <Form.Item
                  label={<span>银行名称</span>}
                  labelCol={{ span: 4 }}
                  wrapperCol={{ span: 20 }}
                  name="brank_name"
                  rules={[
                    { required: true, message: '请输入银行名称' },
                    { max: 20, message: '最大长度为20个字符' },
                  ]}
                >
                  <Input placeholder="请输入银行名称" />
                </Form.Item>
              </Col>

              <Col span={24}>
                <Form.Item
                  label={<span>开户支行</span>}
                  labelCol={{ span: 4 }}
                  wrapperCol={{ span: 20 }}
                  name="open_branch"
                  rules={[
                    { required: true, message: '请输入开户支行' },
                    { max: 20, message: '最大长度为20个字符' },
                  ]}
                >
                  <Input placeholder="请输入开户支行" />
                </Form.Item>
              </Col>

              <Col span={24}>
                <Form.Item
                  label={<span>银行卡号</span>}
                  labelCol={{ span: 4 }}
                  wrapperCol={{ span: 20 }}
                  name="card_number"
                  rules={[
                    { required: true, message: '请输入银行卡号' },
                    {
                      max: 30,
                      message: '最大长度为30个字符',
                    },
                  ]}
                >
                  <Input placeholder="请输入银行卡号" />
                </Form.Item>
              </Col>

              <Col span={24}>
                <Form.Item
                  label={<span>开户行所在地</span>}
                  labelCol={{ span: 4 }}
                  wrapperCol={{ span: 20 }}
                  name="bank_areaids"
                  rules={[{ required: true, message: '请输选择开户行所在地' }]}
                >
                  <Cascader
                    options={cityOptions}
                    placeholder="全国"
                    defaultValue={company.bank_areaids}
                    changeOnSelect
                  />
                </Form.Item>
              </Col>

              <Col span={12}>
                <Form.Item
                  label={<span>公司类型</span>}
                  labelCol={{ span: 8 }}
                  wrapperCol={{ span: 16 }}
                  name="company_type"
                  rules={[{ required: true, message: '请输选择公司类型' }]}
                >
                  <Select
                    defaultValue="企业"
                    style={{ width: 120 }}
                    onChange={this.handleChangeCompanyType}
                  >
                    <Select.Option value="企业">企业</Select.Option>
                    <Select.Option value="个体工商户">个体工商户</Select.Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label={<span>合同有效期</span>}
                  labelCol={{ span: 8 }}
                  wrapperCol={{ span: 16 }}
                  name="contract_date"
                  rules={[{ required: true, message: '请输选择合同有效期' }]}
                >
                  <RangePicker format={dateFormat} />
                  {/* 引入日期选择 */}
                </Form.Item>
              </Col>

              <Col span={12}>
                <Form.Item
                  label={<span>联系人姓名</span>}
                  labelCol={{ span: 8 }}
                  wrapperCol={{ span: 16 }}
                  name="link_person"
                  rules={[
                    { required: true, message: '请输入联系人姓名' },
                    { max: 20, message: '最大长度为20个字符' },
                  ]}
                >
                  <Input placeholder="请输入联系人姓名" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label={<span>手机号</span>}
                  labelCol={{ span: 8 }}
                  wrapperCol={{ span: 16 }}
                  name="LinkPhone"
                  type="number"
                  rules={[
                    { required: true, message: '请输入手机号' },
                    {
                      len: 11,
                      pattern: /^1[0-9]{10}$/,
                      message: '请输入有效的手机号',
                    },
                  ]}
                >
                  <Input placeholder="请输入手机号" />
                </Form.Item>
              </Col>

              <Col span={24}>
                <Form.Item
                  label={<span>邮箱</span>}
                  labelCol={{ span: 4 }}
                  wrapperCol={{ span: 20 }}
                  name="link_email"
                  rules={[
                    { required: true, message: '请输入邮箱' },
                    {
                      type: 'email',
                      message: '请输入有效的邮箱',
                    },
                  ]}
                >
                  <Input placeholder="请输入邮箱" />
                </Form.Item>
              </Col>

              <Col span={8}>
                <Form.Item
                  label={<span>爱采购合同</span>}
                  labelCol={{ span: 12 }}
                  wrapperCol={{ span: 12 }}
                  name="contract_file"
                  rules={[{ required: true, message: '请上传爱采购合同' }]}
                  extra="格式为pdf，小于8M，请按顺序合并合同。"
                >
                  <UploadFile purpose={4} onChange={this.onChangeContractFile} />
                  {/* 引入上传文件组件 */}
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  label={<span>营业执照</span>}
                  labelCol={{ span: 8 }}
                  wrapperCol={{ span: 16 }}
                  name="business_img"
                  rules={[{ required: true, message: '请上传营业执照' }]}
                  extra="图片格式必须为jpg"
                >
                  <ImgUpload
                    purpose={3}
                    onChange={this.onChangeLicense}
                    extRequired={this.beforeUploadbusiness_img}
                  />
                  {/* 引入上传图片组件 */}
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  label={<span>LOGO</span>}
                  labelCol={{ span: 8 }}
                  wrapperCol={{ span: 16 }}
                  name="company_logo"
                  rules={[{ required: true, message: '请上传LOGO' }]}
                  extra="图片尺寸比例为170*170，格式必须为jpg。"
                >
                  <ImgUpload purpose={1} onChange={this.onChangeLogo} />
                </Form.Item>
              </Col>

              <Col span={24}>
                <Form.Item
                  wrapperCol={{
                    offset: 8,
                    span: 16,
                  }}
                >
                  <Button type="primary" htmlType="submit">
                    确定
                  </Button>
                </Form.Item>
              </Col>
            </Row>
          </Form>
        </div>
      </div>
    );
  }
}

export default connect(({ home, company, product, merchant }) => ({
  ...company,
  ...home,
  ...product,
  ...merchant,
}))(AicaigouDate);
