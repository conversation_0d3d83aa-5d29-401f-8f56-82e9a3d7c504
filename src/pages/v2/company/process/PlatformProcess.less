.companybox {
  background: #fff;
}
.alltitle {
  height: 60px;
  padding-left: 20px;
  line-height: 60px;
  background: #fff;
  border-bottom: 1px solid #d7d7d7;
}
.alltitle > span {
  display: inline-block;
  padding: 0 5px;
  color: #333;
  font-weight: bold;
  font-size: 18px;
  line-height: 58px;
  border-bottom: 2px solid #1890ff;
}
.commoncont {
  padding: 20px;
}

.must {
  color: #f00;
  font-size: 12px;
}
.avatar-uploader > .ant-upload {
  width: 128px;
  height: 128px;
}
.checkradio {
  width: 50%;
}
.border {
  border: 1px solid #e6e6e6;
  border-bottom: 0 none;
}
.border .firstline {
  height: 40px;
  line-height: 40px;
  text-indent: 10px;
  background: #f6f6f6;
  border-bottom: 1px solid #e6e6e6;
}
.border > col {
  text-indent: 10px;
  border-right: 1px solid #e6e6e6;
  border-bottom: 1px solid #e6e6e6;
}
.border > col:last-of-type {
  border-bottom: 0 none;
}
.border > col:nth-child(2n) {
  border-right: 0 none;
}
.pass {
  margin-right: 10px;
  margin-left: 10px;
  color: #1890ff;
  font-size: 14px;
}
.fail {
  margin-right: 10px;
  margin-left: 10px;
  color: #f00;
  font-size: 14px;
}
.inprogress {
  margin-right: 10px;
  margin-left: 10px;
  color: #1890ff;
  font-size: 14px;
}
.notstarted {
  margin-right: 10px;
  margin-left: 10px;
  color: #999;
  font-size: 14px;
}
.notstarted > i {
  display: inline-block;
  width: 24px;
  height: 24px;
  margin-right: 5px;
  color: #999;
  font-size: 14px;
  font-style: normal;
  line-height: 24px;
  text-align: center;
  text-indent: 0;
  background: #f6f6f6;
  border-radius: 24px;
}
.lineheight {
  height: 40px;
  line-height: 40px;
  text-indent: 10px;
  border-right: 1px solid #e6e6e6;
  border-bottom: 1px solid #e6e6e6;
}
.lineheight:nth-child(2n) {
  border-right: 0 none;
}
.iconstyle {
  position: relative;
  top: 2px;
  margin-right: 5px;
  font-size: 18px;
}
