import React from 'react';

import { Row, Col, Space } from 'antd';
import {
  DoubleRightOutlined,
  CheckCircleTwoTone,
  CloseCircleTwoTone,
  ClockCircleTwoTone,
} from '@ant-design/icons';
import { connect } from 'dva';
import router from 'umi/router';
import styles from './PlatformProcess.less';

class PlatformProcess extends React.PureComponent {
  componentDidMount() {
    this.props.dispatch({ type: 'merchant/getMerchantStatus', payload: {} });
  }


  stepClick = action => {
    if (action === 3) {
      router.push({ pathname: '/company/aicaigou' });
    } else if (action === 1 || action === 2) {
      router.push({ pathname: '/company/detail' });
    }
  };

  render() {
    const { merchantStatusList = [] } = this.props;
    const stepStyles = {
      error: styles.fail,
      finish: styles.pass,
      wait: styles.notstarted,
      process: styles.inprogress,
    }

    return (
      <div className={styles.companybox}>
        <div className={styles.alltitle}>
          <span>平台开通进度</span>
        </div>
        <div className={styles.commoncont}>
          <Row className={styles.border}>
            <Col span={6} className={styles.firstline}>
              平台名称
            </Col>
            <Col span={18} className={styles.firstline}>
              开通进度
            </Col>

            {/* eslint-disable-next-line max-len,react/no-array-index-key */}
            {merchantStatusList.map((item, index) => (<React.Fragment key={index}>
              <Col span={6} className={styles.lineheight}>
                {item.name}
              </Col>
              <Col span={18} className={styles.lineheight} >
                {item.steps.map((step, idx) => (<React.Fragment> {idx > 0 && <Space>
                  <DoubleRightOutlined twoToneColor="#999"/>
                </Space>}
                  <span className={stepStyles[step.status]} onClick={() => {
                    this.stepClick(step.action);
                  }}>
  {step.status === 'wait' && <i>{idx + 1}</i>}
                    {step.status === 'finish' &&
                      <CheckCircleTwoTone twoToneColor="#52c41a" className={styles.iconstyle}/>}
                    {step.status === 'error' &&
                      <CloseCircleTwoTone twoToneColor="#ff0000" className={styles.iconstyle}/>}
                    {step.status === 'process' &&
                      <ClockCircleTwoTone twoToneColor="#ff0000" className={styles.iconstyle}/>}
                    {step.title}{step.status === 'error' && <React.Fragment>, {step.reason}</React.Fragment>}
</span>
                </React.Fragment>))}

              </Col></React.Fragment>))}
          </Row>
        </div>
      </div>
    );
  }
}

export default connect(({ merchant }) => ({
  ...merchant,
}))(PlatformProcess);
