import React from 'react';

import {
  Form,
  Row,
  Col,
  Input,
  Select,
  Radio,
  Button,
  Modal,
  message,
  Alert,
  Cascader,
  DatePicker,
} from 'antd';
import { connect } from 'dva';
import moment from 'moment/moment';
import styles from './CompanyDate.less';
import ImgUpload from '@/components/ImgUpload';

// 文本域
const { TextArea } = Input;

class CompanyDate extends React.PureComponent {
  // eslint-disable-next-line react/sort-comp
  formRef = React.createRef();

  state = {
    areaidsTip: '',
    cateTip: '',
    isCateLast: true,
    companyDetail: {},
  };

  // businessOptions = ["数据库管理及技术开发","计算机系统分析","计算机技术咨询","计算机软、硬件的技术开发、系统集成及销售","电子商务的技术开发","经营电子商务","计算机网络技术的研发"];
  workingModelOptions = ['生产型', '贸易型', '服务型', '政府', '其他机构'];

  companyTypeOptions = [
    '私营企业',
    '国有企业',
    '集体所有制企业',
    '合资企业',
    '外资企业',
    '股份企业',
    '个体经营',
    '事业单位',
    '社会团体',
    '个人',
    '其他',
  ];

  componentDidMount() {
    // 这个用于开关选项
    this.props.dispatch({ type: 'merchant/queryMerchant', payload: {} });
    this.props.dispatch({ type: 'home/getMyAllCate', payload: {} });
    this.props.dispatch({ type: 'home/getCity', payload: {} });
    this.loadData();
  }

  loadData = () => {
    this.props
      .dispatch({
        type: 'company/getMyCompany',
        payload: {},
      })
      .then(data => {
        this.setState({ companyDetail: data });
        const form = this.formRef.current;
        form.resetFields();
      })
      .catch(err => {
        console.log(err);
        message.error(err.msg);
      });
  };

  handleFailed = err => {
    message.error(err.errorFields[0].errors[0]);
  };

  handleSubmit = values => {
    console.log('values', values);
    const { features } = this.props;
    const { isCateLast } = this.state;
    const payload = {
      ...values,
    };
    if (payload.gender) {
      payload.gender = parseInt(payload.gender, 10);
    }
    if (features.validPeriod) {
      payload.valid_period = `${payload.valid_period_start.format('YYYY-MM-DD')} - ${
        payload.valid_period_end_type === '0'
          ? '永续经营'
          : payload.valid_period_end.format('YYYY-MM-DD')
      }`;
    }
    payload.reg_date = (payload.reg_date && payload.reg_date.format('YYYY-MM-DD')) || '';

    const { areaids } = values;
    if (areaids.length < 2) {
      Modal.error({
        content: '公司地址至少选到第二级',
        zIndex: 9999,
      });
      return;
    }
    if (!isCateLast) {
      Modal.error({
        content: '公司行业需选到最后一级',
        zIndex: 9999,
      });
      return;
    }

    this.props
      .dispatch({
        type: 'company/changeCompany',
        payload,
      })
      .then(data => {
        if (data.msg === 'success') {
          message.success('保存成功');
          this.loadData();
        } else {
          message.error(`保存失败,${data.msg}`);
        }
      })
      .catch(err => {
        message.error(`保存失败,${err.msg}`);
      });
  };

  onChangeLicense = img => {
    this.formRef.current.setFieldsValue({
      license: img,
    });
  };

  onChangeFront = img => {
    this.formRef.current.setFieldsValue({
      frontimage: img,
    });
  };

  onChangeBack = img => {
    this.formRef.current.setFieldsValue({
      image_back: img,
    });
  };

  onChangeHand = img => {
    this.formRef.current.setFieldsValue({
      handimage: img,
    });
  };

  onChangeLogo = img => {
    this.formRef.current.setFieldsValue({
      logo: img,
    });
  };

  handleCateChange = (value, selectedOptions) => {
    const { companyDetail } = this.props;
    if (!selectedOptions[selectedOptions.length - 1].isLeaf) {
      this.setState({ isCateLast: false });
      return;
    }
    this.props.dispatch({
      type: 'product/getCateNotSupportPlatform',
      payload: { id: value[value.length - 1], cid: companyDetail.id },
      callBack: platforms => {
        if (platforms && platforms.length > 0) {
          this.setState({
            cateTip: platforms.toString(),
          });
        } else {
          this.setState({
            cateTip: '',
            isCateLast: true,
          });
          this.formRef.current.setFieldsValue({
            cate: value,
          });
        }
      },
    });
  };

  onChangeCity = (value, selectedOptions) => {
    const { companyDetail } = this.props;
    if (!selectedOptions[selectedOptions.length - 1].isLeaf) {
      return;
    }

    this.props.dispatch({
      type: 'product/getCityNotSupportPlatform',
      payload: { id: value[value.length - 1], cid: companyDetail.id },
      callBack: data => {
        if (data && data.length > 0) {
          this.setState({
            areaidsTip: data.toString(),
          });
        } else {
          this.setState({
            areaidsTip: '',
          });
          this.formRef.current.setFieldsValue({
            areaids: value,
          });
        }
      },
    });
  };

  filter = (inputValue, path) =>
    path.some(option => option.label.toLowerCase().indexOf(inputValue.toLowerCase()) > -1);

  render() {
    const { features, cateOptions, cityOptions } = this.props;
    const { companyDetail } = this.state;
    const { can_edit: canEdit = false, reason } = companyDetail;
    return (
      <div className={styles.companybox}>
        <div className={styles.alltitle}>
          <span>公司资料</span>
        </div>
        <div className={styles.commoncont}>
          {!canEdit && <Alert type="info" description={reason} closable banner />}
          <Form
            ref={this.formRef}
            onFinish={this.handleSubmit}
            onFinishFailed={this.handleFailed}
            initialValues={{
              name: companyDetail.name,
              address: companyDetail.address,
              license: companyDetail.license,
              logo: companyDetail.logo,
              frontimage: companyDetail.frontimage,
              handimage: companyDetail.handimage,
              qrcode: companyDetail.qrcode,
              introduce: companyDetail.introduce,
              contact_name: companyDetail.contact_name,
              cate: companyDetail.cate,
              areaids: companyDetail.areaids,
              gender: companyDetail.gender ? `${companyDetail.gender}` : '1',
              id_card_no: companyDetail.id_card_no,
              phone: companyDetail.phone ? companyDetail.phone[0] : '',
              qq: companyDetail.qq,
              main_product: companyDetail.main_product,
              reg_no: companyDetail.reg_no,
              legal: companyDetail.legal,
              corp_type: companyDetail.corp_type,
              reg_addr: companyDetail.reg_addr,
              reg_money: companyDetail.reg_money,
              reg_date: companyDetail.reg_date ? moment(companyDetail.reg_date, 'YYYY-MM-DD') : '',
              valid_period_start: companyDetail.valid_period
                ? moment(companyDetail.valid_period.split(' - ')[0], 'YYYY-MM-DD')
                : '',
              valid_period_end: companyDetail.valid_period
                ? companyDetail.valid_period.indexOf('永续经营') == -1
                  ? moment(companyDetail.valid_period.split(' - ')[1], 'YYYY-MM-DD')
                  : ''
                : '',
              valid_period_end_type:
                companyDetail.valid_period && companyDetail.valid_period.indexOf('永续经营') > -1
                  ? '0'
                  : '1',
              business: companyDetail.business,
              reg_authority: companyDetail.reg_authority,
              email: companyDetail.email,
              main_brand: companyDetail.main_brand,
              company_type: companyDetail.company_type,
              short_name: companyDetail.short_name,
              working_model: companyDetail.working_model,
            }}
          >
            <Row>
              <Col span={24}>
                <Form.Item
                  label={<span>公司名称</span>}
                  labelCol={{ span: 4 }}
                  wrapperCol={{ span: 20 }}
                  name="name"
                  rules={[
                    { required: true, message: '请输入公司名称' },
                    { max: 50, message: '最大长度为50个字符' },
                  ]}
                >
                  <Input placeholder="请输入公司名称" disabled={!canEdit} />
                </Form.Item>
              </Col>

              <Col span={24}>
                <Form.Item
                  label={<span>公司地址</span>}
                  labelCol={{ span: 4 }}
                  wrapperCol={{ span: 20 }}
                  name="areaids"
                  rules={[{ required: true, message: '请输入公司地址' }]}
                >
                  <Cascader
                    options={cityOptions}
                    onChange={this.onChangeCity}
                    defaultValue={companyDetail.areaids}
                    placeholder="全国"
                    changeOnSelect
                    disabled={!canEdit}
                    getPopupContainer={triggerNode => triggerNode.parentNode}
                  />
                  {this.state.areaidsTip && this.state.areaidsTip.length > 0 && (
                    <Alert
                      type="warning"
                      description={`当前选择城市无法发布到以下平台：${this.state.areaidsTip}`}
                      closable
                      banner
                    />
                  )}
                </Form.Item>
              </Col>

              <Col span={24}>
                <Form.Item
                  label={<span>公司行业</span>}
                  labelCol={{ span: 4 }}
                  wrapperCol={{ span: 20 }}
                  name="cate"
                  rules={[{ required: true, message: '请输选择公司行业' }]}
                >
                  <Cascader
                    options={cateOptions}
                    onChange={this.handleCateChange}
                    changeOnSelect
                    defaultValue={companyDetail.cate}
                    placeholder="分类需要选择到最后一级"
                    disabled={!canEdit}
                    showSearch={this.filter}
                    getPopupContainer={triggerNode => triggerNode.parentNode}
                  />
                  {this.state.cateTip && this.state.cateTip.length > 0 && (
                    <Alert
                      type="warning"
                      description={`当前选择分类无法发布到以下平台：${this.state.cateTip}`}
                      closable
                      banner
                    />
                  )}
                </Form.Item>
              </Col>

              <Col span={24}>
                <Form.Item
                  label={<span>主营产品</span>}
                  labelCol={{ span: 4 }}
                  wrapperCol={{ span: 20 }}
                  name="main_product"
                  rules={[
                    { required: true, message: '请输入主营产品' },
                    { max: 50, message: '最大长度为50个字符' },
                  ]}
                >
                  <Input placeholder="请输入主营产品" disabled={!canEdit} />
                </Form.Item>
              </Col>

              <Col span={24}>
                <Form.Item
                  label={<span>公司介绍</span>}
                  labelCol={{ span: 4 }}
                  wrapperCol={{ span: 20 }}
                  name="introduce"
                  rules={[
                    { required: true, message: '请输入主营产品' },
                    { max: 800, message: '最大长度为800个字符' },
                    { min: 50, message: '最小长度为50个字符' },
                  ]}
                >
                  <TextArea rows={4} placeholder="请输入公司介绍" disabled={!canEdit} />
                </Form.Item>
              </Col>

              <Col span={12}>
                <Form.Item
                  label={<span>营业执照</span>}
                  labelCol={{ span: 8 }}
                  wrapperCol={{ span: 16 }}
                  name="license"
                  rules={[{ required: true, message: '请上传营业执照' }]}
                >
                  <ImgUpload purpose={1} onChange={this.onChangeLicense} disabled={!canEdit} />
                  {/* 引入上传图片组件 */}
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label={<span>LOGO</span>}
                  labelCol={{ span: 8 }}
                  wrapperCol={{ span: 16 }}
                  name="logo"
                  rules={[{ required: true, message: '请上传LOGO' }]}
                >
                  <ImgUpload purpose={0} onChange={this.onChangeLogo} disabled={!canEdit} />
                  {/* 引入上传图片组件 */}
                </Form.Item>
              </Col>

              {features.imageFront && (
                <Col span={12}>
                  <Form.Item
                    label={<span>联系人身份证人像面</span>}
                    labelCol={{ span: 8 }}
                    wrapperCol={{ span: 16 }}
                    name="frontimage"
                    rules={[{ required: true, message: '请上传身份证照' }]}
                  >
                    <ImgUpload purpose={5} onChange={this.onChangeFront} disabled={!canEdit} />
                    {/* 引入上传图片组件 */}
                  </Form.Item>
                </Col>
              )}
              {features.imageHand && (
                <Col span={12}>
                  <Form.Item
                    label={<span>联系人手持身份证照片</span>}
                    labelCol={{ span: 8 }}
                    wrapperCol={{ span: 16 }}
                    name="handimage"
                    rules={[{ required: true, message: '请上传联系人手持身份证照片' }]}
                  >
                    <ImgUpload purpose={6} onChange={this.onChangeHand} disabled={!canEdit} />
                  </Form.Item>
                </Col>
              )}

              <Col span={12}>
                <Form.Item
                  label={<span>联系人姓名</span>}
                  labelCol={{ span: 8 }}
                  wrapperCol={{ span: 16 }}
                  name="contact_name"
                  rules={[
                    { required: true, message: '请输入联系人姓名' },
                    { max: 20, message: '最大长度为20个字符' },
                  ]}
                >
                  <Input placeholder="请输入联系人姓名" disabled={!canEdit} />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label={<span>手机号</span>}
                  labelCol={{ span: 8 }}
                  wrapperCol={{ span: 16 }}
                  name="phone"
                  rules={[
                    { required: true, message: '请输入手机号' },
                    {
                      len: 11,
                      pattern: /^1[0-9]{10}$/,
                      message: '请输入有效的手机号',
                    },
                  ]}
                >
                  <Input placeholder="请输入手机号" />
                </Form.Item>
              </Col>

              {features.gender && (
                <Col span={12}>
                  <Form.Item
                    label={<span>性别</span>}
                    labelCol={{ span: 8 }}
                    wrapperCol={{ span: 16 }}
                    name="gender"
                  >
                    <Select
                      defaultValue="1"
                      disabled={!canEdit}
                      style={{
                        width: 120,
                      }}
                      options={[
                        {
                          value: '1',
                          label: '男',
                        },
                        {
                          value: '2',
                          label: '女',
                        },
                      ]}
                    />
                  </Form.Item>
                </Col>
              )}
              {features.idCardN && (
                <Col span={12}>
                  <Form.Item
                    label={<span>身份证号</span>}
                    labelCol={{ span: 8 }}
                    wrapperCol={{ span: 16 }}
                    name="id_card_no"
                    rules={[{ required: true, message: '请输入身份证号' }]}
                  >
                    <Input placeholder="请输入身份证号" disabled={!canEdit} />
                  </Form.Item>
                </Col>
              )}

              <Col span={12}>
                <Form.Item
                  label={<span>邮箱</span>}
                  labelCol={{ span: 8 }}
                  wrapperCol={{ span: 16 }}
                  name="email"
                  rules={[
                    { required: true, message: '请输入邮箱' },
                    {
                      type: 'email',
                      message: '请输入有效的邮箱',
                    },
                  ]}
                >
                  <Input placeholder="请输入邮箱" disabled={!canEdit} />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label={<span>QQ号</span>}
                  labelCol={{ span: 8 }}
                  wrapperCol={{ span: 16 }}
                  name="qq"
                  rules={[
                    { required: true, message: '请输入QQ号' },
                    { max: 20, message: '最大长度为20个字符' },
                  ]}
                >
                  <Input placeholder="请输入QQ号" disabled={!canEdit} />
                </Form.Item>
              </Col>

              {features.address && (
                <Col span={12}>
                  <Form.Item
                    label={<span>公司详细地址</span>}
                    labelCol={{ span: 8 }}
                    wrapperCol={{ span: 16 }}
                    name="address"
                    rules={[{ required: true, message: '请输入公司详细地址' }]}
                  >
                    <Input placeholder="请输入公司详细地址" disabled={!canEdit} />
                  </Form.Item>
                </Col>
              )}
              {features.legal && (
                <Col span={12}>
                  <Form.Item
                    label={<span>法定代表人</span>}
                    labelCol={{ span: 8 }}
                    wrapperCol={{ span: 16 }}
                    name="legal"
                    rules={[
                      { required: true, message: '请输入法定代表人' },
                      { max: 20, message: '最大长度为20个字符' },
                    ]}
                  >
                    <Input placeholder="请输入法定代表人" disabled={!canEdit} />
                  </Form.Item>
                </Col>
              )}

              {features.regNo && (
                <Col span={12}>
                  <Form.Item
                    label={<span>统一社会信用代码</span>}
                    labelCol={{ span: 8 }}
                    wrapperCol={{ span: 16 }}
                    name="reg_no"
                    rules={[{ required: true, message: '请输入统一社会信用代码' }]}
                  >
                    <Input placeholder="请输入统一社会信用代码" disabled={!canEdit} />
                  </Form.Item>
                </Col>
              )}
              {features.regAuthority && (
                <Col span={12}>
                  <Form.Item
                    label={<span>发照机关</span>}
                    labelCol={{ span: 8 }}
                    wrapperCol={{ span: 16 }}
                    name="reg_authority"
                    rules={[{ required: true, message: '请输入发照机关' }]}
                  >
                    <Input placeholder="请输入发照机关" disabled={!canEdit} />
                  </Form.Item>
                </Col>
              )}

              {features.regAddress && (
                <Col span={12}>
                  <Form.Item
                    label={<span>注册地址</span>}
                    labelCol={{ span: 8 }}
                    wrapperCol={{ span: 16 }}
                    name="reg_addr"
                    rules={[{ required: true, message: '请输入注册地址' }]}
                  >
                    <Input placeholder="请输入注册地址" disabled={!canEdit} />
                  </Form.Item>
                </Col>
              )}
              {features.regDate && (
                <Col span={12}>
                  <Form.Item
                    label={<span>注册时间（成立时间）</span>}
                    labelCol={{ span: 8 }}
                    wrapperCol={{ span: 16 }}
                    name="reg_date"
                    rules={[{ required: true, message: '请输入注册时间' }]}
                  >
                    <DatePicker disabled={!canEdit} format="YYYY-MM-DD" />
                  </Form.Item>
                </Col>
              )}

              {features.validPeriod && (
                <Col span={12}>
                  <Form.Item
                    label={<span>营业期限开始时间</span>}
                    labelCol={{ span: 8 }}
                    wrapperCol={{ span: 16 }}
                    name="valid_period_start"
                    rules={[{ required: true, message: '请选择营业期限开始时间' }]}
                  >
                    <DatePicker disabled={!canEdit} format="YYYY-MM-DD" />
                  </Form.Item>
                </Col>
              )}
              {features.validPeriod && (
                <Col span={12}>
                  <Form.Item
                    label={<span>营业期限结束时间</span>}
                    labelCol={{ span: 8 }}
                    wrapperCol={{ span: 16 }}
                    name="valid_period_end_type"
                    rules={[{ required: true, message: '请选择营业期限结束时间' }]}
                  >
                    <Radio.Group>
                      <Radio value="0">永续经营</Radio>
                      <Radio value="1">
                        <Form.Item name="valid_period_end">
                          <DatePicker disabled={!canEdit} format="YYYY-MM-DD" />
                        </Form.Item>
                      </Radio>
                    </Radio.Group>
                  </Form.Item>
                </Col>
              )}

              {features.shortName && (
                <Col span={12}>
                  <Form.Item
                    label={<span>公司简称</span>}
                    labelCol={{ span: 8 }}
                    wrapperCol={{ span: 16 }}
                    name="short_name"
                    rules={[{ required: true, message: '请输入公司简称' }]}
                  >
                    <Input placeholder="请输入公司简称" disabled={!canEdit} />
                  </Form.Item>
                </Col>
              )}
              {features.shortName && (
                <Col span={12}>
                  <Form.Item
                    label={<span>主营品牌</span>}
                    labelCol={{ span: 8 }}
                    wrapperCol={{ span: 16 }}
                    name="main_brand"
                    rules={[{ required: true, message: '请输入主营品牌' }]}
                  >
                    <Input placeholder="请输入主营品牌" disabled={!canEdit} />
                  </Form.Item>
                </Col>
              )}

              {features.companyType && (
                <Col span={12}>
                  <Form.Item
                    label={<span>公司性质</span>}
                    labelCol={{ span: 8 }}
                    wrapperCol={{ span: 16 }}
                    name="company_type"
                    rules={[{ required: true, message: '请选择公司性质' }]}
                  >
                    <Select
                      defaultValue={this.companyTypeOptions[0]}
                      value={this.state.company_type}
                      disabled={!canEdit}
                    >
                      {this.companyTypeOptions.map(item => (
                        <Select.Option value={item}>{item}</Select.Option>
                      ))}
                    </Select>
                  </Form.Item>
                </Col>
              )}
              {features.companyType && (
                <Col span={12}>
                  <Form.Item
                    label={<span>经营模式</span>}
                    labelCol={{ span: 8 }}
                    wrapperCol={{ span: 16 }}
                    name="working_model"
                    rules={[{ required: true, message: '请选择经营模式' }]}
                  >
                    <Select
                      defaultValue={this.workingModelOptions[0]}
                      value={this.state.working_model}
                      disabled={!canEdit}
                    >
                      {this.workingModelOptions.map(item => (
                        <Select.Option value={item}>{item}</Select.Option>
                      ))}
                    </Select>
                  </Form.Item>
                </Col>
              )}

              {features.companyType && (
                <Col span={12}>
                  <Form.Item
                    label={<span>经营范围</span>}
                    labelCol={{ span: 8 }}
                    wrapperCol={{ span: 16 }}
                    name="business"
                    rules={[{ required: true, message: '请输入经营范围' }]}
                  >
                    <Input placeholder="请输入经营范围" disabled={!canEdit} />
                  </Form.Item>
                </Col>
              )}
              <Col span={12}></Col>

              <Col span={24}>
                <Form.Item
                  wrapperCol={{
                    offset: 8,
                    span: 16,
                  }}
                >
                  <Button type="primary" htmlType="submit">
                    确定
                  </Button>
                </Form.Item>
              </Col>
            </Row>
          </Form>
        </div>
      </div>
    );
  }
}

export default connect(({ company, merchant, home }) => ({
  ...company,
  ...merchant,
  ...home,
}))(CompanyDate);
