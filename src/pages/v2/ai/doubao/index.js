import React from 'react';

import { Row, Col, Input, Button, Modal } from 'antd';
import { connect } from 'dva';
import styles from './index.less';
import { baseUrl } from '@/configs/config';

class DoubaoBot extends React.PureComponent {
  state = {
    tip: {
      total: 0,
      num: 0,
      searched: false,
      show: false,
    },
    history: [], // 用本地的localstorage
    examples: [
      {
        label: '学习',
        text: '圆周率第88位是多少？',
      },
      {
        label: '编程',
        text: `优化代码，简化代码:"func insertionSort(nums [int)[int {if len(nums)<= 1{
return nums
for i := 0; i < len(nums); i++{
//每次从未排序区间取一个数据valuevalue
:= nums[i]
在已排序区间找到插入位置j :=i- 1
for; j >= 0; j--{
//如果比 value大后移if
nums[j]> value
Unknown macro: {nums[j+1]= nums[j]}
else
Unknown macro: { break}
}
//插入数据valuenums[j+1]=
value
return nums
}”`,
      },
      {
        label: '数据分析',
        text: '写个excel公式,如果单元格是空，返回空，如果非空，返回1',
      },
      {
        label: '文案',
        text: '写出5段关于AI智能的介绍',
      },
      {
        label: '翻译',
        text: '勿忘初心翻译成英文',
      },
      {
        label: '总结',
        text:
          '一句话描述这段摘要：科研团队对我国西北地区约1.7亿年前的一种侏罗纪远古植物化石进行了重新研究。这种植物此前被认为是裸子植物，名为美丽镰鳞果。最新研究中，科研团队运用显微CT技术对这种古植物化石进行扫描，发现化石内部包含有双层珠被的倒生胚珠，这是判断被子植物的关键特征。基于此，科研团队判断这是一种远古被子植物。由于化石中展现的是这种植物的多个相连果实，科研团队将其重新命名为美丽青甘宁果序（ingganninginfructus formosa）。',
      },
    ], // 案例
    conversations: [
      { role: 'AI', class: 'ai', text: '您好，我是豆包智能问答机器人，您可以向我提问' },
    ],
    kw: '',
    loading: false,
    selected: '',
  };

  // 用于获取需要滚动的元素的引用
  messagesEndRef = React.createRef();

  componentDidMount() {}

  onSaveBtn = () => {
    this.props.onSave(this.state.selected);
    this.notifyClose();
  };

  didSearch = () => {
    this.doSearch({ kw: this.state.kw, type: 2 });
  };

  doSearch = payload => {
    this.setState({ loading: true });
    this.state.conversations.length = 0;
    this.state.conversations.push({ role: '我', class: 'user', text: payload.kw });
    let i = 0;
    let thinking = setInterval(() => {
      this.state.conversations.length = 1;
      this.state.conversations.push({
        role: 'AI',
        class: 'ai',
        text: '思考中' + '.'.repeat(i + 1),
      });
      i = (i + 1) % 3;
      let conversations = [].concat(...this.state.conversations);
      this.setState({ conversations });
    }, 500);
    this.props
      .dispatch({
        type: 'chat/ask',
        payload,
      })
      .then(data => {
        clearInterval(thinking);
        let content = data.data.content;
        // 更新次数信息
        this.setState({
          tip: {
            total: data.data.user_maxnum || 0,
            num: data.data.user_maxnum - data.data.user_total || 0,
            searched: true,
            show: true,
          },
        });
        this.state.conversations.length = 1;
        if (content) {
          this.state.conversations.push({
            role: 'AI',
            class: 'ai',
            text: content,
          });
          let conversations = [].concat(...this.state.conversations);
          this.setState({ searched: true, kw: '', conversations });

          this.state.history.unshift({
            q: { role: 'user', text: payload.kw },
            a: {
              role: 'ai',
              text: content,
            },
          });

          let history = [].concat(...this.state.history);
          if (history.length > 10) {
            history = history.slice(0, 10);
          }
          this.setState({ searched: true, kw: '', loading: false, history });
        } else {
          this.state.history.unshift({
            q: { role: 'user', text: payload.kw },
            a: {
              role: 'ai',
              text: '不太理解你的问题，请换个方式提问。',
            },
          });
        }
      })
      .catch(e => {
        clearInterval(thinking);
        // Modal.warning({ title: '提示', content: e.msg, zIndex: 9999 });
        let content = e.msg || e.message;
        if (content.includes('超限额')) {
          content += '，点击购买';
          Modal.warning({
            title: '提示',
            content,
            zIndex: 9999,
            onOk: () => {
              window.open('https://fafa.huangye88.com/tool/AIpay.html', '_blank');
            },
            okText: '去购买',
          });
        } else {
          Modal.warning({ title: '提示', content, zIndex: 9999 });
        }
        this.setState({ loading: false });
      });
  };

  doStreamSearch = payload => {
    this.setState({ loading: true });
    this.state.conversations.length = 0;
    this.state.conversations.push({ role: '我', class: 'user', text: payload.kw });
    let i = 0;
    const thinking = setInterval(() => {
      this.state.conversations.length = 1;
      this.state.conversations.push({
        role: 'AI',
        class: 'ai',
        text: `思考中${'.'.repeat(i + 1)}`,
      });
      i = (i + 1) % 3;
      const conversations = [].concat(...this.state.conversations);
      this.setState({ conversations });
    }, 500);
    const that = this;
    fetch(`${baseUrl}/tools/chat/stream/ask?kw=${encodeURIComponent(payload.kw)}`, {
      method: 'GET',
      headers: {
        Authorization: `Bearer ${window.sessionStorage.getItem('Authorization')}`,
      },
    })
      .then(response => response.body)
      .then(async body => {
        clearInterval(thinking);

        let text = '';
        let index = 0;
        let hasRead = false;
        const writer = setInterval(() => {
          if (index < text.length) {
            that.state.conversations.length = 1;
            const newline = text.substring(0, ++index);
            that.state.conversations.push({
              role: 'AI',
              class: 'ai',
              text: newline,
            });
            const conversations = [].concat(...that.state.conversations);
            that.setState({ searched: true, kw: '', conversations });
            if (newline.endsWith('\n') || index % 30 == 0) {
              that.scrollToBottom();
            }
          } else if (hasRead) {
            clearInterval(writer);
          }
        }, 100);
        const reader = body.getReader();
        while (true) {
          const { value, done } = await reader.read();
          if (done) break;
          const trunk = new TextDecoder('utf-8').decode(value);
          console.log(trunk);
          const lines = trunk.split('0\r\n');
          text += lines[0];
          try {
            const o = JSON.parse(text);
            if (o && o.code && o.msg) {
              text = '不太理解你的问题，请换个方式提问。';
              break;
            }
          } catch (e) {
            if (lines.length > 1) {
              break;
            }
          }
        }
        hasRead = true;

        that.state.history.unshift({
          q: { role: 'user', text: payload.kw },
          a: {
            role: 'ai',
            text: text || '不太理解你的问题，请换个方式提问。',
          },
        });
        const history = [].concat(...that.state.history);
        that.setState({ searched: true, kw: '', loading: false, history });
        that.scrollToBottom();
      })
      .catch(e => {
        console.error(e);
        clearInterval(thinking);
        //  <a href='https://fafa.huangye88.com/tool/AIpay.html' target='_blank'>充值</a>
        let content = e.msg || e.message;
        if (content.includes('超限额')) {
          content +=
            "，去<a href='https://fafa.huangye88.com/tool/AIpay.html' target='_blank'>购买</a>";
        }
        Modal.warning({ title: '提示', content, zIndex: 9999 });
        that.setState({ loading: false });
      });
  };

  loadChatHistories = () => {
    this.props
      .dispatch({
        type: 'chat/chatHistories',
        payload: { type: 0 },
      })
      .then(data => {
        const history = data.data.map(item => item.keyword);
        this.setState({ history });
      })
      .catch(e => {
        Modal.warning({ title: '提示', content: e.msg, zIndex: 9999 });
      });
  };

  notifyClose = () => {
    this.props.close();
  };

  selectQuestion = q => () => {
    this.setState({
      kw: q,
    });
  };

  showHistory = item => () => {
    this.setState({
      conversations: [item.q, item.a],
    });
  };

  handleChangeKey = e => {
    this.setState({
      kw: e.target.value,
    });
  };

  // 用于滚动到元素底部
  scrollToBottom = () => {
    this.messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
  };

  render() {
    const { history = [], kw, loading, examples, tip, conversations } = this.state;
    return (
      <div className={styles.Chatbox}>
        <div className={styles.alltitle}>
          <span>豆包</span>
          {tip.show && (
            <span className={styles.tip}>
              总次数{tip.total}次，还可提问{tip.num}次，
              <a href="https://fafa.huangye88.com/tool/AIpay.html" target="_blank">
                充值
              </a>
            </span>
          )}
        </div>
        <div className={styles.commoncont}>
          <Row>
            <Col span={18}>
              <div className={styles.chat}>
                <div className={styles.chatcontent}>
                  {conversations.map(item =>
                    item.class === 'ai' ? (
                      <div className={styles.airobot}>
                        <span className={styles.photo}>{item.role}</span>
                        <p className={styles.chattext}>{item.text}</p>
                      </div>
                    ) : (
                      <div className={styles.me}>
                        <span className={styles.photo}>{item.role}</span>
                        <p className={styles.chattext}>{item.text}</p>
                      </div>
                    ),
                  )}
                </div>
                <Row>
                  <Col span={20}>
                    <div className={styles.border}>
                      <Input.TextArea
                        placeholder="请输入问题"
                        rows={3}
                        value={this.state.kw}
                        onChange={this.handleChangeKey}
                      />
                    </div>
                  </Col>
                  <Col span={4}>
                    <Button
                      type="primary"
                      loading={loading}
                      onClick={this.didSearch}
                      className={styles.ask}
                    >
                      发送
                    </Button>
                  </Col>
                </Row>
              </div>
              <div className={styles.aifunction}>
                <div className={styles.ai_title}>AI机器人可以做什么？点击可查看例句</div>
                <div className={styles.function}>
                  {examples.map(item => (
                    <div className={styles.line} onClick={this.selectQuestion(item.text)}>
                      <i></i>
                      <span className={styles.subtxt}>{item.label}：</span>
                      <span className={styles.txt}>{item.text}</span>
                    </div>
                  ))}
                </div>
              </div>
            </Col>
            <Col span={6}>
              <div className={styles.chat_history}>
                <div className={styles.history_title}>历史聊天记录</div>
                <div className={styles.history_content}>
                  {history.map(item => (
                    <p>
                      <a onClick={this.showHistory(item)}>{item.q.text}</a>
                    </p>
                  ))}
                </div>
              </div>
            </Col>
          </Row>
        </div>
      </div>
    );
  }
}
export default connect(({}) => ({}))(DoubaoBot);
