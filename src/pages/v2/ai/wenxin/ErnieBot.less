.Chatbox {
  background: #fff;
}
.alltitle {
  height: 60px;
  padding-left: 20px;
  line-height: 60px;
  background: #fff;
  border-bottom: 1px solid #d7d7d7;
  display: flex;
  align-items: center;
}
.alltitle > span:first-child {
  display: inline-block;
  padding: 0 5px;
  color: #333;
  font-weight: bold;
  font-size: 18px;
  line-height: 58px;
  border-bottom: 2px solid #1890ff;
}
.alltitle .tip {
  margin-left: 15px;
  color: #52c41a;
  font-size: 14px;
  line-height: normal;
}
.commoncont {
  padding: 20px;
}
.commoncont > p {
  font-size: 16px;
  line-height: 50px;
  text-align: center;
}

.chat {
  border: 1px solid #e6e6e6;
}
.chatcontent {
  height: 480px;
  padding: 10px;
  overflow-y: auto;
  background: #f9f7f7;
}
.airobot {
  padding: 10px 0;
  overflow: hidden;
  text-align: left;
}
.airobot span.photo {
  float: left;
  width: 36px;
  height: 36px;
  color: #fff;
  font-size: 18px;
  line-height: 36px;
  text-align: center;
  background-color: #001529;
}

.airobot p.chattext {
  float: left;
  max-width: 500px;
  margin-left: 10px;
  padding: 5px 10px;
  color: #333;
  line-height: 26px;
  background: #fff;
  border-radius: 3px;
}
.me p.chattext {
  float: right;
  max-width: 500px;
  margin-right: 10px;
  padding: 5px 10px;
  color: #000;
  line-height: 26px;
  background: #fff;
  border-radius: 3px;
}
.me {
  padding: 10px 0;
  overflow: hidden;
  text-align: right;
}
.me span.photo {
  float: right;
  width: 36px;
  height: 36px;
  color: #fff;
  font-size: 18px;
  line-height: 36px;
  text-align: center;
  background-color: #001529;
}

.listbg {
  background: #f0f3f6;
  border-radius: 3px;
}
.listbg > span {
  color: #333;
  font-size: 16px;
}
.listbg > span > i {
  display: inline-block;
  width: 10px;
  height: 10px;
  margin: 0 10px;
  background: #708ca5;
  border-radius: 10px;
}
.history_title {
  height: 46px;
  color: #fff;
  font-weight: 600;
  font-size: 16px;
  line-height: 46px;
  text-indent: 10px;
  background: #7eaad4;
}
.history_content {
  overflow: hidden;
  background: #e8f0f7;
}
.history_content > p {
  display: flex;
  height: 56px;
  padding: 0 10px;
  line-height: 56px;
  border-bottom: 1px solid #fff;
}
.history_content p > a {
  flex: 1;
  overflow: hidden;
  color: #333;
  font-size: 14px;
  white-space: nowrap;
  text-decoration: none;
  text-overflow: ellipsis;
}
.border {
  background-color: #fff;
  border: 1px solid #1890ff;
}
.ask {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  padding: 0;
  color: #fff;
  font-size: 22px;
  background-color: #1890ff;
  border-radius: 5px;
}
p.ai,
p.user {
  display: inline-block;
  max-width: 90%;
  padding: 5px;
  color: #333;
  font-size: 16px;
  white-space: pre-line;
  border-radius: 5px;
}
p.ai {
  float: left;
  margin-left: 5px;
  background-color: #fff;
}
p.user {
  float: right;
  margin-right: 5px;
  background-color: #1890ff;
}

.aifunction {
  margin-top: 20px;
  overflow: hidden;
}
.ai_title {
  color: #333;
  font-weight: 600;
  font-size: 18px;
}
.function {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  width: 100%;
  padding: 10px 0;
}
.function .line {
  //flex: 0 0 calc(50% - 10px);
  width: 340px;
  box-sizing: border-box;
  height: 40px;
  margin: 0 10px 10px 0;
  overflow: hidden;
  color: #666;
  font-size: 14px;
  line-height: 40px;
  white-space: nowrap;
  text-overflow: ellipsis;
  background-color: #f0f3f6;
  border-radius: 5px;
}
.function .line > i {
  display: inline-block;
  width: 10px;
  height: 10px;
  margin: 0 10px;
  background: #708ca5;
  border-radius: 10px;
}
.function .line > span.subtxt {
  color: #000;
  font-weight: 600;
  font-size: 16px;
}
.function .line > span.txt {
  color: #666;
  text-decoration: none;
}
.chat_history {
  margin-left: 15px;
}
