import React from 'react';
import { Row, Col, Card, Typography, Table } from 'antd';
import styles from './HomeContent.less';
import { connect } from 'dva';
import router from "umi/router";

const img1 = require('@/assets/zs01.png');
const img2 = require('@/assets/zs02.png');
const img3 = require('@/assets/zs03.png');
const img4 = require('@/assets/zs04.png');

const { Title, Paragraph } = Typography;


class HomeContent extends React.PureComponent {
  componentDidMount() {
      this.props.dispatch({ type: 'merchant/queryMerchant', payload: {} });
      this.props.dispatch({ type: 'merchant/getMerchantStatus', payload: {} });
      this.props.dispatch({ type: 'home/getOverview', payload: {} });
  }

  goCompanyPage = () => {
    router.push('/company/detail');
  }

  goPublishPage = () => {
    router.push('/publish/products');
  }

  goRankPage = () => {
    router.push('/publish/ranking');
  }

  goHistoryPage = () => {
    router.push('/publish/history');
  }
  render() {
    const { overview, merchantStatus, functionList } = this.props;
    console.log(functionList);
    // 到期时间
    const columns = [
      { title: '功能名称', dataIndex: 'functionname', key: '1' },
      { title: '到期时间', dataIndex: 'expiration', key: '2' },
      {
        title: '操作',
        key: 'operation',
        render: () => <a href="https://fafa.huangye88.com/tool/" target="_blank" rel="noopener noreferrer">续费</a>,
      },
    ];

    const data = [
      {
        key: '1',
        functionname: '发布信息',
        expiration: functionList[0].expireTime,
      },
      {
        key: '2',
        functionname: 'AI机器人',
        expiration: functionList[0].expireTime,
      },
    ];

// 开通平台
    const columns2 = merchantStatus.map((item, key) => ({ title: item.name,
        dataIndex: item.name,
        key,
        render: text => (text === '已开通' ? <a>{text}</a> : <a href="https://fafa.huangye88.com/tool/" target="_blank" rel="noopener noreferrer">{text}</a>),
      }));
    const data2 = [{}];
      merchantStatus.map((item) => {
        data2[0][item.name] = item.enabled ? '已开通' : '去购买';
        return '';
      });
    return (
      <div>
        <div style={{ background: '#fff', padding: '20px', margin: '0 0 20px 0' }}>
          <Row>
            <Col span={6}>
              <div className={styles.product}>
                <div className={styles.product_icon}>
                  <i></i>产品
                </div>
                <div className={styles.show_date}>
                  <div className={styles.h_total}>
                    <p>{overview.product.total}</p>
                    <p>已添加产品数</p>
                  </div>
                  <div className={styles.h_used}>
                    <p>{overview.product.promotions}</p>
                    <p>推广中</p>
                  </div>
                </div>
              </div>
            </Col>
            <Col span={6}>
              <div className={styles.infomation}>
                <div className={styles.infomation_icon}>
                  <i></i>信息
                </div>
                <div className={styles.show_date}>
                  <div className={styles.h_total}>
                    <p>{overview.info.total}</p>
                    <p>发布信息总数</p>
                  </div>
                  <div className={styles.h_used}>
                    <p>{overview.info.yesterday}</p>
                    <p>昨日新增信息数</p>
                  </div>
                </div>
              </div>
            </Col>
            <Col span={6}>
              <div className={styles.ranking}>
                <div className={styles.ranking_icon}>
                  <i></i>排名
                </div>
                <div className={styles.show_date}>
                  <div className={styles.h_total}>
                    <p>{overview.rank.total}</p>
                    <p>总排名数量</p>
                  </div>
                  <div className={styles.h_used}>
                    <p>{overview.rank.hy_88}</p>
                    <p>黄页88排名数</p>
                  </div>
                </div>
              </div>
            </Col>
            <Col span={6}>
              <div className={styles.platform}>
                <div className={styles.platform_icon}>
                  <i></i>平台
                </div>
                <div className={styles.show_date}>
                  <div className={styles.h_total}>
                    <p>{overview.platform.activated}</p>
                    <p>已开通平台数量</p>
                  </div>
                  <div className={styles.h_used}>
                    <p>{overview.platform.total - overview.platform.activated}</p>
                    <p>还可开通</p>
                  </div>
                </div>
              </div>
            </Col>
          </Row>
        </div>
        <Row>
          <Col span={10}>
            <Typography className={styles.noticebox}>
              <Title level={3} className={styles.noticetitle}>
                通知
              </Title>
              <Paragraph className={styles.noticetext}>
                添加产品前请先阅读《<a href="http://www.huangye88.com/help/wentis_243.html" target="_blank" rel="noopener noreferrer">禁售产品总则</a>》、《
                <a href="http://www.huangye88.com/help/wentis_251.html" target="_blank" rel="noopener noreferrer">發發助手产品详情发布标准</a>》、《<a href="http://www.huangye88.com/help/fafazhushous_247.html" target="_blank" rel="noopener noreferrer">發發助手图片规则示例</a>
                》，严禁发布禁售产品。
              </Paragraph>
              <Paragraph className={styles.noticetext}>
                请不要插入与产品不相关的素材，如跨行业新闻、小说等;请遵守广告法，不夸大、虚假宣传;不使用他人图片进行推广。
              </Paragraph>
              <Paragraph className={styles.noticetext}>
                为保证您的推广效果，我们要求产品图片、关键词、素材内容需保持一致，否则将不会通过审核;同时我们还将对产品质量进行审核，请确保产品质量。
              </Paragraph>
              <Paragraph className={styles.noticetext}>
                發發助手是帮助客户解决B2B平台发布信息难的问题，编辑好产品内容后，系统即可生成信息并自动发布，可节省大量时间。發發助手目前已实现自动发布信息、群发B2B平台、
                AI机器人对话等功能，后续我们还将支持更多功能，感谢您的使用!
              </Paragraph>
            </Typography>
          </Col>
          <Col span={14}>
            <Row>
              <Col span={12}>
                <div className={styles.quick_cntry}>
                  <div className={styles.o_name}>快捷入口</div>
                  <Row>
                    <Col span={6}>
                      <Card bordered={false} onClick={this.goCompanyPage}>
                        <p className={styles.cardimg}>
                          <img src={img1} alt="" />
                        </p>
                        <p className={styles.cardtext}>公司资料</p>
                      </Card>
                    </Col>
                    <Col span={6}>
                      <Card bordered={false} onClick={this.goPublishPage}>
                        <p className={styles.cardimg}>
                          <img src={img2} alt="" />
                        </p>
                        <p className={styles.cardtext}>自动发布</p>
                      </Card>
                    </Col>
                    <Col span={6}>
                      <Card bordered={false} onClick={this.goRankPage}>
                        <p className={styles.cardimg}>
                          <img src={img3} alt="" />
                        </p>
                        <p className={styles.cardtext}>排名统计</p>
                      </Card>
                    </Col>
                    <Col span={6}>
                      <Card bordered={false} onClick={this.goHistoryPage}>
                        <p className={styles.cardimg}>
                          <img src={img4} alt="" />
                        </p>
                        <p className={styles.cardtext}>发布历史</p>
                      </Card>
                    </Col>
                  </Row>
                </div>
              </Col>

              <Col span={12}>
                <div className={styles.expiration_date}>
                  <div className={styles.o_name}>到期时间</div>
                  <Table
                    columns={columns}
                    dataSource={data}
                    pagination={false}
                    bordered
                    align="center"
                  />
                </div>
              </Col>
            </Row>
            <Row>
              <Col span={24}>
                <div className={styles.open_platform}>
                  <div className={styles.o_name}>开通平台</div>
                  <Table
                    columns={columns2}
                    dataSource={data2}
                    size="middle"
                    pagination={false}
                    bordered
                    align="center"
                  />
                </div>
              </Col>
            </Row>
          </Col>
        </Row>
      </div>
    );
  }
}
export default connect(({ home, merchant, info, product }) => ({
  ...home,
  ...merchant,
  ...info,
  ...product,
}))(HomeContent);
