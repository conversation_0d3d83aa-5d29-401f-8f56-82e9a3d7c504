.product {
  margin: 0 5px;
  color: #fff;
  background: #3165ff;
  border-radius: 5px;
}
.ranking {
  margin: 0 5px;
  color: #fff;
  background: #fda22a;
  border-radius: 5px;
}
.infomation {
  margin: 0 5px;
  color: #fff;
  background: #1fd6cf;
  border-radius: 5px;
}
.platform {
  margin: 0 5px;
  color: #fff;
  background: #f77e33;
  border-radius: 5px;
}

.product_icon {
  padding: 10px 20px;
  font-weight: bold;
  font-size: 16px;
  border-bottom: 1px solid #7091ff;
}
.infomation_icon {
  padding: 10px 20px;
  font-weight: bold;
  font-size: 16px;
  border-bottom: 1px solid #60e1dd;
}
.ranking_icon {
  padding: 10px 20px;
  font-weight: bold;
  font-size: 16px;
  border-bottom: 1px solid #febd69;
}
.platform_icon {
  padding: 10px 20px;
  font-weight: bold;
  font-size: 16px;
  border-bottom: 1px solid #f9a571;
}
.h_name > i {
  position: relative;
  top: 3px;
  display: inline-block;
  width: 20px;
  height: 20px;
  margin-right: 5px;
}
.product_icon > i {
  position: relative;
  top: 3px;
  display: inline-block;
  width: 20px;
  height: 20px;
  margin-right: 5px;
  background-image: url(../../../assets/products.png);
  background-size: 100% 100%;
}
.infomation_icon > i {
  position: relative;
  top: 3px;
  display: inline-block;
  width: 20px;
  height: 20px;
  margin-right: 5px;
  background-image: url(../../../assets/infomation.png);
  background-size: 100% 100%;
}
.ranking_icon > i {
  position: relative;
  top: 3px;
  display: inline-block;
  width: 20px;
  height: 20px;
  margin-right: 5px;
  background-image: url(../../../assets/kewordranking.png);
  background-size: 100% 100%;
}
.platform_icon > i {
  position: relative;
  top: 3px;
  display: inline-block;
  width: 20px;
  height: 20px;
  margin-right: 5px;
  background-image: url(../../../assets/platform.png);
  background-size: 100% 100%;
}

.show_date {
  display: flex;
  padding: 10px 0;
}

.h_total,
.h_used {
  flex: 1;
  font-size: 14px;
}
.product .show_date .h_total {
  border-right: 1px solid #7091ff;
}
.infomation .show_date .h_total {
  border-right: 1px solid #60e1dd;
}
.ranking .show_date .h_total {
  border-right: 1px solid #febd69;
}
.platform .show_date .h_total {
  border-right: 1px solid #f9a571;
}
.h_total > p,
.h_used > p {
  text-align: center;
}
.h_total > p:first-of-type,
.h_used > p:first-of-type {
  font-weight: bold;
  font-size: 16px;
}

.homebottom {
  display: flex;
  margin: 20px auto 0 auto;
}

.noticebox {
  height: 500px;
  padding: 20px;
  background: #fff;
}
.noticetitle {
  height: 30px;
  color: #333;
  font-size: 16px;
  line-height: 30px;
  text-align: center;
}
.noticetext {
  color: #666;
  font-size: 14px;
  line-height: 24px;
  text-indent: 28px;
}

.noticetext > a {
  color: #75bcff;
}

.quick_cntry,
.expiration_date {
  height: 260px;
  margin-left: 15px;
  background: #fff;
}
.open_platform {
  height: 220px;
  margin-top: 15px;
  margin-left: 15px;
  background: #fff;
}
.o_name {
  height: 60px;
  padding: 0 20px;
  color: #333;
  font-weight: bold;
  font-size: 16px;
  line-height: 60px;
  border-bottom: 1px solid #eee;
}

p.cardimg {
  display: inline-block;
  padding: 15px;
  background: #eef7fe;
  border-radius: 5px;
}
p.cardimg > img {
  width: 40px;
  height: 40px;
}
p.cardtext {
  color: #333;
  font-size: 14px;
  line-height: 40px;
  text-align: center;
}
