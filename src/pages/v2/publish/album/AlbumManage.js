import React from 'react';
import AlbumList from '@/components/AlbumList';
import PopAddalbum from '@/components/PopAddalbum';
import PopUploadimg from '@/components/PopUploadimg';
import { connect } from 'dva';
import styles from './AlbumManage.less';


class AlbumManage extends React.PureComponent {
  state = {
    AlbumsList: [],
  };

  componentDidMount() {
    this.refresh();
  }

  refresh= () => {
    const that = this;
    this.props.dispatch({
      type: 'picture/getAlbumsList',
payload: {},
callBack: AlbumsList => {
        that.setState({
          AlbumsList,
        })
      },
    });
  }


  render() {
    const { AlbumsList = [] } = this.state;


    return (
      <div className={styles.albumbox}>
        <div className={styles.alltitle}>
          <span>相册管理</span>
        </div>
        <div className={styles.commontcont}>
          <div className={styles.gallery_top}>
            <PopAddalbum onSuccess={this.refresh} />
            {/* 在此处使用新建相册弹框 */}
            <PopUploadimg />
            {/* 在此处使用上传图片弹框 */}
          </div>
          <AlbumList items={AlbumsList} onChanged={this.refresh} />
          {/* 在此处使用相册列表 */}
        </div>
      </div>
    );
  }
}

export default connect(({ picture }) => ({
  ...picture,
}))(AlbumManage);
