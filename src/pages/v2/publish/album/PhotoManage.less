.albumbox {
  background: #fff;
}
.alltitle {
  height: 60px;
  padding-left: 20px;
  line-height: 60px;
  background: #fff;
  border-bottom: 1px solid #d7d7d7;
}
.alltitle > span {
  display: inline-block;
  padding: 0 5px;
  color: #333;
  font-weight: bold;
  font-size: 18px;
  line-height: 58px;
  border-bottom: 2px solid #1890ff;
}

.commoncont {
  padding: 20px;
}
.gallery_top {
  margin: 0 auto;
  padding-bottom: 20px;
}
.chose_operate {
  display: flex;
  padding: 20px 0;
}
.chose_l,
.chose_r {
  flex: 1;
  font-size: 14px;
}
.chose_r{
    text-align:right;
}
.piturecont {
  margin: 0 auto;
  overflow: hidden;
}
.photolist {
  position: relative;
  max-width: 198px;
  border: 1px solid #e6e6e6;
  margin-right: 15px;
  margin-bottom: 15px;
  padding: 5px 5px 0 5px;
  float: left;
}
.photolist > div.photos {
  width: 188px;
  height: 150px;
  position: relative;
}
.photosoperate {
  position: absolute;
  top: 10px;
  left: 10px;
}
.photolist > div.photos > p {
  height: 30px;
  line-height: 30px;
  color: #333;
}
.chose_l {
  flex: 1;
  font-size: 14px;
}
.chose_l > span {
  float: left;
  margin-right: 10px;
}
.chose_l > span > font {
  color: #f00;
}
