import { Button, Modal, message, Select, Space, Pagination, Checkbox, Image } from 'antd';
import React from 'react';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { connect } from 'dva';
import PopAltername from '@/components/PopAltername';
import DeleteTip from '@/components/DeleteTip';
import MovephotoTip from '@/components/MovephotoTip';
import { router } from 'umi';
import PopUploadimg from '../../../../components/PopUploadimg';
import styles from './PhotoManage.less';

class PhotoManage extends React.PureComponent {
  state = {
    selected: [], // 选中的图片
    pagination: { current: 1, total: 20, pageSize: 20 }, // 分页
  };

  componentDidMount() {
    this.reloadData();
  }

  reloadData = () => {
    let { albumId } = this.props.match.params;
    albumId = parseInt(albumId, 10);
    const { pagination } = this.state;

    // 相册列表
    this.props.dispatch({
      type: 'picture/getAlbumsList',
      payload: {},
      callBack: albumsList => {
        const selectedItem = albumsList.filter(item => item.id === albumId)[0];
        if (selectedItem) {
          pagination.total = selectedItem.display_total;
          this.setState({
            pagination,
            albumsList,
          });
        }
      },
    });

    this.loadPictures();
  };

  loadPictures = () => {
    const { pagination } = this.state;
    let { albumId } = this.props.match.params;
    albumId = parseInt(albumId, 10);
    // 相册的图片列表
    this.props.dispatch({
      type: 'picture/getPicturesList',
      payload: { id: albumId, pagination },
      callBack: picList => {
        this.setState({
          pictures: picList,
        });
      },
    });
  };

  onAlbumChange = value => {
    router.push(`/publish/album/${value}`);
    this.props.match.params.albumId = value;
    this.reloadData();
  };

  onPicChange = values => {
    this.setState({ selected: values });
  };

  onChangePage = (page, pageSize) => {
    const { pagination } = this.state;
    pagination.current = page;
    pagination.pageSize = pageSize;
    this.loadPictures();
  };

  selectAll = () => {
    const { pictures } = this.state;
    this.setState({
      selected: pictures.map(item => item.id),
    });
  };

  deSelectAll = () => {
    this.setState({
      selected: [],
    });
  };

  reverseAll = () => {
    const { selected, pictures } = this.state;
    const newSelected = [];
    for (const pic of pictures) {
      if (selected.indexOf(pic.id) === -1) {
        newSelected.push(pic.id);
      }
    }
    this.setState({
      selected: newSelected,
    });
  };

  delSelPic = () => {
    const { selected } = this.state;
    let { albumId } = this.props.match.params;
    albumId = parseInt(albumId, 10);
    if (selected.length === 0) {
      Modal.error({
        title: '提示',
        content: '请勾选需要删除的图片',
        zIndex: 9999,
      });
      return;
    }
    this.props.dispatch({
      type: 'picture/DeletePics',
      payload: { from: albumId, ids: selected },
      callBack: () => {
        this.reloadData();
      },
    });
  };

  moveSelPic = toId => {
    const { selected } = this.state;
    let { albumId } = this.props.match.params;
    albumId = parseInt(albumId, 10);
    if (selected.length === 0 || toId === albumId) {
      return;
    }
    Modal.confirm({
      title: '移动照片',
      icon: <ExclamationCircleOutlined />,
      content: '确定移动照片到新的相册？',
      okText: '确定',
      okType: 'danger',
      cancelText: '取消',
      onOk: () => {
        this.props.dispatch({
          type: 'picture/movePics',
          payload: { from: albumId, ids: selected, toId },
          callBack: () => {
            this.reloadData();
          },
        });
      },
      onCancel() {
        console.log('Cancel');
      },
    });
  };

  render() {
    let { albumId } = this.props.match.params;
    albumId = parseInt(albumId, 10);
    const { albumsList = [], pictures = [], selected = [], pagination } = this.state;
    console.log('selected', selected);

    return (
      <div className={styles.albumbox}>
        <div className={styles.alltitle}>
          <span>相册管理</span>
        </div>
        <div className={styles.commoncont}>
          <div className={styles.gallery_top}>
            选择相册：
            <Space>
              <Select
                defaultValue={albumId}
                style={{
                  width: 120,
                }}
                onChange={this.onAlbumChange}
              >
                {albumsList.map(item => (
                  <Select.Option value={item.id}>{item.name}</Select.Option>
                ))}
              </Select>
            </Space>
            <PopUploadimg id={albumId} onChanged={() => this.reloadData()} />
            {/* 在此处使用上传图片弹框 */}
          </div>
          <div className={styles.piturecont}>
            <Checkbox.Group
              style={{
                width: '100%',
              }}
              onChange={this.onPicChange}
              value={selected}
            >
              {pictures &&
                pictures.map(item => (
                  <div className={styles.photolist}>
                    <div className={styles.photos}>
                      <Image width={188} height={120} src={item.url} />
                      <p>
                        <div
                          style={{
                            width: '160px',
                            overflow: 'hidden',
                            height: '30px',
                          }}
                        >
                          {item.name}
                          {/* 在此处使用修改名称弹框 */}
                          <PopAltername item={item} onSuccess={this.loadPictures} />
                        </div>
                      </p>
                    </div>
                    <div className={styles.photosoperate}>
                      <Checkbox value={item.id}></Checkbox>
                    </div>
                  </div>
                ))}
            </Checkbox.Group>
            {/* 在此处使用图片列表 */}
          </div>
          <div className={styles.chose_operate}>
            <div className={styles.chose_l}>
              <span>
                共<font>{pagination.total}</font>张图片
              </span>
              <Space>
                <Button onClick={this.selectAll}>全选</Button>
                <Button onClick={this.deSelectAll}>全不选</Button>
                <Button onClick={this.reverseAll}>反选</Button>
                {selected && <DeleteTip onOk={this.delSelPic} />}
                {/* 在此处引入删除提示组件 */}
                {albumsList && selected && (
                  <MovephotoTip items={albumsList} onOk={id => this.moveSelPic(id)} />
                )}
                {/* 在此处引入移动图片提示组件 */}
              </Space>
            </div>
            {/* 在此处使图片操作组件 */}
            <div className={styles.chose_r}>
              <Pagination
                total={pagination.total}
                pageSize={pagination.pageSize}
                onChange={this.onChangePage}
                showSizeChanger
                showQuickJumper
                showTotal={total => `总共 ${total} 张`}
              />
            </div>
          </div>
        </div>
      </div>
    );
  }
}

export default connect(({ picture }) => ({
  ...picture,
}))(PhotoManage);
