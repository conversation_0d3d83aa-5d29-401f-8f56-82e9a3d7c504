import React from 'react';

import { Form, Row, Col, Select, Button, Input, Modal, Table, Pagination } from 'antd';
import styles from './ReleaseHistory.less';
import { connect } from 'dva';
import ViewInfo from '@/components/ViewInfo';
import Error from '@/components/Error';

// select
const handleChange = value => {
  console.log(`selected ${value}`);
};

class ReleaseHistory extends React.PureComponent {
  state = {
    platform: '-1',
    status: '',
    isShowManual: false,
    isShowMsg: false,
    isAiShoulu: false,
  };

  formRef = React.createRef();

  consoleManual = () => {
    this.setState({
      isShowManual: false,
    });
  };

  handleChange = value => {
    this.setState({
      pub_type: value,
    });
  };

  handlePlatformChange = value => {
    this.setState({
      platform: value,
    });
  };

  handleChangeStatus = value => {
    this.setState({
      status: value,
    });
  };

  changePageSize = content => {
    console.log(content);
    const { InfoList = [], pagination } = this.props;
    const { pageSize, current } = content;
    const { keyword, pub_type, status, platform } = this.state;
    if (this.state.status == 5) {
      this.setState({
        isAiShoulu: true,
      });
    } else {
      this.setState({
        isAiShoulu: false,
      });
    }
    this.props.dispatch({
      type: 'info/getInfoList',
      payload: { keyword, pub_type, platform, status, currentPage: current, pageSize },
    });
  };

  pageChange = currentPage => {
    console.log(currentPage);
    if (this.state.status == 5) {
      this.setState({
        isAiShoulu: true,
      });
    } else {
      this.setState({
        isAiShoulu: false,
      });
    }
    const { keyword, pub_type, platform, status } = this.state;

    this.props.dispatch({
      type: 'info/getInfoList',
      payload: { keyword, pub_type, platform, status, currentPage },
    });
  };

  componentDidMount() {
    if (this.state.status == 5) {
      this.setState({
        isAiShoulu: true,
      });
    } else {
      this.setState({
        isAiShoulu: false,
      });
    }
    this.props.dispatch({ type: 'info/getInfoList', payload: {} });
  }

  handleChangeKey = e => {
    this.setState({
      keyword: e.target.value,
    });
  };

  onFinish = values => {
    // eslint-disable-next-line @typescript-eslint/camelcase
    const { keyword, pub_type, platform, status } = values;
    // if (this.state.status == 4) {
    //   this.setState({
    //     isShowMsg: true,
    //   });
    // } else {
    //   this.setState({
    //     isShowMsg: false,
    //   });
    // }
    // if (this.state.status == 5) {
    //   this.setState({
    //     isAiShoulu: true,
    //   });
    // } else {
    //   this.setState({
    //     isAiShoulu: false,
    //   });
    // }
    this.props.dispatch({
      type: 'info/getInfoList',
      payload: { keyword, pub_type, platform, status, currentPage: 1 },
    });
  };

  showManual = () => {
    this.setState({
      isShowManual: true,
    });
  };

  handleFormReset = () => {
    this.setState({
      keyword: '',
      pub_type: '',
      platform: '',
    });
  };

  handleSelectModal = record => {
    const { dispatch } = this.props;
    const that = this;

    this.props.dispatch({
      type: 'info/setInfo',
      payload: record,
      callBack(result) {
        that.showManual();
      },
    });
  };

  handleFailed = record => {
    Modal.error({
      content: record.failed_reason,
      zIndex: 9999,
    });
  };

  onPageChanged = (page, pageSize) => {
    const form = this.formRef.current;
    const formData = form.getFieldsValue();
    const { keyword, pub_type, platform, status } = formData;
    this.props.dispatch({
      type: 'info/getInfoList',
      payload: { keyword, pub_type, platform, status, currentPage: page, pageSize },
    });
  };

  render() {
    const columns = [
      {
        title: '标题',
        dataIndex: 'name',
        key: 'title',
      },
      {
        title: '发布日期',
        dataIndex: 'time',
        key: 'update',
      },
      {
        title: '发布平台',
        dataIndex: 'merchant_class',
        key: 'platform',
      },
      {
        title: '发布来源',
        dataIndex: 'pubType',
        key: 'source',
      },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'state',
        render: (text, record) => {
          if (record.status === 1) {
            return '等待推送';
          }
          if (record.status === 2) {
            return '推送成功';
          }
          if (record.status === 3) {
            return '审核未通过';
          }
          if (record.status === 4) {
            return '推送失败';
          }
          return '未定义';
        },
      },
      {
        title: '操作',
        key: 'operate',
        render: (text, record) => (
          <span className={styles.aText}>
            {record.status === 2 && (
              <a href={record.res_url} target="_blank" rel="noreferrer noopener">
                浏览
              </a>
            )}

            {record.status !== 2 && <ViewInfo item={record} />}

            {record.status === 4 && <Error title={record.failed_reason} />}
          </span>
        ),
      },
    ];
    const { InfoList = [], pagination = {} } = this.props;
    const { isOpenAi } = this.props;
    const { post_names } = this.props;

    return (
      <div className={styles.Releasebox}>
        <div className={styles.alltitle}>
          <span>发布历史</span>
        </div>
        <div className={styles.commoncont}>
          <Form
            ref={this.formRef}
            initialValues={{ keyword: '', platform: '-1', pub_type: '', status: '' }}
            onFinish={this.onFinish}
          >
            <Row>
              <Col span={5}>
                <Form.Item
                  label={<span>关键词</span>}
                  name="keyword"
                  labelCol={{ span: 8 }}
                  wrapperCol={{ span: 16 }}
                >
                  <Input placeholder="请输入关键词" value={this.state.keyword} />
                </Form.Item>
              </Col>

              <Col span={5}>
                <Form.Item
                  label={<span>平台</span>}
                  name="platform"
                  labelCol={{ span: 8 }}
                  wrapperCol={{ span: 16 }}
                >
                  <Select defaultValue="全部" onChange={handleChange}>
                    <Select.Option value="-1">全部</Select.Option>
                    {post_names &&
                      post_names.map(item => (
                        <Select.Option value={item.id}>{item.name}</Select.Option>
                      ))}
                  </Select>
                </Form.Item>
              </Col>

              <Col span={5}>
                <Form.Item
                  label={<span>来源</span>}
                  name="pub_type"
                  labelCol={{ span: 8 }}
                  wrapperCol={{ span: 16 }}
                >
                  <Select
                    defaultValue="全部"
                    onChange={handleChange}
                    options={[
                      {
                        value: '',
                        label: '全部',
                      },
                      {
                        value: 'auto',
                        label: '自动发布',
                      },
                      {
                        value: 'manual',
                        label: '手动发布',
                      },
                    ]}
                  />
                </Form.Item>
              </Col>

              <Col span={5}>
                <Form.Item
                  label={<span>状态</span>}
                  name="status"
                  labelCol={{ span: 8 }}
                  wrapperCol={{ span: 16 }}
                >
                  <Select defaultValue="全部" onChange={handleChange}>
                    <Select.Option value="">全部</Select.Option>
                    <Select.Option value="1">等待推送</Select.Option>
                    <Select.Option value="2">推送成功</Select.Option>
                    <Select.Option value="4">推送失败</Select.Option>
                  </Select>
                </Form.Item>
              </Col>

              <Col span={4}>
                <Form.Item
                  wrapperCol={{
                    offset: 1,
                    span: 16,
                  }}
                >
                  <Button type="primary" htmlType="submit">
                    搜索
                  </Button>
                </Form.Item>
              </Col>
            </Row>
          </Form>
          <Table pagination={false} columns={columns} dataSource={InfoList} />
          {/* 引入发布历史表格组件 */}
          <div className={styles.tablefoot}>
            <div className={styles.historynumber}>
              共<span>{pagination.total}</span>条信息
            </div>
            <div className={styles.historypage}>
              <Pagination
                total={pagination.total}
                onChange={this.onPageChanged}
                showSizeChanger
                showQuickJumper
                showTotal={total => `总共 ${total} 条`}
              />
              {/* 引入分页组件 */}
            </div>
          </div>
        </div>
      </div>
    );
  }
}

export default connect(({ info, home, merchant }) => ({ ...info, ...home, ...merchant }))(
  ReleaseHistory,
);
