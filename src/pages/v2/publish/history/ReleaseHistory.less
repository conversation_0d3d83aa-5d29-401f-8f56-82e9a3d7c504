.Releasebox {
  background: #fff;
}
.alltitle {
  height: 60px;
  padding-left: 20px;
  line-height: 60px;
  background: #fff;
  border-bottom: 1px solid #d7d7d7;
}
.alltitle > span {
  display: inline-block;
  padding: 0 5px;
  color: #333;
  font-weight: bold;
  font-size: 18px;
  line-height: 58px;
  border-bottom: 2px solid #1890ff;
}
.commoncont {
  padding: 20px;
}
.tablefoot {
  display: flex;
  margin: 0 auto;
  padding: 10px 0;
}
.historynumber {
  flex: 1;
  text-align: left;
}
.historynumber > span {
  color: #f00;
}
.historypage {
  flex: 2;
  text-align: right;
}
