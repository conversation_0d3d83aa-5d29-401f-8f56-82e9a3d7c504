import React from 'react';

import { Tabs } from 'antd';

import styles from './RankCount.less';
import RankingHy88 from '@/components/RankingHy88';
import RankingOthers from '@/components/RankingOthers';

class RankCount extends React.PureComponent {
  state = {
    defaultActiveKey: '1',
  }


  render() {
    return (
      <div className={styles.Rankbox}>
        <div className={styles.alltitle}>
          <span>排名统计</span>
        </div>
        <div className={styles.commoncont}>
          <Tabs defaultActiveKey={this.state.defaultActiveKey}>
            <Tabs.TabPane tab="黄页88" key="1">
              <RankingHy88 />
            </Tabs.TabPane>
            <Tabs.TabPane tab="第三方平台" key="2">
              <RankingOthers />
            </Tabs.TabPane>
          </Tabs>
        </div>
      </div>
    );
  }
}

export default RankCount;
