import React from 'react';
import { Table, Space, message, Pagination } from 'antd';
import { connect } from 'dva';
import router from 'umi/router';
import styles from './ProductList.less';
import ProductOperate from '@/components/ProductOperate';
import EditProductDetail from '@/pages/v1/publish/products/EditProductDetail';
import ProductDetail from '@/pages/v2/publish/producttemplate/ProductDetail';
const gif = require('@/assets/new.gif');

class ProductList extends React.PureComponent {
  state = {
    showNormalDetail: false,
    showCustomDetail: false,
    id: 0,
  };

  componentDidMount() {
    this.loadData();
  }

  // eslint-disable-next-line react/sort-comp
  loadData() {
    this.props.dispatch({
      type: 'product/getProductList',
      payload: { count: 1, currentPage: 1, pageSize: 10 },
    });
  }

  pageChange = (currentPage, pageSize) => {
    this.props.dispatch({
      type: 'product/getProductList',
      payload: { currentPage, pageSize, count: 1 },
    });
  };

  goAddProduct = type => {
    if (type === 0) {
      router.push('/publish/products/add1');
    } else {
      router.push('/publish/products/customstep1');
    }
  };

  viewDetail = item => {
    this.setState({
      id: item.id,
      showCustomDetail: item.mode === 1,
      showNormalDetail: item.mode === 0,
    });
  };

  render() {
    const columns = [
      {
        title: '序号',
        dataIndex: 'sNumber',
        key: 'number',
      },

      {
        title: '产品名称',
        dataIndex: 'name',
        key: 'name',
      },
      {
        title: '添加/更新时间',
        dataIndex: 'time',
        render: (text, record) => {
          const { time, updateTime } = record;
          return (
            <div>
              <span>{time}</span>
              <br />
              <span style={{ color: '#A6A6A6' }}>{updateTime}</span>
            </div>
          );
        },
        key: 'time',
      },
      {
        title: '模板类型',
        key: 'mode',
        dataIndex: 'mode',
        render: record => {
          const text = record === 1 ? '自定义' : '默认模板';
          return (
            <div>
              <span>{text}</span>
            </div>
          );
        },
      },
      {
        title: '产品状态',
        key: 'status',
        dataIndex: 'status',
      },
      {
        title: '操作',
        key: 'operate',
        dataIndex: 'operate',
        render: (text, record) => (
          <Space size="middle">
            <a
              onClick={e => {
                e.preventDefault();
                this.viewDetail(record);
              }}
            >
              <span>查看</span>
            </a>
            <ProductOperate item={record} onFinish={() => this.loadData()} />
          </Space>
        ),
      },
    ];
    const { productList, pagination } = this.props;
    console.log(pagination, productList);

    return (
      <div className={styles.Productbox}>
        <div className={styles.alltitle}>
          <span>产品列表</span>
        </div>
        <div className={styles.producttop}>
          <a onClick={() => this.goAddProduct(0)}>新增产品</a>
          <a onClick={() => this.goAddProduct(1)}>
            自定义模板
            <span style={{ float: 'right', marginTop: '-10px', position: 'absolute' }}>
              {/* eslint-disable-next-line global-require */}
              <img src={gif} alt="Custom Image" style={{ width: '28px', height: '18px' }} />
            </span>
          </a>
        </div>
        <div className={styles.commoncont}>
          <Table pagination={false} columns={columns} dataSource={productList} bordered />
          {/* 在此处使用产品列表表格 */}
        </div>
        <div className={styles.pagesline}>
          <Pagination
            total={pagination.total}
            showSizeChanger
            onChange={this.pageChange}
            showQuickJumper
            showTotal={total => `总共 ${total} 条`}
          />
        </div>

        {this.state.showNormalDetail && (
          <EditProductDetail
            close={() => this.setState({ showNormalDetail: false })}
            id={this.state.id}
            isCreateProduct={false}
            onlyShow={true}
          />
        )}
        {this.state.showCustomDetail && (
          <ProductDetail
            close={() => this.setState({ showCustomDetail: false })}
            id={this.state.id}
            isCreateProduct={false}
            onlyShow={true}
          />
        )}
      </div>
    );
  }
}

export default connect(({ product, home }) => ({ ...product, ...home }))(ProductList);
