import React from 'react';

import { Table, Popover, Row } from 'antd';
import { connect } from 'dva';
import styles from './B2BPlatform.less';
import UploadStatementPic from '@/components/UploadStatementPic';



class B2BPlatformProcess extends React.PureComponent {
  state = {
  };


  componentDidMount() {
    this.props.dispatch({ type: 'merchant/queryMerchant', payload: {} });
  }

  ShowAddPicAlbum = id => {
    this.setState({
      isShowAddPicAlbum: true,
      selId: id,
    });
  };


  consoleAddPicAlbum = () => {
    this.setState({
      isShowAddPicAlbum: false,
    });
    this.props.dispatch({ type: 'merchant/queryMerchant', payload: {} });
  };

  openCompany = () => {
    router.push({ pathname: '/company/detail' });
  };

  render() {
    const { merchantList = [] } = this.props;
    const columns = [
      {
        title: '序号',
        dataIndex: 'id',
        key: 'id',
      },

      {
        title: '平台',
        dataIndex: 'name',
        key: 'name',
      },
      {
        title: '平台地址',
        dataIndex: 'company_site',
        key: 'company_site',
        render: (text, record) => (
          <a type="link" href={record.company_site} target="_blank" rel="noopener noreferrer">
            {record.company_site}
          </a>
        ),
      },
      {
        title: '启用状态',
        key: 'pause',
        dataIndex: 'pause',
        render: (text, record) => (record.pause ? '暂停' : '启用'),
      },
      {
        title: '每个推广产品发布数量',
        key: 'pub_per_count',
        dataIndex: 'pub_per_count',
      },
      {
        title: '状态',
        key: 'status',
        dataIndex: 'status',
        render: (text, record) => (
          <div>
            {record.reason !== undefined && record.reason !== '' && record.reason.length > 0 && (
              <Popover placement="bottom" zIndex={9999} trigger="hover" content={record.reason}>
                <span style={{ color: 'blue' }}>{record.status}</span>
              </Popover>
            )}
            {(record.reason === undefined || record.reason === '') && <div> {record.status}</div>}
            {record.action === 1 && (
              <Row>
                <a style={{ color: 'blue' }} onClick={this.openCompany}>
                  上传营业执照
                </a>
              </Row>
            )}
            {record.action === 2 && (
              <Row>
                <a
                  style={{ color: 'blue' }}
                  onClick={() => {
                    this.ShowAddPicAlbum(record.id);
                  }}
                >
                  {' '}
                  上传企业声明
                </a>
              </Row>
            )}
          </div>
        ),
      },
    ];

    return (
      <div className={styles.companybox}>
        <div className={styles.alltitle}>
          <span>B2B商铺</span>
        </div>
        <div className={styles.commoncont}>
          <Table pagination={false} columns={columns} dataSource={merchantList} bordered/>
        </div>
        {this.state.isShowAddPicAlbum && (
          <UploadStatementPic close={this.consoleAddPicAlbum} id={this.state.selId} />
        )}
      </div>
    );
  }
}

export default connect(({ merchant }) => ({ ...merchant }))(B2BPlatformProcess);
