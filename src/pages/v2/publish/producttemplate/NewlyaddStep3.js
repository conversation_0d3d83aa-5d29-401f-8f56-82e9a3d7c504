import React from 'react';
import { Steps, Form, Button, Row, Col, Space, message, Modal } from 'antd';
import { connect } from 'dva';
import styles from './Custom.less';
import CustomUploadimg from '../../../../components/CustomUploadimg';
import CustomUploadvideo from '../../../../components/CustomUploadvideo';
import CustomUploadDisplaypic from '@/components/CustomUploadDisplaypic';
import CustomUploadDisplayvideo from '@/components/CustomUploadDisplayvideo';
import { keyMap } from '@/utils/constant';
import calculateHash from '@/utils/hash';
import router from 'umi/router';

const getCacheKey = productId => {
  return `newlyadd_step3_cache_${productId || 'new'}`;
};

class NewlyaddStep3 extends React.PureComponent {
  formRef = React.createRef();

  state = {
    // eslint-disable-next-line react/no-unused-state
    product: {
      titlepic: [], // 标题图片, 这里处理下，包含了封面图片
      pic: [], // 详情图片
      videos: [], // 视频
    },
    // 只读，用来对比是否有修改
    preProduct: {
      titlepic: [], // 标题图片, 这里处理下，包含了封面图片
      pic: [], // 详情图片
      videos: [], // 视频
    },
    id: 0,
    hashStr: '',
    submiting: false,
    currentStep: 2,
    auditRes2: [],
    fullAuditRes2: [],
  };

  clearCache = id => {
    localStorage.removeItem(getCacheKey(id || this.state.id));
  };

  componentDidMount() {
    console.log('CustomStep3 loaded');
    // 解析id 参数，然后加载产品信息。如果没有id参数，就跳转到产品列表页
    let { id } = this.props.match.params;
    if (!id) {
      router.push('/publish/products');
      return;
    }
    id = parseInt(id, 10);

    const cache = localStorage.getItem(getCacheKey(id));
    if (cache) {
      Modal.confirm({
        title: '发现未保存的编辑内容',
        content: '是否恢复上次未保存的编辑内容？',
        okText: '恢复',
        cancelText: '不需要',
        onOk: () => {
          const cachedData = JSON.parse(cache);
          this.setState(
            {
              product: cachedData.product,
              id,
            },
            () => {
              this.formRef.current.setFieldsValue({
                titlepic: cachedData.product.titlepic,
                pic: cachedData.product.pic,
                videos: cachedData.product.videos,
              });
            },
          );
        },
        onCancel: () => {
          this.clearCache(id);
          this.setState({ id });
          this.loadProduct(id);
        },
      });
    } else {
      this.setState({ id });
      this.loadProduct(id);
    }
  }

  saveToCache = () => {
    const cacheData = {
      product: this.state.product,
    };
    localStorage.setItem(getCacheKey(this.state.id), JSON.stringify(cacheData));
  };

  arrString2json(arr) {
    const result = [];
    if (!arr) {
      return [];
    }
    for (const item of arr) {
      try {
        result.push(JSON.parse(item));
      } catch (error) {
        result.push({ url: item, name: '图片名称' });
      }
    }
    return result;
  }
  loadProduct = id => {
    this.props
      .dispatch({
        type: 'product/getProductById',
        payload: { product_id: id },
      })
      .then(result => {
        this.setState(
          ({ product }) => {
            let titlepic = [];
            if (result.titlepic) {
              titlepic = result.titlepic;
            }
            let pic = [];
            if (result.pic) {
              pic = result.pic;
            }
            const auditRes2 = {};
            for (const key in result.audit_res2) {
              if (Object.prototype.hasOwnProperty.call(product, key)) {
                auditRes2[key] = result.audit_res2[key];
              }
            }
            const updatedProduct = {
              ...product,
              titlepic: this.arrString2json(titlepic),
              pic: this.arrString2json(pic),
              videos: this.arrString2json(result.videos),
            };
            const hashStr = calculateHash(updatedProduct);
            return {
              product: updatedProduct,
              preProduct: { ...updatedProduct },
              hashStr,
              auditRes2,
              fullAuditRes2: result.audit_res2,
            };
          },
          () => {
            // 更新表单
            this.formRef.current.setFieldsValue({
              titlepic: this.state.product.titlepic,
              pic: this.state.product.pic,
              videos: this.state.product.videos,
            });
          },
        );
      })
      .catch(err => {
        console.log(err);
      });
  };

  onStepChange = (current, step) => {
    if (current < this.state.currentStep) {
      router.push(`/publish/products/add${current + 1}/${this.state.id}`);
    }
  };

  handleFailed = err => {
    message.error(err.errorFields[0].errors[0]);
  };

  json2arrString(arr) {
    const result = [];
    if (!arr) {
      return [];
    }
    for (const item of arr) {
      result.push(JSON.stringify(item));
    }
    return result;
  }

  onFinish = values => {
    const { id: productId, auditRes2, preProduct, fullAuditRes2 } = this.state;
    let newAuditRes2 = { ...fullAuditRes2 };
    for (const key in auditRes2) {
      const element = values[key];
      if (JSON.stringify(element) === JSON.stringify(preProduct[key])) {
        message.error(`请修改${keyMap[key]}的值: ${auditRes2[key]}`);
        return;
      }
      delete fullAuditRes2[key];
    }
    const newHash = calculateHash(this.state.product);
    if (newHash !== this.state.hashStr) {
      this.setState({ submiting: true });
      // eslint-disable-next-line prefer-const
      let { titlepic, videos } = this.state.product;

      // 有变化， 调用更新接口
      this.props
        .dispatch({
          type: 'product/updateProductById',
          payload: {
            product_id: productId,
            pic: this.json2arrString(values.pic),
            videos: this.json2arrString(values.videos),
            titlepic: this.json2arrString(values.titlepic),
            audit_res2: newAuditRes2,
          },
        })
        .then(result => {
          this.setState({ submiting: false });
          this.clearCache();
          router.push(`/publish/products/add4/${productId}`);
        })
        .catch(err => {
          this.setState({ submiting: false });
          message.error(err.msg || err.message);
        });
    } else {
      this.setState({ submiting: false });
      this.clearCache();
      router.push(`/publish/products/add4/${productId}`);
    }
  };

  render() {
    const { submiting, product, auditRes2 } = this.state;
    return (
      <div className={styles.custombox}>
        <div className={styles.alltitle}>
          <span>新增产品</span>
        </div>
        <div className={styles.customwrap}>
          {Object.keys(auditRes2).length > 0 && (
            <div className={styles.to_examine}>
              <p>审核未通过原因：</p>
              {Object.keys(auditRes2).map(item => (
                <p key={item}>
                  {keyMap[item]}：{auditRes2[item]}。
                </p>
              ))}
            </div>
          )}
          <div className={styles.stepbox}>
            <Steps current={this.state.currentStep} onChange={this.onStepChange}>
              <Steps.Step title="添加标题" />
              <Steps.Step title="添加素材" />
              <Steps.Step title="图片视频" />
              <Steps.Step title="属性分类" />
            </Steps>
          </div>
          <Form
            ref={this.formRef}
            onFinish={this.onFinish}
            onFinishFailed={this.handleFailed}
            initialValues={{
              videos: product.videos,
              titlepic: product.titlepic,
              pic: product.pic,
            }}
          >
            <Row style={{ padding: '0 20px' }}>
              <Col span={4}>
                <Form.Item
                  name="titlepic"
                  label="标题图片"
                  rules={[
                    {
                      required: true,
                      message: '请上传标题图片！',
                    },
                    {
                      type: 'array',
                      min: 3,
                      max: 50,
                      message: '上传3-50张！',
                    },
                  ]}
                  valuePropName="fileList"
                  extra="图片清晰、完整，上传3-50张"
                >
                  {/* 上传标题图片组件 */}
                  <CustomUploadimg
                    onConfirm={selected => {
                      this.setState(
                        ({ product }) => {
                          // 需要合并之前的 titlepic, 并滤重
                          // eslint-disable-next-line no-use-before-define,@typescript-eslint/no-use-before-define
                          const newSelected = [...product.titlepic, ...selected];
                          // 对图片去重
                          // eslint-disable-next-line no-shadow
                          const selectedPics = newSelected.filter(
                            (item, index, self) => self.findIndex(t => t === item) === index,
                          );
                          const updatedProduct = {
                            ...product,
                            titlepic: selectedPics,
                          };
                          return {
                            product: updatedProduct,
                          };
                        },
                        () => {
                          // 更新form表单
                          this.formRef.current.setFieldsValue({
                            titlepic: this.state.product.titlepic,
                          });
                          this.saveToCache();
                        },
                      );
                    }}
                  />
                </Form.Item>
              </Col>
              <Col span={20}>
                <CustomUploadDisplaypic
                  images={product.titlepic}
                  onRemoved={item => {
                    this.setState(
                      ({ product }) => {
                        const titlepic = product.titlepic.filter(t => t !== item);
                        const updatedProduct = {
                          ...product,
                          titlepic,
                        };
                        return {
                          product: updatedProduct,
                        };
                      },
                      () => {
                        // 更新form表单
                        this.formRef.current.setFieldsValue({
                          titlepic: this.state.product.titlepic,
                        });
                      },
                    );
                  }}
                />
                {/* 右侧上传后显示图片 */}
              </Col>
              <Col span={4}>
                <Form.Item
                  name="pic"
                  label="详情图片"
                  rules={[
                    {
                      required: true,
                      message: '请上传详情图片！',
                    },
                    {
                      type: 'array',
                      min: 3,
                      max: 50,
                      message: '上传3-50张！',
                    },
                  ]}
                  valuePropName="fileList"
                  extra="图片清晰、完整，上传3-50张"
                >
                  {/* 上传标题图片组件 */}
                  <CustomUploadimg
                    onConfirm={selected => {
                      this.setState(
                        ({ product }) => {
                          // 需要合并之前的 pic, 并滤重
                          // eslint-disable-next-line no-use-before-define,@typescript-eslint/no-use-before-define
                          const newSelected = [...product.pic, ...selected];
                          // 对图片去重
                          // eslint-disable-next-line no-shadow
                          const selectedPics = newSelected.filter(
                            (item, index, self) => self.findIndex(t => t === item) === index,
                          );
                          const updatedProduct = {
                            ...product,
                            pic: selectedPics,
                          };
                          return {
                            product: updatedProduct,
                          };
                        },
                        () => {
                          // 更新form表单
                          this.formRef.current.setFieldsValue({
                            pic: this.state.product.pic,
                          });
                          this.saveToCache();
                        },
                      );
                    }}
                  />
                </Form.Item>
              </Col>
              <Col span={20}>
                <CustomUploadDisplaypic
                  images={product.pic}
                  onRemoved={item => {
                    this.setState(
                      ({ product }) => {
                        const pic = product.pic.filter(t => t !== item);
                        const updatedProduct = {
                          ...product,
                          pic,
                        };
                        return {
                          product: updatedProduct,
                        };
                      },
                      () => {
                        // 更新form表单
                        this.formRef.current.setFieldsValue({
                          pic: this.state.product.pic,
                        });
                      },
                    );
                  }}
                />
                {/* 右侧上传后显示图片 */}
              </Col>
              <Col span={4}>
                <Form.Item
                  name="videos"
                  label="上传视频"
                  valuePropName="fileList"
                  rules={[
                    {
                      type: 'array',
                      max: 6,
                      message: '最多上传6个',
                    },
                  ]}
                  extra="选择与产品相关的视频，最多上传6个，仅支持在黄页88网显示。"
                >
                  {/* 上传视频组件 */}
                  <CustomUploadvideo
                    onConfirm={selected => {
                      const newSelected = [...product.videos, ...selected];
                      // 对图片去重
                      // eslint-disable-next-line no-shadow
                      const selectedVideos = newSelected.filter(
                        (item, index, self) => self.findIndex(t => t.id === item.id) === index,
                      );
                      // eslint-disable-next-line no-shadow
                      this.setState(
                        ({ product }) => {
                          const updatedProduct = {
                            ...product,
                            videos: selectedVideos,
                          };
                          return {
                            product: updatedProduct,
                          };
                        },
                        () => {
                          // 更新form表单
                          this.formRef.current.setFieldsValue({
                            videos: this.state.product.videos,
                          });
                          this.saveToCache();
                        },
                      );
                    }}
                  />
                </Form.Item>
              </Col>
              <Col span={20}>
                {/* 右侧上传后显示视频 */}
                <CustomUploadDisplayvideo
                  videos={product.videos}
                  onRemoved={item => {
                    this.setState(
                      ({ product }) => {
                        const videos = product.videos.filter(t => t.id !== item.id);
                        const updatedProduct = {
                          ...product,
                          videos,
                        };
                        return {
                          product: updatedProduct,
                        };
                      },
                      () => {
                        // 更新form表单
                        this.formRef.current.setFieldsValue({
                          videos: this.state.product.videos,
                        });
                      },
                    );
                  }}
                />
              </Col>
            </Row>
            <div className={styles.addtitle_submit}>
              <Space>
                <Button type="primary" htmlType="submit" loading={submiting}>
                  保存进入下一步
                </Button>
              </Space>
            </div>
          </Form>
        </div>
      </div>
    );
  }
}

export default connect(({ home }) => ({ ...home }))(NewlyaddStep3);
