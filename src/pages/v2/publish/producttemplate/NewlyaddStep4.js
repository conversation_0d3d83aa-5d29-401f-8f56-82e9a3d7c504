import React from 'react';
import {
  Row,
  Steps,
  Col,
  Form,
  Input,
  InputNumber,
  Select,
  Radio,
  Button,
  Cascader,
  Space,
  message,
  Alert,
  Checkbox,
  Modal,
} from 'antd';

import styles from './Custom.less';
import Setkeywords from '@/components/Setkeywords';
import SetAlias from '@/components/SetAlias';
import AddSubtract from '@/components/AddSubtract';
import { connect } from 'dva';
import calculateHash from '@/utils/hash';
import { keyMap } from '@/utils/constant';
import { min } from 'lodash';
import router from 'umi/router';
import { findDOMNode } from 'react-dom';

const getCacheKey = productId => {
  return `newlyadd_step4_cache_${productId || 'new'}`;
};

class NewlyaddStep4 extends React.PureComponent {
  formRef = React.createRef();

  dynFormRef = React.createRef();

  state = {
    // eslint-disable-next-line react/no-unused-state
    product: {
      price: null, // 价格，0-面议
      unit: null, // 单位
      areaids: [], // 面向地区
      properties: {}, // 属性参数
      cate: [], // 分类
      brand: '', // 品牌
      min_order: '', // 起订量
      inventory: '', // 库存
      faq: '', // 产品问答
    },
    // 只读，用来对比是否有修改
    preProduct: {
      price: null,
      unit: null,
      areaids: [],
      properties: {},
      cate: [],
      brand: '',
      min_order: '',
      inventory: '',
      faq: '',
    },
    id: 0,
    hashStr: '',
    submiting: false,
    auditRes2: [],
    areaidsTip: '', // 地区判断
    cateTip: '', // 分类判断
    isCateLast: true,
    // 动态属性
    properties: {}, // 编辑的属性
    fields: [], // 动态分类表单
    AddPropertiesTitle: {}, // 动态添加的属性名
    AddPropertiesValue: {}, // 动态添加的属性字段
    currentStep: 3,
    isInitialLoad: true, // 添加标志位,用于区分是否是初始加载
  };

  componentDidMount() {
    let { id } = this.props.match.params;
    if (!id) {
      router.push('/publish/products');
      return;
    }
    this.props.dispatch({ type: 'home/getCity', payload: {} });
    this.props.dispatch({ type: 'home/getMyAllCate', payload: {} });

    id = parseInt(id, 10);

    // 先加载数据
    this.setState({ id }, () => {
      this.loadProduct(id);
    });
  }

  componentDidUpdate(prevProps, prevState) {
    // 只有在非初始加载状态下才保存缓存
    if (
      !this.state.isInitialLoad &&
      (JSON.stringify(prevState.product) !== JSON.stringify(this.state.product) ||
        JSON.stringify(prevState.properties) !== JSON.stringify(this.state.properties) ||
        JSON.stringify(prevState.AddPropertiesTitle) !==
          JSON.stringify(this.state.AddPropertiesTitle) ||
        JSON.stringify(prevState.AddPropertiesValue) !==
          JSON.stringify(this.state.AddPropertiesValue))
    ) {
      this.saveToCache();
    }
  }

  saveToCache = () => {
    const cacheData = {
      product: this.state.product,
      properties: this.state.properties,
      AddPropertiesTitle: this.state.AddPropertiesTitle,
      AddPropertiesValue: this.state.AddPropertiesValue,
      fields: this.state.fields,
    };
    localStorage.setItem(getCacheKey(this.state.id), JSON.stringify(cacheData));
  };

  loadProduct = id => {
    this.props
      .dispatch({
        type: 'product/getProductById',
        payload: { product_id: id },
      })
      .then(result => {
        this.setState(
          ({ product }) => {
            const updatedProduct = {
              ...product,
            };
            // 用 result 来更新 product
            for (const key in result) {
              if (Object.prototype.hasOwnProperty.call(product, key)) {
                updatedProduct[key] = result[key];
              }
            }
            // 处理null对象，给默认值
            updatedProduct.alias = result.alias || [];
            updatedProduct.word = result.word || [];
            updatedProduct.areaids = result.areaids && result.areaids.filter(item => item !== '0');
            updatedProduct.cate = result.cate || [];
            updatedProduct.properties = result.properties || {};

            // 处理result.audit_res2 只保留product也存在的属性
            const auditRes2 = {};
            for (const key in result.audit_res2) {
              if (Object.prototype.hasOwnProperty.call(product, key)) {
                auditRes2[key] = result.audit_res2[key];
              }
            }
            const hashStr = calculateHash(updatedProduct);
            if (updatedProduct.cate.length > 0) {
              this.getCateProperty(
                updatedProduct.cate[updatedProduct.cate.length - 1],
                updatedProduct.properties,
              );
            }
            return {
              product: updatedProduct,
              preProduct: updatedProduct, // 保存原始数据用于对比
              properties: updatedProduct.properties || {}, // 初始化properties状态
              hashStr,
              auditRes2,
              isInitialLoad: true, // 保持初始加载状态
            };
          },
          () => {
            // 更新表单
            this.formRef.current.resetFields();

            // 在loadProduct完成后检查缓存
            const cache = localStorage.getItem(getCacheKey(id));
            if (cache) {
              Modal.confirm({
                title: '发现未保存的编辑内容',
                content: '是否恢复上次未保存的编辑内容？',
                okText: '恢复',
                cancelText: '不需要',
                onOk: () => {
                  const cachedData = JSON.parse(cache);
                  // 先设置fields,避免被getCateProperty重置
                  this.setState(
                    {
                      fields: cachedData.fields || [],
                    },
                    () => {
                      // 合并自定义属性到properties
                      const mergedProperties = { ...cachedData.properties };
                      for (const key in cachedData.AddPropertiesValue) {
                        if (
                          cachedData.AddPropertiesValue[key] &&
                          cachedData.AddPropertiesTitle[key]
                        ) {
                          const newKey = cachedData.AddPropertiesTitle[key];
                          mergedProperties[newKey] = cachedData.AddPropertiesValue[key];
                        }
                      }

                      // 恢复缓存数据
                      this.setState(
                        {
                          product: {
                            ...cachedData.product,
                            properties: mergedProperties,
                          },
                          preProduct: this.state.preProduct, // 保持原始数据不变
                          properties: mergedProperties,
                          AddPropertiesTitle: cachedData.AddPropertiesTitle || {},
                          AddPropertiesValue: cachedData.AddPropertiesValue || {},
                          isInitialLoad: false, // 恢复缓存后,设置非初始加载状态
                        },
                        () => {
                          // 设置表单值
                          this.formRef.current.resetFields();

                          // 如果有分类，获取分类属性
                          if (cachedData.product.cate && cachedData.product.cate.length > 0) {
                            this.getCateProperty(
                              cachedData.product.cate[cachedData.product.cate.length - 1],
                              mergedProperties,
                            );
                          }
                        },
                      );
                    },
                  );
                },
                onCancel: () => {
                  this.clearCache(id);
                  // 清除缓存后,设置非初始加载状态
                  this.setState({ isInitialLoad: false });
                },
              });
            } else {
              // 如果没有缓存,设置非初始加载状态
              this.setState({ isInitialLoad: false });
            }
          },
        );
      })
      .catch(err => {
        console.log(err);
        // 发生错误时也要设置非初始加载状态
        this.setState({ isInitialLoad: false });
      });
  };

  onChangeCity = (value, selectedOptions) => {
    if (!value) {
      this.setState(
        prevState => ({
          areaids: [],
          areaidsTip: '',
          product: {
            ...prevState.product,
            areaids: [], // 更新 product 中的 areaids
          },
        }),
        () => {
          this.formRef.current.setFieldsValue({
            areaids: [],
          });
        },
      );
      return;
    }

    const { product } = this.state;
    const { company } = this.props;

    this.props.dispatch({
      type: 'product/getCityNotSupportPlatform',
      payload: { id: value[value.length - 1], cid: company.id },
      callBack: data => {
        if (data && data.length > 0) {
          this.setState(prevState => ({
            areaidsTip: data.toString(),
            product: {
              ...prevState.product,
              areaids: value, // 即使有不支持的平台也更新值
            },
          }));
        } else {
          this.setState(
            prevState => ({
              areaidsTip: '',
              product: {
                ...prevState.product,
                areaids: value,
              },
            }),
            () => {
              this.formRef.current.setFieldsValue({
                areaids: value,
              });
            },
          );
        }
      },
    });
  };

  onChangeCate = (value, selectedOptions) => {
    const { company, isOpenBaixing } = this.props;

    if (!selectedOptions[selectedOptions.length - 1].isLeaf) {
      this.setState({
        isCateLast: false,
        product: {
          ...this.state.product,
          cate: value, // 更新 product 中的 cate
        },
      });
      return;
    }

    this.props.dispatch({
      type: 'product/getCateNotSupportPlatform',
      payload: { id: value[value.length - 1], cid: company.id },
      callBack: platforms => {
        if (platforms && platforms.length > 0) {
          this.setState(
            prevState => ({
              cateTip: platforms.toString(),
              isCateLast: true,
              product: {
                ...prevState.product,
                cate: value,
              },
            }),
            () => {
              this.formRef.current.setFieldsValue({
                cate: value,
              });
            },
          );
        } else {
          this.setState(
            prevState => ({
              cateTip: '',
              isCateLast: true,
              product: {
                ...prevState.product,
                cate: value,
              },
            }),
            () => {
              this.formRef.current.setFieldsValue({
                cate: value,
              });
            },
          );
        }
      },
    });
    // 切换分类时，清空自定义属性，只保留分类属性
    this.getCateProperty(value[value.length - 1], null);
  };

  // 获取分类属性
  // when properties is not null, it means that we need to judge witch properties is not in the fields
  getCateProperty = (id, properties = null) => {
    const { isOpenBaixing } = this.props;
    const reset = properties == null;

    // 保存当前的自定义属性
    const {
      AddPropertiesTitle: currentAddPropertiesTitle,
      AddPropertiesValue: currentAddPropertiesValue,
    } = this.state;

    this.props.dispatch({
      type: 'product/getCateProperty',
      payload: { id, merge: isOpenBaixing ? '1' : '0' },
      callBack: fields => {
        let cateProPerty = [...fields];

        // 如果不是重置，需要处理现有的properties数据
        if (!reset && properties) {
          // 检查哪些属性是自定义的（不在分类属性中的）
          for (const propKey in properties) {
            let isCustomizeField = true;
            for (const field of fields) {
              // 检查是否匹配：propKey === fieldname 或 propKey === properties[fieldname]中的fieldname
              if (propKey === field.fieldname) {
                isCustomizeField = false;
                break;
              }
            }

            // 如果是自定义属性，添加到fields中
            if (isCustomizeField) {
              const timestamp = Date.now() + Math.random(); // 确保唯一性
              const newCatePropertyItem = {
                displayname: propKey,
                displayValue: properties[propKey],
                fieldtype: 'addinput',
                ItemIndex: timestamp,
              };
              cateProPerty.push(newCatePropertyItem);
              currentAddPropertiesTitle[timestamp] = propKey;
              currentAddPropertiesValue[timestamp] = properties[propKey];
            }
          }
        }

        // 如果是重置，保留当前的自定义属性
        // 如果不是重置，合并属性
        this.setState({
          fields: cateProPerty,
          // 更新properties状态，确保包含所有属性值
          properties: reset ? {} : { ...properties },
          // 切换分类时清空自定义属性
          AddPropertiesTitle: reset ? {} : currentAddPropertiesTitle,
          AddPropertiesValue: reset ? {} : currentAddPropertiesValue,
        });
      },
    });
  };

  handleFailed = err => {
    message.error(err.errorFields[0].errors[0]);
  };

  onStepChange = (current, step) => {
    if (current < this.state.currentStep) {
      router.push(`/publish/products/add${current + 1}/${this.state.id}`);
    }
  };

  onFinish = async values => {
    let {
      product,
      auditRes2,
      isCateLast,
      properties,
      AddPropertiesValue,
      AddPropertiesTitle,
      preProduct,
    } = this.state;
    const { features } = this.props;

    // 遍历 auditRes2, 如果值没修改就提示用户修改
    for (const key in auditRes2) {
      const element = values[key];
      if (JSON.stringify(element) === JSON.stringify(preProduct[key])) {
        message.error(`请修改${keyMap[key]}的值: ${auditRes2[key]}`);
        return;
      }
    }

    // 动态属性
    let propertiesError = null;
    if (this.dynFormRef.current) {
      await this.dynFormRef.current.validateFields().catch(err => {
        propertiesError = err;
        Modal.error({ title: '提示', content: '分类属性必填项未填', zIndex: 9999 });
      });
    }

    if (propertiesError) {
      alert('分类属性必填项未填');
      return;
    }

    let propertiesCount = 0;
    // 重新构建properties，只包含当前有效的属性
    const finalProperties = {};

    // 1. 添加分类属性（从动态表单获取）
    const dynValues = this.dynFormRef.current.getFieldsValue();
    Object.keys(dynValues).forEach(key => {
      if (dynValues[key]) {
        // eslint-disable-next-line no-plusplus
        propertiesCount++;
        finalProperties[key] = dynValues[key];
      }
    });

    // 2. 添加当前有效的自定义属性（只添加AddPropertiesValue中存在的）
    for (const key in AddPropertiesValue) {
      if (AddPropertiesValue[key] && AddPropertiesTitle[key]) {
        propertiesCount++;
        const newKey = AddPropertiesTitle[key];
        finalProperties[newKey] = AddPropertiesValue[key];
      }
    }

    // 使用重新构建的properties
    properties = finalProperties;

    if (features.properties > 0 && propertiesCount < features.properties) {
      message.error(`您开通的平台要求填写至少${features.properties}个属性参数产品才能正常推广`);
      return;
    }
    if (features.price && !values.price) {
      message.error(`您开通的平台要求填写单价才能正常推广`);
      return;
    }
    values.properties = properties;

    product = { ...product, ...values };
    console.log('newproduct', product);
    this.setState({ submiting: true });
    const { id: productId } = this.state;
    const newHash = calculateHash(product);

    const successCallback = () => {
      this.clearCache();
      router.push('/publish/products/');
    };

    this.props
      .dispatch({
        type: 'product/updateProductById',
        payload: {
          ...product,
          product_id: productId,
        },
      })
      .then(result => {
        this.props
          .dispatch({
            type: 'product/optionProductById',
            payload: { product_id: productId, opt: 1 },
          })
          .then(msg => {
            this.setState({ submiting: false });
            if (msg === 'success') {
              Modal.success({
                content: '提交审核成功',
                zIndex: 9999,
              });
              successCallback();
            } else {
              Modal.error({
                content: '提交审核失败',
                zIndex: 9999,
              });
            }
          })
          .catch(err => {
            console.error(err);
            Modal.error({
              content: `提交审核失败:${err.msg}` || err.message,
              zIndex: 9999,
            });
            this.setState({ submiting: false });
          });
      })
      .catch(err => {
        this.setState({ submiting: false });
        message.error(err.msg || err.message);
      });
  };

  // eslint-disable-next-line max-len
  filter = (inputValue, path) =>
    path.some(option => option.label.toLowerCase().indexOf(inputValue.toLowerCase()) > -1);

  AddProperty = () => {
    const { fields, AddPropertiesTitle = {}, AddPropertiesValue = {} } = this.state;
    const timestamp1 = Date.now();
    const newCatePropertyItem = {
      displayname: '',
      displayValue: '',
      fieldtype: 'addinput',
      ItemIndex: timestamp1,
    };
    AddPropertiesTitle[timestamp1] = '';
    AddPropertiesValue[timestamp1] = '';

    this.setState({
      fields: [...fields, newCatePropertyItem],
      AddPropertiesTitle,
      AddPropertiesValue,
    });
  };

  changeAddProPertyItemTitle = (e, item) => {
    const { AddPropertiesTitle = {} } = this.state;
    AddPropertiesTitle[item.ItemIndex] = e.target.value;
    this.setState({
      AddPropertiesTitle,
    });
  };

  changeAddProPertyItemValue = (e, item) => {
    const { AddPropertiesValue = {} } = this.state;
    AddPropertiesValue[item.ItemIndex] = e.target.value;
    this.setState({
      AddPropertiesValue,
    });
  };

  delProperItem = (e, item) => {
    const { AddPropertiesValue, AddPropertiesTitle, fields, properties } = this.state;

    // 获取要删除的属性名
    const propertyNameToDelete = AddPropertiesTitle[item.ItemIndex];

    const newAddPropertiesValue = {};
    for (var i in AddPropertiesValue) {
      if (i != item.ItemIndex) {
        newAddPropertiesValue[i] = AddPropertiesValue[i];
      }
    }

    const newAddPropertiesTitle = {};
    for (const i in AddPropertiesTitle) {
      if (i !== item.ItemIndex) {
        newAddPropertiesTitle[i] = AddPropertiesTitle[i];
      }
    }

    const newcateProPerty = [];
    for (let i = 0; i < fields.length; i++) {
      if (fields[i].ItemIndex != item.ItemIndex) {
        newcateProPerty.push(fields[i]);
      }
    }

    // 从properties中删除对应的属性
    const newProperties = { ...properties };
    if (propertyNameToDelete && newProperties[propertyNameToDelete]) {
      delete newProperties[propertyNameToDelete];
    }

    this.setState({
      fields: newcateProPerty,
      AddPropertiesTitle: newAddPropertiesTitle,
      AddPropertiesValue: newAddPropertiesValue,
      properties: newProperties,
    });
  };

  // 处理属性表单值变化
  handlePropertyChange = (fieldname, value) => {
    this.setState(prevState => ({
      properties: {
        ...prevState.properties,
        [fieldname]: value,
      },
    }));
  };

  renderCateProperty = () => {
    const { fields, AddPropertiesValue, AddPropertiesTitle, properties } = this.state;
    let cateProPertyList = [];
    console.log('fields', fields);
    cateProPertyList = fields.map(item => {
      if (item.fieldtype === 'input') {
        return (
          <Col span={24}>
            <Form.Item
              label={item.displayname}
              labelAlign="left"
              name={item.fieldname}
              rules={[
                {
                  required: item.required,
                  message: `请输入${item.displayname}`,
                  whitespace: true,
                },
              ]}
            >
              <Input
                placeholder={`请输入${item.displayname}`}
                value={(properties && properties[item.fieldname]) || ''}
                onChange={e => this.handlePropertyChange(item.fieldname, e.target.value)}
              />
            </Form.Item>
          </Col>
        );
      }
      if (item.fieldtype === 'addinput') {
        return (
          <Row style={{ marginTop: '10px' }}>
            <Col span={8}>
              <Input
                placeholder="请输入参数名"
                defaultValue={AddPropertiesTitle && AddPropertiesTitle[item.ItemIndex]}
                onChange={e => {
                  this.changeAddProPertyItemTitle(e, item);
                }}
              />
            </Col>
            <div style={{ lineHeight: '30px' }}>:</div>
            <Col span={8}>
              <Input
                placeholder="请输入参数值"
                defaultValue={AddPropertiesValue && AddPropertiesValue[item.ItemIndex]}
                onChange={e => {
                  this.changeAddProPertyItemValue(e, item);
                }}
              />
            </Col>
            <Col span={4}>
              <Button
                type="primary"
                style={{ marginLeft: '5px' }}
                size="Small"
                onClick={e => {
                  this.delProperItem(e, item);
                }}
              >
                删除
              </Button>
            </Col>
          </Row>
        );
      }

      if (item.fieldtype === 'textarea') {
        return (
          <Col span={24}>
            <Form.Item
              label={item.displayname}
              name={item.fieldname}
              labelAlign="left"
              rules={[
                {
                  required: item.required,
                  message: `请输入${item.displayname}`,
                  whitespace: true,
                },
              ]}
            >
              <Input.TextArea
                value={(properties && properties[item.fieldname]) || ''}
                rows={6}
                onChange={e => this.handlePropertyChange(item.fieldname, e.target.value)}
              />
            </Form.Item>
          </Col>
        );
      }

      if (item.fieldtype === 'checkbox') {
        return (
          <Col span={24}>
            <Form.Item label={item.displayname} name={item.fieldname} labelAlign="left">
              <Checkbox.Group
                value={(properties && properties[item.fieldname]) || []}
                onChange={value => this.handlePropertyChange(item.fieldname, value)}
              >
                {item.fieldoptions.map(checkItem => (
                  <Checkbox value={checkItem.value ? checkItem.value : checkItem}>
                    {checkItem.label ? checkItem.label : checkItem}
                  </Checkbox>
                ))}
              </Checkbox.Group>
            </Form.Item>
          </Col>
        );
      }
      if (item.fieldtype === 'radio') {
        return (
          <Col span={24}>
            <Form.Item
              label={item.displayname}
              name={item.fieldname}
              labelAlign="left"
              rules={[
                {
                  required: item.required,
                  message: `请选择${item.displayname}`,
                  whitespace: true,
                },
              ]}
            >
              <Radio.Group
                value={(properties && properties[item.fieldname]) || ''}
                onChange={e => this.handlePropertyChange(item.fieldname, e.target.value)}
              >
                {item.fieldoptions.map(checkItem => (
                  <Radio value={checkItem.value ? checkItem.value : checkItem}>
                    {checkItem.label ? checkItem.label : checkItem}
                  </Radio>
                ))}
              </Radio.Group>
            </Form.Item>
          </Col>
        );
      }
      if (item.fieldtype === 'select') {
        return (
          <Col span={24}>
            <Form.Item
              label={item.displayname}
              labelAlign="left"
              name={item.fieldname}
              rules={[
                {
                  required: item.required,
                  message: `请选择${item.displayname}`,
                  whitespace: true,
                },
              ]}
            >
              <Select
                value={(properties && properties[item.fieldname]) || undefined}
                style={{ width: 200 }}
                dropdownStyle={{ zIndex: 5000 }}
                allowClear
                onChange={value => this.handlePropertyChange(item.fieldname, value)}
              >
                {item.fieldoptions.map(checkItem =>
                  checkItem.label ? (
                    <Select.Option value={checkItem.value}>{checkItem.label}</Select.Option>
                  ) : (
                    <Select.Option value={checkItem}>{checkItem}</Select.Option>
                  ),
                )}
              </Select>
            </Form.Item>
          </Col>
        );
      }
    });
    return cateProPertyList;
  };

  clearCache = id => {
    localStorage.removeItem(getCacheKey(id || this.state.id));
  };

  // 添加表单值变化处理方法
  handleFormValuesChange = (changedValues, allValues) => {
    if (!changedValues.properties) {
      this.setState(prevState => ({
        product: {
          ...prevState.product,
          ...changedValues,
        },
      }));
    }
  };

  render() {
    const CreateProPerty = () => {
      const { properties = {} } = this.state;
      const cateProPertyList = this.renderCateProperty();
      return (
        <div>
          <Form initialValues={properties} ref={this.dynFormRef} model={properties}>
            {cateProPertyList}
          </Form>
          <Row>
            <Button onClick={this.AddProperty} style={{ marginTop: 10 }}>
              添加自定义参数
            </Button>
          </Row>
        </div>
      );
    };

    const { cateOptions, cityOptions } = this.props;
    const { isOpenAi } = this.props;
    const { submiting, product, auditRes2 } = this.state;

    return (
      <div className={styles.custombox}>
        <div className={styles.alltitle}>
          <span>新增产品</span>
        </div>
        <div className={styles.customwrap}>
          {Object.keys(auditRes2).length > 0 && (
            <div className={styles.to_examine}>
              <p>审核未通过原因：</p>
              {Object.keys(auditRes2).map(item => (
                <p key={item}>
                  {keyMap[item]}：{auditRes2[item]}。
                </p>
              ))}
            </div>
          )}
          <div className={styles.stepbox}>
            <Steps
              current={this.state.currentStep}
              onChange={this.onStepChange}
              items={[
                {
                  title: '添加标题',
                },
                {
                  title: '添加素材',
                },
                {
                  title: '图片视频',
                },
                {
                  title: '属性分类',
                },
              ]}
            />
          </div>
          <Form
            ref={this.formRef}
            onFinish={this.onFinish}
            onFinishFailed={this.handleFailed}
            initialValues={{
              price: product.price,
              unit: product.unit,
              areaids: product.areaids,
              properties: product.properties,
              cate: product.cate,
              alias: product.alias,
              word: product.word,
              brand: product.brand,
              min_order: product.min_order,
              inventory: product.inventory,
              faq: product.faq,
            }}
            onValuesChange={this.handleFormValuesChange}
          >
            <Form.Item
              label={<span>单价</span>}
              labelCol={{ span: 3 }}
              wrapperCol={{ span: 18 }}
              name="price"
              rules={[{ type: 'number', min: 0, message: '请输入单价， 大于等于0' }]}
              help="0代表面议"
            >
              <InputNumber
                style={{
                  width: '100%',
                }}
                placeholder="请输入单价"
              />
            </Form.Item>

            <Form.Item
              label={<span>单位</span>}
              labelCol={{ span: 3 }}
              wrapperCol={{ span: 18 }}
              name="unit"
              rules={[{ required: true, message: '请输入单位' }]}
            >
              <Input placeholder="请输入单位" />
            </Form.Item>

            <Form.Item
              label={<span>面向地区</span>}
              labelCol={{ span: 3 }}
              wrapperCol={{ span: 18 }}
              name="areaids"
              help="面向地区默认全国"
            >
              <Cascader
                options={cityOptions}
                onChange={this.onChangeCity}
                defaultValue={product.areaids}
                placeholder="全国"
                changeOnSelect
                getPopupContainer={triggerNode => triggerNode.parentNode}
              />
              {this.state.areaidsTip && this.state.areaidsTip.length > 0 && (
                <Alert
                  type="warning"
                  description={`当前选择城市无法发布到以下平台：${this.state.areaidsTip}`}
                  closable
                  banner
                />
              )}
            </Form.Item>

            <Form.Item
              label={<span>品牌</span>}
              labelCol={{ span: 3 }}
              wrapperCol={{ span: 18 }}
              name="brand"
              rules={[
                { max: 20, message: '品牌字数在20字以内' },
                {
                  validator: (_, value) => {
                    if (value !== undefined && value !== product.name) {
                      return Promise.resolve();
                    }
                    // eslint-disable-next-line prefer-promise-reject-errors
                    return Promise.reject('品牌不能与产品名称相同');
                  },
                },
              ]}
            >
              <Input placeholder="请输入品牌" />
            </Form.Item>

            <Form.Item
              label={<span>起订量</span>}
              labelCol={{ span: 3 }}
              wrapperCol={{ span: 18 }}
              name="min_order"
              help=""
            >
              <InputNumber
                style={{
                  width: '100%',
                }}
                placeholder="请输入起订量"
              />
            </Form.Item>

            <Form.Item
              label={<span>库存</span>}
              labelCol={{ span: 3 }}
              wrapperCol={{ span: 18 }}
              name="inventory"
              help=""
            >
              <InputNumber
                style={{
                  width: '100%',
                }}
                placeholder="请输入库存"
              />
            </Form.Item>

            <Form.Item
              label={<span>分类</span>}
              labelCol={{ span: 3 }}
              wrapperCol={{ span: 18 }}
              name="cate"
              help="分类需要选到最后一级"
              rules={[
                {
                  required: true,
                  message: '请选择分类',
                },
                {
                  validator: (_, value) => {
                    if (this.state.isCateLast) {
                      return Promise.resolve();
                    }
                    // eslint-disable-next-line prefer-promise-reject-errors
                    return Promise.reject('分类需要选到最后一级');
                  },
                },
              ]}
            >
              <Cascader
                options={cateOptions}
                onChange={this.onChangeCate}
                defaultValue={product.cate}
                placeholder="分类需要选择到最后一级"
                showSearch={this.filter}
                changeOnSelect
                getPopupContainer={triggerNode => triggerNode.parentNode}
              />
              {this.state.cateTip && this.state.cateTip.length > 0 && (
                <Alert
                  type="warning"
                  description={`当前选择分类无法发布到以下平台：${this.state.cateTip}`}
                  closable
                  banner
                />
              )}
            </Form.Item>

            <Form.Item
              label={<span>产品属性参数</span>}
              labelCol={{ span: 3 }}
              wrapperCol={{ span: 18 }}
              name="properties"
            >
              <Row className={styles.productdata_bg} style={{ padding: '5px' }}>
                <CreateProPerty />
              </Row>
            </Form.Item>

            <Form.Item
              label={<span>产品问答</span>}
              labelCol={{ span: 3 }}
              wrapperCol={{ span: 18 }}
              name="faq"
              rules={[{ max: 400, message: '最多可添加400字' }]}
              help="最多可添加400字"
            >
              <Input.TextArea showCount maxLength={400} placeholder="请输入产品问答" rows={6} />
            </Form.Item>

            <div className={styles.addtitle_submit}>
              <Space>
                <Button type="primary" htmlType="submit" loading={submiting}>
                  提交审核
                </Button>
              </Space>
            </div>
          </Form>
        </div>
      </div>
    );
  }
}

export default connect(({ home, merchant }) => ({
  ...home,
  ...merchant,
}))(NewlyaddStep4);
