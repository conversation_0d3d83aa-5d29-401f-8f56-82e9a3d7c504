import React from 'react';
import {
  Row,
  Steps,
  Col,
  Form,
  Input,
  Checkbox,
  Radio,
  Button,
  Tag,
  Space,
  message,
  Modal,
} from 'antd';

import styles from './Custom.less';
// import CustomTitle from '@/components/CustomTitle';
// import KeywordDig from '@/components/KeywordDig';
// import AutoGenerate from '@/components/AutoGenerate';
import { combineArray } from '@/utils/autoKeyword';
import DigKeyword from '@/components/DigKeyword';
import KeyWord from '@/components/KeyWord';
import calculateHash from '@/utils/hash';
import CustomTitle from '@/components/CustomTitle';
import { keyMap } from '@/utils/constant';
import { router } from 'umi';

import { connect } from 'dva';
import { update } from 'lodash';
import { updateProduct } from '@/services/product';
import { title } from '@/configs/config';
const { TextArea } = Input;
const { Step } = Steps;

// checkbox
const onChange = checkedValues => {
  console.log('checked = ', checkedValues);
};

// tag

const log = e => {
  console.log(e);
};
const preventDefault = e => {
  e.preventDefault();
  console.log('Clicked! But prevent default.');
};

// 添加缓存key生成方法
const getCacheKey = productId => {
  return `newlyadd_step1_cache_${productId || 'new'}`;
};

class NewlyaddStep1 extends React.PureComponent {
  formRef = React.createRef();

  state = {
    // eslint-disable-next-line react/no-unused-state
    product: {
      mode: 0,
      name: '',
      option_title: [],
      word: [], // 关键词，等同于keywords
      cache: {
        keywords: [],
        alias: [],
        subtitles: [],
      },
    },
    preProduct: {
      mode: 0,
      name: '',
      option_title: [],
      word: [], // 关键词，等同于keywords
      cache: {
        keywords: [],
        alias: [],
        subtitles: [],
      },
      status: 0,
    },
    id: 0,
    hashStr: '',
    customList: [], // 自定义标题列表
    option_title: [],
    methods: [],
    selectedSepSymbol: '',
    // 分隔符号
    calOptions: [
      { label: '关键词', value: 'k' },
      { label: '关键词+别名', value: 'k#a' },
      { label: '关键词+副标题', value: 'k#t' },
      { label: '别名+关键词', value: 'a#k' },
      { label: '副标题+关键词', value: 't#k' },
      { label: '关键词+别名+副标题', value: 'k#a#t' },
      { label: '关键词+副标题+别名', value: 'k#t#a' },
      { label: '别名+关键词+副标题', value: 'a#k#t' },
      { label: '别名+副标题+关键词', value: 'a#t#k' },
      { label: '副标题+关键词+别名', value: 't#k#a' },
      { label: '副标题+别名+关键词', value: 't#a#k' },
    ],
    // model 控制
    showDigKeywords: false,
    isShowKeyWord: false,
    keywordType: 'k',
    maxLen: 30,
    itemList: [],
    submiting: false, // 提交中
    currentStep: 0,
    auditRes2: [],
    fullAuditRes2: [],
  };

  sepSymbols = [{ label: '无', value: '' }, { label: ',', value: ',' }, { label: '-', value: '-' }];

  componentDidMount() {
    this.props.dispatch({ type: 'home/getCity', payload: {} });

    // 获取产品ID
    let { id } = this.props.match.params;
    id = id ? parseInt(id, 10) : 0;

    // 检查缓存
    const cache = localStorage.getItem(getCacheKey(id));
    if (cache) {
      Modal.confirm({
        title: '发现未保存的编辑内容',
        content: '是否恢复上次未保存的编辑内容？',
        okText: '恢复',
        cancelText: '不需要',
        onOk: () => {
          const cachedData = JSON.parse(cache);
          this.setState(
            {
              product: cachedData.product,
              option_title: cachedData.option_title,
              id,
            },
            () => {
              this.formRef.current.setFieldsValue({
                name: cachedData.product.name,
                option_title: cachedData.option_title,
                word: cachedData.product.cache.keywords,
              });
            },
          );
        },
        onCancel: () => {
          this.clearCache(id);
          this.loadProduct();
        },
      });
    } else {
      this.loadProduct();
    }
  }

  loadProduct = () => {
    let { id } = this.props.match.params;
    if (!id) {
      return;
    }
    id = parseInt(id, 10);
    this.props
      .dispatch({
        type: 'product/getProductById',
        payload: { product_id: id },
      })
      .then(result => {
        // 检查产品标题状态，如果标题已用完则显示提示弹窗
        if (result.status === 6 || result.status === 8) {
          Modal.warning({
            title: '提示',
            content:
              '该产品标题已用完，请重新添加新标题，否则直接影响推广效果，屡次不改会禁止更新本产品。',
            okText: '我知道了',
            zIndex: 9999,
          });
        }

        if (!result.cache) {
          result.cache = {
            keywords: result.word || [],
            alias: result.alias || [],
            subtitles: result.title || [],
          };
        }

        result.option_title = result.option_title || [];

        this.setState(({ product }) => {
          // 处理result.audit_res2 只保留product也存在的属性
          const auditRes2 = {};
          for (const key in result.audit_res2) {
            if (Object.prototype.hasOwnProperty.call(product, key)) {
              auditRes2[key] = result.audit_res2[key];
            }
          }
          const updatedProduct = {
            ...product,
            name: result.name,
            cache: result.cache,
            option_title: result.option_title,
          };

          const hashStr = calculateHash(updatedProduct);

          return {
            id,
            product: updatedProduct,
            preProduct: {
              ...updatedProduct,
              status: typeof result.status === 'number' ? result.status : 0,
            },
            hashStr,
            option_title: result.option_title,
            auditRes2,
            fullAuditRes2: result.audit_res2,
          };
        });
        // 更新form表单
        this.formRef.current.setFieldsValue({
          name: result.name,
          option_title: result.option_title,
          word: result.cache.keywords,
        });
        console.log('loadProduct', result);
      })
      .catch(err => {
        console.log(err);
      });
  };

  onStepChange = (current, step) => {
    if (current < this.state.currentStep) {
      router.push(`/publish/products/add${current + 1}/${this.state.id}`);
    }
  };

  showDigKeywordForKeyword = () => {
    this.showDigKeywordFor('k');
  };

  showDigKeywordForTitle = () => {
    this.showDigKeywordFor('t');
  };

  showDigKeywordFor = keywordType => {
    this.setState({
      showDigKeywords: true,
      keywordType,
    });
  };

  hideDigKeyword = () => {
    this.setState({
      showDigKeywords: false,
    });
  };

  onSaveFromDig = list => {
    this.onKeywordListUpdated(list);
    this.hideDigKeyword();
  };

  showAutoKeyword = () => {
    this.showKeyWord('k', 20, this.state.product.cache.keywords);
  };

  showAutoTitle = () => {
    this.showKeyWord('t', 15, this.state.product.cache.subtitles);
  };

  showKeyWord = (keywordType, maxLen, itemList) => {
    this.setState({
      isShowKeyWord: true,
      keywordType,
      maxLen,
      itemList,
    });
  };

  hideKeyWord = () => {
    this.setState({
      isShowKeyWord: false,
    });
  };

  onKeywordListUpdated = keywordList => {
    switch (this.state.keywordType) {
      case 'k':
        this.setState(
          ({ product }) => ({
            isShowKeyWord: false,
            product: {
              ...product,
              cache: {
                ...product.cache,
                keywords: product.cache.keywords.concat(keywordList),
              },
            },
          }),
          () => {
            // update field
            this.formRef.current.setFieldsValue({
              word: this.state.product.cache.keywords,
            });
            this.saveToCache();
          },
        );
        break;
      case 't':
        this.setState(({ product }) => ({
          isShowKeyWord: false,
          product: {
            ...product,
            cache: {
              ...product.cache,
              subtitles: product.cache.subtitles.concat(keywordList),
            },
          },
        }));
        break;
    }
  };

  onFinish = values => {
    const { id: productId, auditRes2, product, preProduct, fullAuditRes2 } = this.state;
    let newAuditRes2 = { ...fullAuditRes2 };
    if (productId === 0) {
      this.setState({ submiting: true });
      this.props
        .dispatch({
          type: 'product/addProduct',
          payload: {
            mode: 0,
            alias: this.state.product.cache.alias,
            name: values.name,
            option_title: values.option_title,
            cache: this.state.product.cache,
            word: values.word,
            title: this.state.product.cache.subtitles,
          },
        })
        .then(result => {
          this.setState({ submiting: false });
          const { msg, data } = result;
          this.clearCache();
          router.push(`/publish/products/add2/${data.id}`);
        })
        .catch(err => {
          this.setState({ submiting: false });
          message.error(err.msg || err.message);
        });
    } else {
      for (const key in auditRes2) {
        let element = values[key];
        let target = preProduct[key];
        // 关键词特殊处理下
        if (key === 'word') {
          element = values[key].join(',');
          target = preProduct.cache.keywords.join(',');
        }
        if (JSON.stringify(element) === JSON.stringify(target)) {
          message.error(`请修改${keyMap[key]}的值: ${auditRes2[key]}`);
          return;
        }
        delete newAuditRes2[key];
      }

      // 判断标题是否已更新
      // 使用数组的副本进行排序比较，不修改原始数组
      const originalTitles = JSON.stringify([...preProduct.option_title].sort());
      const currentTitles = JSON.stringify([...values.option_title].sort());

      if (
        (preProduct.status === 6 || preProduct.status === 8) &&
        originalTitles === currentTitles &&
        productId !== 0
      ) {
        Modal.warning({
          title: '提示',
          content:
            '该产品标题已用完，请重新添加新标题，否则直接影响推广效果，屡次不改会禁止更新本产品。',
          okText: '我知道了',
          zIndex: 9999,
        });
        return;
      }

      this.state.product.name = values.name;
      this.state.product.option_title = values.option_title;
      const newHash = calculateHash(this.state.product);
      if (newHash !== this.state.hashStr) {
        this.setState({ submiting: true });
        // 有变化， 调用更新接口
        this.props
          .dispatch({
            type: 'product/updateProductById',
            payload: {
              product_id: productId,
              name: values.name,
              option_title: values.option_title,
              cache: this.state.product.cache,
              word: values.word,
              alias: this.state.product.cache.alias,
              title: this.state.product.cache.subtitles,
              audit_res2: newAuditRes2,
            },
          })
          .then(result => {
            this.setState({ submiting: false });
            this.clearCache();
            router.push(`/publish/products/add2/${productId}`);
          })
          .catch(err => {
            this.setState({ submiting: false });
            message.error(err.msg || err.message);
          });
      } else {
        this.setState({ submiting: false });
        this.clearCache();
        router.push(`/publish/products/add2/${productId}`);
      }
    }
  };

  handleFailed = err => {
    message.error(err.errorFields[0].errors[0]);
  };

  // tag
  onItemRemoved = item => {
    console.log('onItemRemoved', item);
    //  从 option_title 里 删除掉item, 并更新 state和form表单
    this.setState(
      // eslint-disable-next-line @typescript-eslint/camelcase
      ({ option_title }) => ({
        option_title: option_title.filter(i => i !== item),
      }),
      () => {
        this.formRef.current.setFieldsValue({
          option_title: this.state.option_title,
        });
        this.saveToCache();
      },
    );
  };

  onChangeMethod = checkedValues => {
    this.setState({
      methods: checkedValues,
    });
  };

  onChangeSepSymbol = e => {
    this.setState({
      selectedSepSymbol: e.target.value,
    });
  };

  clearAll = () => {
    this.setState(
      () => ({
        option_title: [],
      }),
      () => {
        // 更新表单
        this.formRef.current.setFieldsValue({
          option_title: [],
        });
        this.saveToCache();
      },
    );
  };

  hasErr = (list, maxPerLen, maxLen) => {
    if (!list) {
      return false;
    }
    if (list.length > maxLen) {
      return true;
    }
    for (const i in list) {
      const item = list[i];
      if (!this.isTitleOk(item, maxPerLen)) {
        return true;
      }
    }
    return false;
  };

  isTitleOk = (title, maxPerLen) => {
    const numberCn = /^[0-9]+$/im;
    const letterCn = /^[A-Za-z]+$/im;
    if (title.length > maxPerLen) {
      return false;
    }
    if (numberCn.test(title) || letterCn.test(title)) {
      return false;
    }
    return true;
  };

  checkTitleError = (title, maxPerLen) => {
    const numberCn = /^[0-9]+$/im;
    const letterCn = /^[A-Za-z]+$/im;
    if (title.length > maxPerLen) {
      return `不能超过${maxPerLen}字`;
    }
    if (numberCn.test(title)) {
      return '不能为纯数字';
    }
    if (letterCn.test(title)) {
      return '不能为纯字母';
    }
    return '';
  };

  onKeywordsChange = e => {
    const v = e.target.value;
    const newlist = v.split(/\r\n|\r|\n|\s+/);
    this.setState(
      ({ product }) => ({
        product: {
          ...product,
          cache: {
            ...product.cache,
            keywords: newlist,
          },
          word: newlist,
        },
      }),
      () => {
        this.formRef.current.setFieldsValue({
          word: newlist,
        });
        this.saveToCache();
      },
    );
  };

  onTitleChange = e => {
    const v = e.target.value;
    const newlist = v.split(/\r\n|\r|\n|\s+/);
    this.setState(
      ({ product }) => ({
        product: {
          ...product,
          cache: {
            ...product.cache,
            subtitles: newlist,
          },
        },
      }),
      () => {
        this.saveToCache();
      },
    );
  };

  onAliasChange = e => {
    const v = e.target.value;
    const newlist = v.split(/\r\n|\r|\n|\s+/);

    this.setState(
      ({ product }) => ({
        product: {
          ...product,
          cache: {
            ...product.cache,
            alias: newlist,
          },
        },
      }),
      () => {
        this.saveToCache();
      },
    );
  };

  removeDuplicated = list => {
    const items = [];
    for (const i in list) {
      if (items.indexOf(list[i]) === -1) {
        items.push(list[i]);
      }
    }
    return items;
  };

  removeTooLarge = (list, limitSize) => {
    const items = [];
    for (const i in list) {
      if (list[i].length <= limitSize) {
        items.push(list[i]);
      }
    }
    return items;
  };

  generateTitles = () => {
    // 生成标题会比较慢
    let { keywords, subtitles, alias } = this.state.product.cache;
    keywords = keywords.filter(item => item.length > 0);
    subtitles = subtitles.filter(item => item.length > 0);
    alias = alias.filter(item => item.length > 0);
    const errors = [];
    for (const item of keywords) {
      const err = this.checkTitleError(item, 20);
      if (err != '' && errors.indexOf(`关键词${err}`) === -1) {
        errors.push(`关键词${err}`);
      }
    }
    for (const item of subtitles) {
      const err = this.checkTitleError(item, 15);
      if (err != '' && errors.indexOf(`副标题${err}`) === -1) {
        errors.push(`副标题${err}`);
      }
    }
    for (const item of alias) {
      const err = this.checkTitleError(item, 15);
      if (err != '' && errors.indexOf(`别名${err}`) === -1) {
        errors.push(`别名${err}`);
      }
    }
    // for (const item of optionTitleList) {
    //   const err = this.checkTitleError(item, 30);
    //   if (err != '' && errors.indexOf(`自定义标题${err}`) === -1) {
    //     errors.push(`自定义标题${err}`);
    //   }
    // }
    const doSave = () => {
      let titles = this.genTitles();
      // titles 合并下 option_title
      titles = titles.concat(this.state.option_title);

      const len1 = titles.length;
      let pureList = this.removeDuplicated(titles);
      const len2 = pureList.length;
      pureList = this.removeTooLarge(pureList, 30);
      const len3 = pureList.length;
      let tip = '';
      if (len1 !== len2) {
        tip = '部分标题重复，已去重';
      }
      if (len2 !== len3) {
        tip = '部分标题超过30个字已删除';
      }
      if (len3 > 2000) {
        tip = '标题最多添加2000个';
        pureList = pureList.slice(0, 2000);
      }

      if (tip !== '') {
        Modal.confirm({
          title: '提示',
          content: tip,
          zIndex: 9999,
          cancelText: '取消',
          okText: '确定',
          onOk: () => {
            this.setState(
              ({ product }) => ({
                option_title: pureList,
                product: {
                  ...product,
                  cache: {
                    ...product.cache,
                    alias,
                    keywords,
                    subtitles,
                  },
                },
              }),
              () => {
                this.formRef.current.setFieldsValue({
                  option_title: pureList,
                });
                this.saveToCache();
              },
            );
          },
          onCancel() {},
        });
      } else {
        this.setState(
          ({ product }) => ({
            option_title: pureList,
            product: {
              ...product,
              cache: {
                ...product.cache,
                alias,
                keywords,
                subtitles,
              },
            },
          }),
          () => {
            this.formRef.current.setFieldsValue({
              option_title: pureList,
            });
            this.saveToCache();
          },
        );
      }
    };

    if (errors.length > 0) {
      Modal.confirm({
        title: '提示',
        content: `${errors.join(',')},已去除`,
        zIndex: 9999,
        okText: '确定',
        onOk: () => {
          keywords = keywords.filter(item => this.checkTitleError(item, 20) === '');
          subtitles = subtitles.filter(item => this.checkTitleError(item, 15) === '');
          alias = alias.filter(item => this.checkTitleError(item, 15) === '');
          this.setState(
            ({ product }) => ({
              product: {
                ...product,
                cache: {
                  ...product.cache,
                  alias,
                  keywords,
                  subtitles,
                },
              },
            }),
            () => {
              // 访问更新后的状态值
              doSave();
            },
          );
        },
        onCancel() {},
      });
    } else {
      doSave();
    }
  };

  genTitles = () => {
    const { product, methods, selectedSepSymbol } = this.state;
    const resultList = [];
    const typemapping = {
      a: product.cache.alias.filter(item => item.length > 0), // 别名
      k: product.cache.keywords.filter(item => item.length > 0), // 关键词
      t: product.cache.subtitles.filter(item => item.length > 0), // 副标题
    };
    for (const i in methods) {
      const types = methods[i].split('#');
      const arrs = [];
      let ok = true;
      for (const j in types) {
        const options = typemapping[types[j]];
        if (!options || options.length == 0) {
          ok = false;
          break;
        }
        arrs.push(options);
      }
      if (!ok) {
        continue;
      }
      let titles = [];
      titles = combineArray(arrs, selectedSepSymbol);

      for (const k in titles) {
        const title = titles[k];
        if (resultList.length >= 2000) {
          break;
        }
        if (resultList.indexOf(title) == -1) {
          resultList.push(title.replace(/\s+/g, ''));
        }
      }
    }
    for (let i = 1; i < resultList.length; i++) {
      const random = Math.floor(Math.random() * (i + 1));
      [resultList[i], resultList[random]] = [resultList[random], resultList[i]];
    }
    return resultList;
  };

  handleProductNameChange = e => {
    const newName = e.target.value;
    this.setState(
      ({ product }) => ({
        product: {
          ...product,
          name: newName,
        },
      }),
      () => {
        this.formRef.current.setFieldsValue({
          name: newName,
        });
        this.saveToCache();
      },
    );
  };

  // 添加保存缓存方法
  saveToCache = () => {
    const cacheData = {
      product: this.state.product,
      option_title: this.state.option_title,
    };
    localStorage.setItem(getCacheKey(this.state.id), JSON.stringify(cacheData));
  };

  // 添加清除缓存方法
  clearCache = id => {
    localStorage.removeItem(getCacheKey(id || this.state.id));
  };

  // 智能乱序功能 - 随机打乱标题顺序
  shuffleTitles = () => {
    const { option_title } = this.state;

    if (option_title.length === 0) {
      message.info('没有标题可以打乱顺序');
      return;
    }

    // 创建标题数组的副本并打乱顺序
    const shuffledTitles = [...option_title];
    for (let i = shuffledTitles.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffledTitles[i], shuffledTitles[j]] = [shuffledTitles[j], shuffledTitles[i]];
    }

    // 更新状态和表单
    this.setState(
      {
        option_title: shuffledTitles,
      },
      () => {
        // 更新表单
        this.formRef.current.setFieldsValue({
          option_title: shuffledTitles,
        });
        this.saveToCache();
        message.success('标题顺序已随机打乱');
      },
    );
  };

  render() {
    const {
      product,
      selectedSepSymbol,
      calOptions,
      customList,
      maxLen,
      itemList,
      currentStep,
      auditRes2,
      option_title,
    } = this.state;
    const keywordCtrl = {
      text: product.cache && product.cache.keywords ? product.cache.keywords.join('\r\n') : '',
      num:
        product.cache && product.cache.keywords
          ? product.cache.keywords.filter(item => item.length > 0).length
          : 0,
      hasErr: this.hasErr(product.cache && product.cache.keywords, 20, 2000),
    };

    const aliasCtrl = {
      text: product.cache && product.cache.alias ? product.cache.alias.join('\r\n') : '',
      num:
        product.cache && product.cache.alias
          ? product.cache.alias.filter(item => item.length > 0).length
          : 0,
      hasErr: this.hasErr(product.cache && product.cache.alias, 15, 100),
    };
    const titleCtrl = {
      text: product.cache && product.cache.subtitles ? product.cache.subtitles.join('\r\n') : '',
      num:
        product.cache && product.cache.subtitles
          ? product.cache.subtitles.filter(item => item.length > 0).length
          : 0,
      hasErr: this.hasErr(product.cache && product.cache.subtitles, 15, 2000),
    };
    return (
      <div className={styles.custombox}>
        <div className={styles.alltitle}>
          <span>新增产品</span>
        </div>
        <div className={styles.customwrap}>
          {Object.keys(auditRes2).length > 0 && (
            <div className={styles.to_examine}>
              <p>审核未通过原因：</p>
              {Object.keys(auditRes2).map(item => (
                <p key={item}>
                  {keyMap[item]}：{auditRes2[item]}。
                </p>
              ))}
            </div>
          )}
          <div className={styles.stepbox}>
            <Steps current={currentStep} onChange={this.onStepChange}>
              <Step title="添加标题" />
              <Step title="添加素材" />
              <Step title="图片视频" />
              <Step title="属性分类" />
            </Steps>
          </div>
          <Form
            ref={this.formRef}
            onFinish={this.onFinish}
            onFinishFailed={this.handleFailed}
            initialValues={{
              name: product.name,
              option_title: product.option_title,
              word: product.cache.keywords,
            }}
          >
            <Row>
              <Col span={24}>
                <Form.Item
                  label={<span>产品名称</span>}
                  labelCol={{ span: 2 }}
                  wrapperCol={{ span: 22 }}
                  name="name"
                  rules={[
                    { required: true, message: '请输入产品名称' },
                    { max: 20, message: '产品名称字数不超过20个字' },
                  ]}
                  help="产品名称字数不超过20个字"
                >
                  <Input placeholder="请输入产品名称" onChange={this.handleProductNameChange} />
                </Form.Item>
              </Col>
              <Col span={2} style={{ textAlign: 'right' }}>
                组合生成标题：
              </Col>
              <Col span={22}>
                <Row gutter={[8, 8]} justify="space-between" className={styles.customright}>
                  <Col span={5}>
                    <div className={styles.title_head}>
                      关键词[{keywordCtrl.num}]
                      <span onClick={this.showDigKeywordForKeyword}>关键词挖掘</span>
                      <span onClick={this.showAutoKeyword}>自动生成</span>
                    </div>

                    <TextArea
                      rows={8}
                      placeholder="请输入关键词"
                      onChange={this.onKeywordsChange}
                      value={keywordCtrl.text}
                      style={{
                        border: keywordCtrl.hasErr ? '1px solid red' : '1px solid #d3d3d3',
                        width: '100%',
                        overflow: 'scroll',
                        overflowX: 'hidden',
                      }}
                    />
                    <Form.Item
                      labelCol={{ span: 2 }}
                      wrapperCol={{ span: 22 }}
                      name="word"
                      rules={[{ type: 'array', max: 2000, message: '关键词不能超过2000个' }]}
                    ></Form.Item>
                    <div className={styles.rule}>
                      换行分隔，每个关键词不超过20个字，且不能是纯数字或纯字母，最多可添加2000个。
                    </div>
                  </Col>
                  <Col span={5}>
                    <div className={styles.title_head}>
                      副标题[{titleCtrl.num}]
                      <span onClick={this.showDigKeywordForTitle}>关键词挖掘</span>
                      <span onClick={this.showAutoTitle}>自动生成</span>
                    </div>

                    <TextArea
                      rows={8}
                      onChange={this.onTitleChange}
                      value={titleCtrl.text}
                      placeholder="请输入关键词"
                      style={{
                        border: titleCtrl.hasErr ? '1px solid red' : '1px solid #d3d3d3',
                        width: '100%',
                        overflow: 'scroll',
                        overflowX: 'hidden',
                      }}
                    />

                    <div className={styles.rule}>
                      换行分隔，每个关键词不超过15个字，且不能是纯数字或纯字母，最多可添加2000个。
                    </div>
                  </Col>
                  <Col span={5}>
                    <div className={styles.title_head}>别名[{aliasCtrl.num}]</div>
                    <TextArea
                      rows={8}
                      value={aliasCtrl.text}
                      onChange={this.onAliasChange}
                      placeholder="请输入关键词"
                      style={{
                        border: aliasCtrl.hasErr ? '1px solid red' : '1px solid #d3d3d3',
                        width: '100%',
                        overflow: 'scroll',
                        overflowX: 'hidden',
                      }}
                    />
                    <div className={styles.rule}>
                      换行分隔，每个关键词不超过15个字，且不能是纯数字或纯字母，最多可添加100个。
                    </div>
                  </Col>
                  <Col span={9}>
                    <div className={styles.last_title_head}>组合方式：</div>
                    <Checkbox.Group style={{ width: '100%' }} onChange={this.onChangeMethod}>
                      <Row>
                        {calOptions.map((item, index) => (
                          <Col span={12} key={index}>
                            <Checkbox value={item.value}>{item.label}</Checkbox>
                          </Col>
                        ))}
                        <Col span={12}></Col>
                      </Row>
                    </Checkbox.Group>
                    <div className={styles.title_head}>分隔符号：</div>
                    <Radio.Group
                      value={selectedSepSymbol}
                      name="checkboxgroup"
                      onChange={this.onChangeSepSymbol}
                      options={this.sepSymbols}
                    />
                    <div className={styles.generatetitle_butn}>
                      <Button type="primary" block onClick={this.generateTitles}>
                        生成标题
                      </Button>
                    </div>
                  </Col>
                </Row>
              </Col>
              <Col span={24}>
                <Form.Item
                  label={<span>已添加标题</span>}
                  labelCol={{ span: 2 }}
                  wrapperCol={{ span: 22 }}
                  name="option_title"
                  rules={[
                    { required: true, message: '请生成标题' },
                    { type: 'array', min: 200, max: 2000, message: '标题个数在200~2000个' },
                  ]}
                >
                  <Row
                    justify="space-between"
                    className={styles.customright}
                    style={{ height: '200px', overflowY: 'scroll' }}
                  >
                    {option_title.map((item, index) => (
                      // eslint-disable-next-line react/no-array-index-key
                      <Col span={7} key={index}>
                        <Tag closable onClose={() => this.onItemRemoved(item)}>
                          {item}
                        </Tag>
                      </Col>
                    ))}
                  </Row>
                  <div className={styles.added_bottom}>
                    <span>
                      当前共<font>{option_title.length}</font>
                      个标题，最少添加200个，最多添加2000个。标题将按照显示顺序使用，如需打乱显示顺序，点击智能乱序。
                    </span>
                    <Space>
                      <CustomTitle
                        titles={customList}
                        maxNum={2000 - option_title.length}
                        onTitleChange={titles => {
                          this.setState(
                            ({ option_title }) => ({
                              option_title: option_title.concat(titles),
                            }),
                            () => {
                              // 更新表单
                              this.formRef.current.setFieldsValue({
                                option_title: this.state.option_title,
                              });
                              this.saveToCache();
                            },
                          );
                        }}
                      />
                      <Button type="primary" onClick={this.shuffleTitles}>
                        智能乱序
                      </Button>
                      <Button onClick={this.clearAll}>清空</Button>
                    </Space>
                  </div>
                </Form.Item>
              </Col>
            </Row>
            <div className={styles.addtitle_submit}>
              <Space>
                <Button type="primary" htmlType="submit" loading={this.state.submiting}>
                  保存进入下一步
                </Button>
              </Space>
            </div>
          </Form>
        </div>
        {this.state.showDigKeywords && (
          <DigKeyword close={this.hideDigKeyword} onSave={this.onSaveFromDig} />
        )}
        {this.state.isShowKeyWord && (
          <KeyWord
            maxLen={maxLen}
            productName={product.name}
            close={this.hideKeyWord}
            keywordList={itemList}
            onSaveBtn={item => {
              this.onKeywordListUpdated(item);
            }}
          />
        )}
      </div>
    );
  }
}

export default connect(({ home }) => ({ ...home }))(NewlyaddStep1);
