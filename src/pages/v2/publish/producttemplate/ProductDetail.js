import React from 'react';
import {
  Row,
  Col,
  Image,
  Cascader,
  Form,
  Select,
  Radio,
  Checkbox,
  Space,
  Input,
  Button,
} from 'antd';
import styles from './ProductDetail.less';
import TitleGroup from '../../../../components/TitleGroup';
import DeailGroup from '../../../../components/DeailGroup';
import 'video.js/dist/video-js.css';
import Video from '@/components/Video';
import { CloseCircleOutlined } from '@ant-design/icons';
import CustomUploadDisplaypic from '@/components/CustomUploadDisplaypic';
import CustomUploadDisplayvideo from '@/components/CustomUploadDisplayvideo';
import { connect } from 'dva';
class ProductDetail extends React.PureComponent {
  state = {
    // eslint-disable-next-line react/no-unused-state
    product: {
      mode: 1,
      name: '',
      option_title: [],
      cache: {
        keywords: [],
        alias: [],
        subtitles: [],
      },
      temp_content: '',
      vars: {
        图片组: [],
        素材内容组1: [],
        素材内容组2: [],
        素材内容组3: [],
        素材内容组4: [],
      },
      titlepic: [], // 标题图片
      videos: [], // 视频
      price: null, // 价格，0-面议
      unit: null, // 单位
      area_names: ['全国'], // 面向地区
      properties: {}, // 属性参数
      cate_names: [], // 分类
      alias: [], // 产品别名
      word: [], // 关键词
      brand: '', // 品牌
    },
    // 动态属性
    properties: {}, // 编辑的属性
    fields: [], // 动态分类表单
    AddPropertiesTitle: {}, // 动态添加的属性名
    AddPropertiesValue: {}, // 动态添加的属性字段
  };

  componentDidMount() {
    this.loadProduct();
  }

  arrString2json(arr) {
    const result = [];
    if (!arr) {
      return [];
    }
    for (const item of arr) {
      try {
        result.push(JSON.parse(item));
      } catch (e) {
        result.push({ url: item, name: '图片名称' });
      }
    }
    return result;
  }

  loadProduct = () => {
    let { id } = this.props;
    if (!id) {
      return;
    }
    id = parseInt(id, 10);
    this.props
      .dispatch({
        type: 'product/getProductById',
        payload: { product_id: id },
      })
      .then(result => {
        this.setState(({ product }) => {
          let vars = product.vars;
          const aliasOptions = [];
          const keywordOptions = [];

          if (result.vars) {
            vars = { ...product.vars, ...result.vars };
          }
          const updatedProduct = {
            ...product,
          };
          // 用 result 来更新 product
          for (const key in result) {
            if (Object.prototype.hasOwnProperty.call(product, key)) {
              updatedProduct[key] = result[key];
            }
          }
          updatedProduct.titlepic = this.arrString2json(result.titlepic || []);
          updatedProduct.videos = this.arrString2json(result.videos || []);
          updatedProduct.vars = result.vars || {};
          // 处理null对象，给默认值
          updatedProduct.alias = result.alias || [];
          updatedProduct.word = result.word || [];
          updatedProduct.areaids = result.areaids && result.areaids.filter(item => item !== '0');
          updatedProduct.cate = result.cate || [];
          updatedProduct.properties = result.properties || {};
          updatedProduct.area_names = result.area_names || ['全国'];
          updatedProduct.cate_names = result.cate_names || [];

          // 把result.cache.alias 处理成select的option格式
          if (result.cache && result.cache.alias) {
            result.cache.alias.forEach(element => {
              aliasOptions.push({ value: element, label: element });
            });
          }
          if (result.cache && result.cache.keywords) {
            result.cache.keywords.forEach(element => {
              keywordOptions.push({ value: element, label: element });
            });
          }
          updatedProduct.aliasOptions = aliasOptions;
          updatedProduct.keywordOptions = keywordOptions;

          if (updatedProduct.cate.length > 0) {
            this.getCateProperty(
              updatedProduct.cate[updatedProduct.cate.length - 1],
              updatedProduct.properties,
            );
          }

          return {
            id,
            product: updatedProduct,
          };
        });
        // 更新form表单
        this.formRef.current.setFieldsValue({
          name: result.name,
          option_title: result.option_title,
        });
      })
      .catch(err => {
        console.log(err);
      });
  };

  getCateProperty = (id, properties = null) => {
    const { isOpenBaixing } = this.props;
    const reset = properties == null;
    this.props.dispatch({
      type: 'product/getCateProperty',
      payload: { id, merge: isOpenBaixing ? '1' : '0' },

      callBack: fields => {
        let cateProPerty = [...fields];
        let AddPropertiesTitle = {};
        let AddPropertiesValue = {};
        this.setState(preState => {
          if (!reset) {
            for (const i in properties) {
              let isCustomizeField = true;
              for (const j in fields) {
                // i == fields[j].fieldname or properties[i] == fields[j].fieldname
                if (i === fields[j].fieldname || `properties[${i}]` === fields[j].fieldname) {
                  isCustomizeField = false;
                }
              }

              if (isCustomizeField) {
                const newCatePropertyItem = {
                  displayname: i,
                  displayValue: properties[i],
                  fieldtype: 'addinput',
                  ItemIndex: i,
                };
                cateProPerty.push(newCatePropertyItem);
                AddPropertiesTitle[i] = i;
                AddPropertiesValue[i] = properties[i];
              }
            }
          }
          return {
            fields: cateProPerty,
            properties: reset ? {} : properties,
            AddPropertiesTitle,
            AddPropertiesValue,
          };
        });
      },
    });
  };

  renderCateProperty = properties => {
    const { fields, AddPropertiesValue, AddPropertiesTitle } = this.state;
    let cateProPertyList = [];
    console.log('fields', fields);
    cateProPertyList = fields.map(item => {
      if (item.fieldtype === 'input') {
        return (
          <Col span={24}>
            <Form.Item
              label={item.displayname}
              labelAlign="left"
              name={item.fieldname}
              rules={[
                {
                  required: item.required,
                  message: `请输入${item.displayname}`,
                  whitespace: true,
                },
              ]}
            >
              <Input
                placeholder={`请输入${item.displayname}`}
                defaultValue={properties && properties[item.fieldname]}
              />
            </Form.Item>
          </Col>
        );
      }
      if (item.fieldtype === 'addinput') {
        return (
          <Row style={{ marginTop: '10px' }}>
            <Col span={8}>
              <Input
                placeholder="请输入参数名"
                defaultValue={AddPropertiesTitle && AddPropertiesTitle[item.ItemIndex]}
                onChange={e => {
                  this.changeAddProPertyItemTitle(e, item);
                }}
              />
            </Col>
            <div style={{ lineHeight: '30px' }}>:</div>
            <Col span={8}>
              <Input
                placeholder="请输入参数值"
                defaultValue={AddPropertiesValue && AddPropertiesValue[item.ItemIndex]}
                onChange={e => {
                  this.changeAddProPertyItemValue(e, item);
                }}
              />
            </Col>
            <Col span={4}>
              <Button
                type="primary"
                style={{ marginLeft: '5px' }}
                size="Small"
                onClick={e => {
                  this.delProperItem(e, item);
                }}
              >
                删除
              </Button>
            </Col>
          </Row>
        );
      }

      if (item.fieldtype === 'textarea') {
        return (
          <Col span={24}>
            <Form.Item
              label={item.displayname}
              name={item.fieldname}
              labelAlign="left"
              rules={[
                {
                  required: item.required,
                  message: `请输入${item.displayname}`,
                  whitespace: true,
                },
              ]}
            >
              <Input.TextArea defaultValue={properties && properties[item.fieldname]} rows={6} />
            </Form.Item>
          </Col>
        );
      }

      if (item.fieldtype === 'checkbox') {
        return (
          <Col span={24}>
            <Form.Item label={item.displayname} name={item.fieldname} labelAlign="left">
              <Checkbox.Group defaultValue={properties && properties[item.fieldname]}>
                {item.fieldoptions.map(checkItem => (
                  <Checkbox value={checkItem.value ? checkItem.value : checkItem}>
                    {checkItem.label ? checkItem.label : checkItem}
                  </Checkbox>
                ))}
              </Checkbox.Group>
            </Form.Item>
          </Col>
        );
      }
      if (item.fieldtype === 'radio') {
        return (
          <Col span={24}>
            <Form.Item
              label={item.displayname}
              name={item.fieldname}
              labelAlign="left"
              rules={[
                {
                  required: item.required,
                  message: `请选择${item.displayname}`,
                  whitespace: true,
                },
              ]}
            >
              <Radio.Group defaultValue={properties && properties[item.fieldname]}>
                {item.fieldoptions.map(checkItem => (
                  <Radio value={checkItem.value ? checkItem.value : checkItem}>
                    {checkItem.label ? checkItem.label : checkItem}
                  </Radio>
                ))}
              </Radio.Group>
            </Form.Item>
          </Col>
        );
      }
      if (item.fieldtype === 'select') {
        return (
          <Col span={24}>
            <Form.Item
              label={item.displayname}
              labelAlign="left"
              name={item.fieldname}
              rules={[
                {
                  required: item.required,
                  message: `请选择${item.displayname}`,
                  whitespace: true,
                },
              ]}
            >
              <Select
                defaultValue={properties && properties[item.fieldname]}
                style={{ width: 200 }}
                dropdownStyle={{ zIndex: 5000 }}
                allowClear
              >
                {item.fieldoptions.map(checkItem =>
                  checkItem.label ? (
                    <Select.Option value={checkItem.value}>{checkItem.label}</Select.Option>
                  ) : (
                    <Select.Option value={checkItem}>{checkItem}</Select.Option>
                  ),
                )}
              </Select>
            </Form.Item>
          </Col>
        );
      }
    });
    return cateProPertyList;
  };

  render() {
    const { product } = this.state;
    const { cityOptions, cateOptions } = this.props;
    const CreateProPerty = () => {
      const { properties = {} } = this.state;
      const cateProPertyList = this.renderCateProperty(properties);
      return (
        <div>
          <Form initialValues={properties} ref={this.dynFormRef} model={properties}>
            {cateProPertyList}
          </Form>
          <Row></Row>
        </div>
      );
    };
    return (
      <div style={{ zIndex: 20, position: 'relative' }}>
        <div style={{ background: '#fff', padding: 24 }}>
          <div className={styles.mask} />
          <div className={styles.modalDlg}>
            <div className={styles.alltitle}>
              <span>产品详情</span>
            </div>
            <CloseCircleOutlined className={styles.closeIcon} onClick={this.props.close} />
            <div className={styles.commoncont}>
              <Space>
                <Row>
                  <Col span={4} className={styles.space}>
                    产品名称：
                  </Col>
                  <Col span={20} className={styles.space}>
                    <div className={styles.productname}>{product.name}</div>
                  </Col>

                  <Col span={4} className={styles.space}>
                    信息标题：
                  </Col>
                  <Col span={20} className={styles.space}>
                    <div className={styles.producttitle}>
                      <Row>
                        <Col className={styles.gutterbox} span={8}>
                          <span>{product.option_title.join(',')}</span>
                        </Col>
                      </Row>
                    </div>
                    <div className={styles.tip}>
                      当前共<span>{product.option_title.length}</span>
                      个标题，最少添加200个，最多添加2000个。
                      <TitleGroup cache={product.cache}></TitleGroup>
                    </div>
                  </Col>

                  <Col span={4} className={styles.space}>
                    详情模板：
                  </Col>
                  <Col span={20} className={styles.space}>
                    <div className={styles.deailtemplate}>{product.temp_content}</div>
                    <div>
                      <DeailGroup vars={product.vars}></DeailGroup>
                    </div>
                  </Col>

                  <Col span={4} className={styles.space}>
                    标题图片：
                  </Col>
                  <Col span={20} className={styles.space}>
                    <div className={styles.titlepicture}>
                      <CustomUploadDisplaypic images={product.titlepic} />
                    </div>
                  </Col>

                  <Col span={4} className={styles.space}>
                    视频：
                  </Col>
                  <Col span={20} className={styles.space}>
                    <div className={styles.videobox}>
                      <CustomUploadDisplayvideo videos={product.videos} />
                    </div>
                  </Col>

                  <Col span={4} className={styles.space}>
                    单价：
                  </Col>
                  <Col span={20} className={styles.space}>
                    <div className={styles.price}>{product.price}</div>
                  </Col>

                  <Col span={4} className={styles.space}>
                    单位：
                  </Col>
                  <Col span={20} className={styles.space}>
                    <div className={styles.unit}>{product.unit}</div>
                  </Col>

                  <Col span={4} className={styles.space}>
                    品牌：
                  </Col>
                  <Col span={20} className={styles.space}>
                    <div className={styles.brand}>{product.brand}</div>
                  </Col>

                  <Col span={4} className={styles.space}>
                    面向地区：
                  </Col>
                  <Col span={20} className={styles.space}>
                    <span>{product.area_names.join('>')}</span>
                  </Col>

                  <Col span={4} className={styles.space}>
                    关键词：
                  </Col>
                  <Col span={20} className={styles.space}>
                    <div className={styles.keyword}>
                      <span>{product.word.join(',')}</span>
                    </div>
                  </Col>

                  <Col span={4} className={styles.space}>
                    分类：
                  </Col>
                  <Col span={20} className={styles.space}>
                    <span>{product.cate_names.join('>')}</span>
                  </Col>

                  <Col span={4} className={styles.space}>
                    产品属性参数：
                  </Col>
                  <Col span={20} className={styles.space}>
                    <Row className={styles.canshu}>
                      <CreateProPerty />
                    </Row>
                  </Col>
                </Row>
              </Space>
            </div>
          </div>
        </div>
      </div>
    );
  }
}

export default connect(({ home, merchant }) => ({
  ...home,
  ...merchant,
}))(ProductDetail);
