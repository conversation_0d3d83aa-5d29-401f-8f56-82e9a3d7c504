import React from 'react';
import { Row, Steps, Col, Form, Input, Button, Space, message, Select, Modal } from 'antd';
import router from 'umi/router';
import { connect } from 'dva';
import styles from './Custom.less';
import VarGroup from '../../../../components/VarGroup';
import calculateHash from '@/utils/hash';
import SourceDig from '@/components/SourceDig';
import AiGeneration from '@/components/AiGeneration';
import { keyMap } from '@/utils/constant';
import SourceVarGroup from '@/components/SourceVarGroup';

const { TextArea } = Input;
const handleChange = value => {
  console.log(`selected ${value}`);
};

// 添加缓存key生成方法
const getCacheKey = productId => {
  return `custom_step2_cache_${productId || 'new'}`;
};

class CustomStep2 extends React.PureComponent {
  formRef = React.createRef();

  textareaRef = React.createRef();

  state = {
    // eslint-disable-next-line react/no-unused-state
    product: {
      temp_content: '',
      vars: {
        图片组: [],
        素材内容组1: [],
        素材内容组2: [],
        素材内容组3: [],
        素材内容组4: [],
      },
    },
    used: {
      素材内容组1: false,
      素材内容组2: false,
      素材内容组3: false,
      素材内容组4: false,
    },
    preProduct: {
      temp_content: '',
      vars: {
        图片组: [],
        素材内容组1: [],
        素材内容组2: [],
        素材内容组3: [],
        素材内容组4: [],
      },
      status: 0,
    },
    id: 0,
    hashStr: '',
    currentStep: 1,
    submiting: false,
    auditRes2: [],
    fullAuditRes2: [],
  };

  componentDidMount() {
    console.log('CustomStep2 loaded');
    let { id } = this.props.match.params;
    if (!id) {
      router.push('/publish/products');
      return;
    }
    id = parseInt(id, 10);

    // 检查缓存
    const cache = localStorage.getItem(getCacheKey(id));
    if (cache) {
      Modal.confirm({
        title: '发现未保存的编辑内容',
        content: '是否恢复上次未保存的编辑内容？',
        okText: '恢复',
        cancelText: '不需要',
        onOk: () => {
          const cachedData = JSON.parse(cache);
          this.setState(
            {
              product: cachedData.product,
              id,
            },
            () => {
              this.formRef.current.setFieldsValue({
                temp_content: cachedData.product.temp_content,
              });
            },
          );
        },
        onCancel: () => {
          this.clearCache(id);
          this.setState({ id });
          this.loadProduct(id);
        },
      });
    } else {
      this.setState({ id });
      this.loadProduct(id);
    }
  }

  loadProduct = id => {
    this.props
      .dispatch({
        type: 'product/getProductById',
        payload: { product_id: id },
      })
      .then(result => {
        // 检查产品素材状态，如果素材已用完则显示提示弹窗
        if (result.status === 7) {
          Modal.warning({
            title: '提示',
            content:
              '该产品素材已用完，请重新添加新素材，否则直接影响推广效果，屡次不改会禁止更新本产品。',
            okText: '我知道了',
            zIndex: 9999,
          });
        }

        this.setState(
          ({ product, used }) => {
            const auditRes2 = {};
            for (const key in result.audit_res2) {
              if (Object.prototype.hasOwnProperty.call(product, key)) {
                auditRes2[key] = result.audit_res2[key];
              }
            }
            let vars = product.vars;

            if (result.vars) {
              vars = { ...product.vars, ...result.vars };
            }
            const updatedProduct = {
              ...product,
              temp_content: result.temp_content,
              cache: result.cache,
              vars,
            };
            for (const key in used) {
              if (result.temp_content.includes(`【${key}】`)) {
                used[key] = true;
              }
            }

            const hashStr = calculateHash(updatedProduct);
            return {
              product: updatedProduct,
              preProduct: {
                ...updatedProduct,
                status: typeof result.status === 'number' ? result.status : 0,
              },
              hashStr,
              auditRes2,
              fullAuditRes2: result.audit_res2,
            };
          },
          () => {
            // 更新表单
            this.formRef.current.setFieldsValue({
              temp_content: this.state.product.temp_content,
            });
          },
        );
      })
      .catch(err => {
        console.log(err);
      });
  };

  onStepChange = (current, step) => {
    if (current < this.state.currentStep) {
      router.push(`/publish/products/customstep${current + 1}/${this.state.id}`);
    }
  };

  handleFailed = err => {
    message.error(err.errorFields[0].errors[0]);
  };

  insertPlaceholder = text => {
    const { product } = this.state;
    // 去掉text 前后的【】
    const key = text.substring(1, text.length - 1);
    const textarea = this.textareaRef.current.resizableTextArea.textArea;
    console.log(textarea);
    const startPos = textarea.selectionStart;
    const endPos = textarea.selectionEnd;
    if (!startPos || !endPos) {
      product.temp_content += text;
    } else {
      const textBefore = textarea.value.substring(0, startPos);
      const textAfter = textarea.value.substring(endPos, textarea.value.length);

      product.temp_content = textBefore + text + textAfter;
      textarea.selectionStart = startPos + text.length;
      textarea.selectionEnd = startPos + text.length;
    }

    // 使用不可变方法更新状态
    this.setState(prevState => ({
      product: { ...prevState.product }, // 如果 product 需要更新
      used: { ...prevState.used, [key]: true },
    }));
    this.formRef.current.setFieldsValue({
      temp_content: product.temp_content,
    });
    this.saveToCache();
  };

  // 添加保存缓存方法
  saveToCache = () => {
    const cacheData = {
      product: this.state.product,
    };
    localStorage.setItem(getCacheKey(this.state.id), JSON.stringify(cacheData));
  };

  // 修改updateContent方法,添加缓存
  updateContent = item => {
    this.setState(
      ({ product }) => {
        const updatedProduct = {
          ...product,
          temp_content: item,
        };
        return {
          product: updatedProduct,
        };
      },
      () => {
        this.formRef.current.setFieldsValue({
          temp_content: this.state.product.temp_content,
        });
        this.saveToCache();
      },
    );
  };

  clearCache = id => {
    localStorage.removeItem(getCacheKey(id || this.state.id));
  };

  onFinish = values => {
    const { vars } = this.state.product;

    const { id: productId, auditRes2, product, preProduct, fullAuditRes2 } = this.state;
    let newAuditRes2 = { ...fullAuditRes2 };
    for (const key in auditRes2) {
      const element = values[key];
      if (JSON.stringify(element) === JSON.stringify(preProduct[key])) {
        message.error(`请修改${keyMap[key]}的值: ${auditRes2[key]}`);
        return;
      }
      delete newAuditRes2[key];
    }

    // 判断素材是否已更新
    const originalContent = preProduct.temp_content;
    const currentContent = values.temp_content;

    if (preProduct.status === 7 && originalContent === currentContent && productId !== 0) {
      Modal.warning({
        title: '提示',
        content:
          '该产品素材已用完，请重新添加新素材，否则直接影响推广效果，屡次不改会禁止更新本产品。',
        okText: '我知道了',
        zIndex: 9999,
      });
      return;
    }

    this.state.product.temp_content = values.temp_content;
    const newHash = calculateHash(this.state.product);

    const successCallback = () => {
      this.clearCache();
      router.push(`/publish/products/customstep3/${this.state.id}`);
    };

    if (newHash !== this.state.hashStr) {
      this.setState({ submiting: true });
      this.props
        .dispatch({
          type: 'product/updateProductById',
          payload: {
            product_id: productId,
            temp_content: values.temp_content,
            vars,
            audit_res2: newAuditRes2,
          },
        })
        .then(result => {
          this.setState({ submiting: false });
          successCallback();
        })
        .catch(err => {
          this.setState({ submiting: false });
          message.error(err.msg || err.message);
        });
    } else {
      successCallback();
    }
  };

  render() {
    const { product, submiting, auditRes2, used } = this.state;

    const others = { ...product.vars }; // 复制一个对象
    delete others['图片组'];
    for (const key in used) {
      if (used[key]) {
        delete others[key];
      }
    }
    console.log('others:', others);

    return (
      <div className={styles.custombox}>
        <div className={styles.alltitle}>
          <span>自定义模板</span>
        </div>
        <div className={styles.customwrap}>
          {Object.keys(auditRes2).length > 0 && (
            <div className={styles.to_examine}>
              <p>审核未通过原因：</p>
              {Object.keys(auditRes2).map(item => (
                <p key={item}>
                  {keyMap[item]}：{auditRes2[item]}。
                </p>
              ))}
            </div>
          )}
          <div className={styles.stepbox}>
            <Steps
              current={this.state.currentStep}
              onChange={this.onStepChange}
              items={[
                {
                  title: '添加标题',
                },
                {
                  title: '添加素材',
                },
                {
                  title: '图片视频',
                },
                {
                  title: '属性分类',
                },
              ]}
            />
          </div>
          <Form
            ref={this.formRef}
            onFinish={this.onFinish}
            onFinishFailed={this.handleFailed}
            initialValues={{
              temp_content: product.temp_content,
            }}
          >
            <Row>
              <Col span={24}>
                <Form.Item
                  label={<span>详情模板</span>}
                  labelCol={{ span: 2 }}
                  wrapperCol={{ span: 22 }}
                >
                  <Space>
                    <SourceDig
                      onSave={item => {
                        // 更新 state和表单 的 temp_content
                        this.updateContent(item);
                      }}
                    />
                    <AiGeneration
                      onSave={item => {
                        this.updateContent(item);
                      }}
                    />
                  </Space>
                </Form.Item>
              </Col>
              <Col span={2}></Col>
              <Col span={22}>
                <Form.Item
                  labelCol={{ span: 2 }}
                  wrapperCol={{ span: 22 }}
                  name="temp_content"
                  rules={[{ required: true, message: '请填写详情模板' }]}
                >
                  <TextArea
                    ref={this.textareaRef}
                    rows={10}
                    placeholder="请输入内容详情"
                    onChange={e => {
                      this.updateContent(e.target.value);
                    }}
                  />
                </Form.Item>
                <div className={styles.added_bottom}>
                  <Space>
                    <Button onClick={() => this.insertPlaceholder('【图片组】')}>插入图片组</Button>
                    {Object.keys(others).length > 0 && (
                      <Select
                        defaultValue={Object.keys(others)[0]}
                        style={{
                          width: 120,
                        }}
                        onSelect={value => this.insertPlaceholder(`【${value}】`)}
                      >
                        {Object.keys(others).map(item => (
                          <Select.Option key={item} value={item}>
                            {item}
                          </Select.Option>
                        ))}
                      </Select>
                    )}
                    <SourceVarGroup
                      product={this.state.product}
                      onVarsChange={vars => {
                        // eslint-disable-next-line react/no-access-state-in-setstate
                        this.setState({ product: { ...this.state.product, vars } });
                        this.saveToCache();
                      }}
                    />
                  </Space>
                </div>
              </Col>
            </Row>
            <div className={styles.addtitle_submit}>
              <Space>
                <Button type="primary" htmlType="submit" loading={submiting}>
                  保存并下一步
                </Button>
              </Space>
            </div>
          </Form>
        </div>
      </div>
    );
  }
}

export default connect(({ home }) => ({ ...home }))(CustomStep2);
