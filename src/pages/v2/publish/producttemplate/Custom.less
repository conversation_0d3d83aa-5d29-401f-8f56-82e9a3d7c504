.custombox {
  background: #fff;
}
.alltitle {
  height: 60px;
  padding-left: 20px;
  line-height: 60px;
  background: #fff;
  border-bottom: 1px solid #d7d7d7;
}
.alltitle > span {
  display: inline-block;
  padding: 0 5px;
  color: #333;
  font-weight: bold;
  font-size: 18px;
  line-height: 58px;
  border-bottom: 2px solid #1890ff;
}
.customwrap {
  margin: 0 auto;
  padding: 10px 20px;
}
.to_examine {
  padding: 10px 0;
  overflow: hidden;
  text-align: center;
}
.to_examine > p {
  display: inline-block;
  color: #f00;
  font-size: 12px;
}
.stepbox {
  padding: 30px 100px;
  text-align: center;
}

.customright {
  margin-bottom: 10px;
  padding: 10px 0;
  font-size: 13px;
  border: 1px solid #e6e6e6;
}

.title_head {
  box-sizing: content-box !important;
  height: 24px;
  padding: 10px 5px;
  color: #333;
  font-size: 14px;
  line-height: 24px;
  background: #ffebca;
  border-top: 1px solid #e6e6e6;
  border-right: 1px solid #e6e6e6;
  border-left: 1px solid #e6e6e6;
}
.last_title_head {
  padding-top: 10px;
  padding-bottom: 10px;
  font-size: 14px;
}
.title_head span {
  display: inline-block;
  height: 24px;
  margin-left: 5px;
  padding: 0 5px;
  color: #fff;
  font-size: 12px;
  line-height: 24px;
  background: #999;
  border-radius: 5px;
}

.rule {
  color: #999;
  font-size: 12px;
  line-height: 20px;
}

.generatetitle_butn {
  margin: 0 auto;
  padding: 5px;
}

.added_bottom {
  margin: 0 auto;
  padding: 10px 0;
}
.added_bottom > span {
  color: #666;
  font-size: 14px;
}
.added_bottom > span font {
  color: #f00;
}

.addtitle_submit {
  margin: 0 auto;
  padding: 10px 0;
  text-align: center;
}

.productdata_bg {
  padding: 5px 0;
  background: #f6f6f6;
  border: 1px solid #e6e6e6;
  border-radius: 5px;
}
.topbox {
  padding: 10px;
}
.tips {
  color: #999;
  font-size: 12px;
}
.chosebutn {
  padding: 10px;
}
