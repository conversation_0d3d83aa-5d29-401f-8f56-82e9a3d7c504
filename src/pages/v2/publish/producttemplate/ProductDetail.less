.Deailbox {
  background: #fff;
}
.space {
  padding: 5px 0px;
}
/* 遮罩层 */
.mask {
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  background: #000;
  z-index: 0;
  opacity: 0.5;
}
/* 弹出层 */
.modalDlg {
  width: 80%;
  height: 90%;
  position: fixed;
  top: 20px;
  left: 0;
  right: 0;
  z-index: 3;
  margin: 0 auto;
  background-color: #fff;
  border-radius: 5px;
  display: flex;
  flex-direction: column;
  padding: 10px;
  .closeIcon {
    position: fixed;
    top: 25px;
    right: 12%;
    font-size: 18px;
    color: #666666;
  }
}
.alltitle {
  height: 60px;
  padding-left: 20px;
  line-height: 60px;
  background: #fff;
  border-bottom: 1px solid #d7d7d7;
}
.alltitle > span {
  display: inline-block;
  padding: 0 5px;
  color: #333;
  font-weight: bold;
  font-size: 18px;
  line-height: 58px;
  border-bottom: 2px solid #1890ff;
}
.commoncont {
  padding: 20px;
  overflow: scroll;
}
.productname {
  height: 30px;
  border: 1px solid #e6e6e6;
  background: #f6f6f6;
  line-height: 30px;
}
.producttitle {
  border: 1px solid #e6e6e6;
  background: #f6f6f6;
  max-height: 210px;
  overflow: hidden;
  overflow-y: scroll;
}
.gutterbox {
  padding: 8px 0;
}
.titletext {
  height: 30px;
  border: 1px solid #e6e6e6;
  background: #fff;
  line-height: 30px;
  color: #333;
}
.tip {
  font-size: 14px;
}
.tip > span {
  color: #f00;
}
.deailtemplate {
  max-height: 120px;
  border: 1px solid #e6e6e6;
  background: #fff;
  line-height: 20px;
  color: #333;
  overflow: hidden;
  overflow-y: scroll;
}
.titlepicture {
  border: 1px solid #e6e6e6;
}
.picturelist {
  float: left;
  box-sizing: content-box;
  width: 140px;
  height: 150px;
  margin: 10px;
  padding: 5px;
  border: 1px solid #e6e6e6;
}
.imgname {
  width: 140px;
  overflow: hidden;
  height: 40px;
  line-height: 40px;
  color: #666;
}
.videolist {
  float: left;
  box-sizing: content-box;
  width: 140px;
  height: 150px;
  margin: 10px;
  padding: 5px;
  border: 1px solid #e6e6e6;
}
.videobox {
  border: 1px solid #e6e6e6;
  overflow: hidden;
  margin: 10px 0px;
  padding: 5px 0px;
}

.videoname {
  width: 140px;
  overflow: hidden;
  height: 40px;
  line-height: 40px;
  color: #666;
}
.price {
  height: 30px;
  border: 1px solid #e6e6e6;
  background: #f6f6f6;
  line-height: 30px;
}
.unit {
  height: 30px;
  border: 1px solid #e6e6e6;
  background: #f6f6f6;
  line-height: 30px;
}
.brand {
  height: 30px;
  border: 1px solid #e6e6e6;
  background: #f6f6f6;
  line-height: 30px;
}
.area {
  height: 30px;
  border: 1px solid #e6e6e6;
  background: #f6f6f6;
  line-height: 30px;
}
.keyword {
  max-height: 120px;
  border: 1px solid #e6e6e6;
  background: #fff;
  line-height: 20px;
  color: #333;
  overflow: hidden;
  overflow-y: scroll;
}
.keyword > span {
  padding: 3px 5px;
  background: #f6f6f6;
  color: #666;
  margin: 5px;
  float: left;
}
.alias {
  max-height: 120px;
  border: 1px solid #e6e6e6;
  background: #fff;
  line-height: 20px;
  color: #333;
  overflow: hidden;
  overflow-y: scroll;
}
.alias > span {
  padding: 3px 5px;
  background: #f6f6f6;
  color: #666;
  margin: 5px;
  float: left;
}
.classify {
  height: 30px;
  border: 1px solid #e6e6e6;
  background: #f6f6f6;
  line-height: 30px;
}
.canshu {
  border: 1px solid #e6e6e6;
  background: #f6f6f6;
  padding: 10px;
}
.video-js .vjs-big-play-button {
  width: 2em !important;
  top: 30px !important;
  left: 40px !important;
}
