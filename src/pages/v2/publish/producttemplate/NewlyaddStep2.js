import React from 'react';
import { Steps, Button, Space, Form, message, Modal, Upload } from 'antd';
import { Table, Radio, Typography } from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import styles from './Custom.less';
import SourceTable from '@/components/SourceTable';
import router from 'umi/router';
import AddSource from '@/components/AddSource';
import SourceDig from '@/components/SourceDig';
import AiGeneration from '@/components/AiGeneration';
import { connect } from 'dva';
import { keyMap } from '@/utils/constant';
import calculateHash from '@/utils/hash';

const { Text } = Typography;

const getCacheKey = productId => {
  return `newlyadd_step2_cache_${productId || 'new'}`;
};

class NewlyaddStep2 extends React.PureComponent {
  formRef = React.createRef();

  state = {
    // eslint-disable-next-line react/no-unused-state
    product: {
      material: [], // 素材内容,未编辑的
    },
    preProduct: {
      material: [], // 原始素材内容
      status: 0,
    },
    material: [], // 素材内容,编辑中的
    submiting: false, // 提交中
    currentStep: 1,
    auditRes2: [],
    selectedRowKeys: [],
    fullAuditRes2: [],
    batchImportVisible: false, // 批量导入弹窗显示状态
  };

  clearCache = id => {
    localStorage.removeItem(getCacheKey(id || this.state.id));
  };

  componentDidMount() {
    let { id } = this.props.match.params;
    if (!id) {
      router.push('/publish/products');
      return;
    }
    id = parseInt(id, 10);

    const cache = localStorage.getItem(getCacheKey(id));
    if (cache) {
      Modal.confirm({
        title: '发现未保存的编辑内容',
        content: '是否恢复上次未保存的编辑内容？',
        okText: '恢复',
        cancelText: '不需要',
        onOk: () => {
          const cachedData = JSON.parse(cache);
          this.setState(
            {
              product: cachedData.product,
              material: cachedData.material,
              id,
            },
            () => {
              this.formRef.current.setFieldsValue({
                material: cachedData.material,
              });
            },
          );
        },
        onCancel: () => {
          this.clearCache(id);
          this.loadProduct(id);
        },
      });
    } else {
      this.setState({ id });
      this.loadProduct(id);
    }
  }

  loadProduct = id => {
    this.props
      .dispatch({
        type: 'product/getProductById',
        payload: { product_id: id },
      })
      .then(result => {
        // 检查产品素材状态，如果素材已用完则显示提示弹窗
        if (result.status === 7) {
          Modal.warning({
            title: '提示',
            content:
              '该产品素材已用完，请重新添加新素材，否则直接影响推广效果，屡次不改会禁止更新本产品。',
            okText: '我知道了',
            zIndex: 9999,
          });
        }

        this.setState(
          ({ product }) => {
            // 处理result.audit_res2 只保留product也存在的属性
            const auditRes2 = {};
            for (const key in result.audit_res2) {
              if (Object.prototype.hasOwnProperty.call(product, key)) {
                auditRes2[key] = result.audit_res2[key];
              }
            }
            const updatedProduct = {
              ...product,
              material: result.material || [],
            };

            const hashStr = calculateHash(updatedProduct);

            return {
              id,
              product: updatedProduct,
              preProduct: {
                material: result.material || [],
                status: typeof result.status === 'number' ? result.status : 0,
              },
              material: [...updatedProduct.material],
              hashStr,
              auditRes2,
              fullAuditRes2: result.audit_res2,
            };
          },
          () => {
            // update form field
            this.formRef.current.setFieldsValue({ material: this.state.material });
          },
        );
      })
      .catch(err => {
        console.log(err);
      });
  };

  onStepChange = (current, step) => {
    if (current < this.state.currentStep) {
      router.push(`/publish/products/add${current + 1}/${this.state.id}`);
    }
  };

  goNext = () => {
    router.push('/publish/products/add3');
  };

  // 保存素材内容
  onContentChange = (idx, content) => {
    this.checkSimilarity(content, idx);
    this.saveToCache();
  };

  onContentsAdded = contents => {
    this.checkSimilarity(contents, -1);
  };

  selectAllRows = () => {
    const selectedRowKeys = this.state.material.map((item, idx) => idx);
    this.setState({ selectedRowKeys });
  };

  reverSelectRows = () => {
    // reserve the select
    const { selectedRowKeys } = this.state;
    const { material } = this.state;
    const newSelectedRowKeys = material
      .map((item, idx) => idx)
      .filter(item => !selectedRowKeys.includes(item));
    this.setState({ selectedRowKeys: newSelectedRowKeys });
  };

  // 删除选中的素材
  deleteSelectedRows = () => {
    const { selectedRowKeys } = this.state;
    this.setState(
      prevState => {
        const { material } = prevState;
        const newMaterial = material.filter((item, idx) => !selectedRowKeys.includes(idx));
        return { material: newMaterial, selectedRowKeys: [] };
      },
      () => {
        this.formRef.current.setFieldsValue({ material: this.state.material });
      },
    );
  };

  checkSimilarity = (item, index) => {
    let list = this.state.material;
    if (index < 0) {
      list = list.concat(item);
      index = list.length - 1;
    } else {
      if (list[index] === item) {
        return;
      }
      list[index] = item;
    }
    this.props.dispatch({
      type: 'product/similar',
      payload: { data: list, index },
      callBack: ({ msg, code }) => {
        if (code === 'input_param_error') {
          Modal.error({
            content: msg,
            zIndex: 9999,
          });
          console.log('set error');
        } else {
          console.log('set success:', list);
          this.setState(
            ({}) => {
              return { material: [...list] };
            },
            () => {
              this.formRef.current.setFieldsValue({ material: list });
              this.saveToCache();
            },
          );
        }
      },
    });
  };

  onFinish = values => {
    const { id: productId, auditRes2, product, preProduct, fullAuditRes2 } = this.state;
    let newAuditRes2 = { ...fullAuditRes2 };
    for (const key in auditRes2) {
      const element = values[key];
      if (JSON.stringify(element) === JSON.stringify(product[key])) {
        debugger;
        message.error(`请修改${keyMap[key]}的值: ${auditRes2[key]}`);
        return;
      }
      delete newAuditRes2[key];
    }

    // 判断素材是否已更新
    // 使用数组的副本进行排序比较，不修改原始数组
    const originalMaterial = JSON.stringify([...preProduct.material].sort());
    const currentMaterial = JSON.stringify([...values.material].sort());

    if (preProduct.status === 7 && originalMaterial === currentMaterial && productId !== 0) {
      Modal.warning({
        title: '提示',
        content:
          '该产品素材已用完，请重新添加新素材，否则直接影响推广效果，屡次不改会禁止更新本产品。',
        okText: '我知道了',
        zIndex: 9999,
      });
      return;
    }

    this.state.product.material = this.state.material;
    const newHash = calculateHash(this.state.product);
    if (newHash !== this.state.hashStr) {
      this.setState({ submiting: true });
      // 有变化， 调用更新接口
      this.props
        .dispatch({
          type: 'product/updateProductById',
          payload: {
            product_id: productId,
            material: values.material,
            audit_res2: newAuditRes2,
          },
        })
        .then(result => {
          this.setState({ submiting: false });
          this.clearCache();
          router.push(`/publish/products/add3/${productId}`);
        })
        .catch(err => {
          this.setState({ submiting: false });
          message.error(err.msg || err.message);
        });
    } else {
      this.setState({ submiting: false });
      this.clearCache();
      router.push(`/publish/products/add3/${productId}`);
    }
  };

  handleFailed = err => {
    message.error(err.errorFields[0].errors[0]);
  };

  saveToCache = () => {
    const cacheData = {
      product: this.state.product,
      material: this.state.material,
    };
    localStorage.setItem(getCacheKey(this.state.id), JSON.stringify(cacheData));
  };

  // 显示批量导入弹窗
  showBatchImport = () => {
    this.setState({ batchImportVisible: true });
  };

  // 隐藏批量导入弹窗
  hideBatchImport = () => {
    this.setState({ batchImportVisible: false });
  };

  // 处理批量导入文件
  handleBatchImport = file => {
    const reader = new FileReader();
    reader.onload = e => {
      try {
        const content = e.target.result;
        this.processBatchContent(content);
      } catch (error) {
        message.error('文件读取失败，请检查文件格式');
      }
    };
    reader.readAsText(file, 'UTF-8');
    return false; // 阻止默认上传行为
  };

  // 处理批量导入的内容
  processBatchContent = content => {
    // 按空白行分割内容
    const materials = content
      .split(/\n\s*\n/) // 按空白行分割
      .map(item => item.trim()) // 去除首尾空白
      .filter(item => item.length > 0); // 过滤空内容

    let validMaterials = [];
    let discardedCount = 0;
    const currentMaterials = [...this.state.material];

    // 过滤符合条件的素材
    for (const material of materials) {
      // 检查字数限制
      if (material.length < 50 || material.length > 5000) {
        discardedCount++;
        continue;
      }

      // 检查总数限制
      if (currentMaterials.length + validMaterials.length >= 100) {
        discardedCount += materials.length - materials.indexOf(material);
        break;
      }

      validMaterials.push(material);
    }

    if (validMaterials.length > 0) {
      // 使用现有的checkSimilarity函数来处理重复检查和添加
      this.setState({ batchImportVisible: false });

      // 批量添加素材，利用现有的checkSimilarity函数
      this.checkSimilarity(validMaterials, -1);

      // 显示导入结果
      setTimeout(() => {
        const finalCount = this.state.material.length - currentMaterials.length;
        const actualDiscarded = validMaterials.length - finalCount + discardedCount;

        if (actualDiscarded > 0) {
          message.success(
            `导入成功，部分素材不符合要求，已被舍弃。成功导入${finalCount}篇，舍弃${actualDiscarded}篇。`,
          );
        } else {
          message.success(`导入成功，共导入${finalCount}篇素材。`);
        }
      }, 100);
    } else {
      message.warning('没有符合要求的素材可以导入');
      this.setState({ batchImportVisible: false });
    }
  };

  render() {
    const { material, currentStep, auditRes2 } = this.state;
    console.log('auditRes2', auditRes2);
    const columns = [
      {
        title: '序号',
        dataIndex: 'number',
        key: 'number',
      },
      {
        title: '素材内容',
        dataIndex: 'content',
        key: 'content',
      },
      {
        title: '操作',
        dataIndex: 'operate',
        key: 'operate',
        width: 200,
        render: (text, record) => {
          return (
            <span>
              <AddSource
                key={`${record.key}-${record.content}`}
                idx={record.key}
                content={record.content}
                onSave={this.onContentChange}
              />
            </span>
          );
        },
      },
    ];
    const data = material.map((item, idx) => {
      return {
        key: idx,
        number: idx + 1,
        content: item,
      };
    });
    const rowSelection = {
      selectedRowKeys: this.state.selectedRowKeys,
      onChange: (selectedRowKeys, selectedRows) => {
        console.log(`selectedRowKeys: ${selectedRowKeys}`, 'selectedRows: ', selectedRows);
        this.setState({ selectedRowKeys });
      },
      getCheckboxProps: record => ({
        disabled: record.name === 'Disabled User', // Column configuration not to be checked
        name: record.name,
      }),
      onSelectAll: (selected, selectedRows, changeRows) => {
        if (selected) {
          // 全选操作
          this.setState({ selectedRowKeys: selectedRows.map((item, idx) => idx) });
        } else {
          // 取消全选操作
          this.setState({ selectedRowKeys: [] });
        }
      },
    };

    return (
      <div className={styles.custombox}>
        <div className={styles.alltitle}>
          <span>新增产品</span>
        </div>
        <div className={styles.customwrap}>
          {Object.keys(auditRes2).length > 0 && (
            <div className={styles.to_examine}>
              <p>审核未通过原因：</p>
              {Object.keys(auditRes2).map(item => (
                <p key={item}>
                  {keyMap[item]}：{auditRes2[item]}。
                </p>
              ))}
            </div>
          )}
          <div style={{ padding: '0 10px' }}>
            <div className={styles.stepbox}>
              <Steps
                current={currentStep}
                onChange={this.onStepChange}
                items={[
                  {
                    title: '添加标题',
                  },
                  {
                    title: '添加素材',
                  },
                  {
                    title: '图片视频',
                  },
                  {
                    title: '属性分类',
                  },
                ]}
              />
            </div>
            <div className={styles.topbox}>
              <AddSource idx={-1} content={''} onSave={this.onContentChange} />{' '}
              <SourceDig single={false} onSave={this.onContentsAdded} />
              {/* 挖掘素材内容组件 */}
              <AiGeneration
                onSave={item => {
                  this.onContentsAdded([item]);
                }}
              />
              {/* 智能生成素材内容组件 */}
              <Button type="primary" icon={<UploadOutlined />} onClick={this.showBatchImport}>
                批量导入
              </Button>
              {/* 链入添加素材页面组件 */}
            </div>
            <div className={styles.tips}>
              素材内容在50-5000之间，上传15-100篇，素材内容为比较通用的产品定义、描述、外观、组成、特性、结构、范围、注意事项等，需要保证语句完整且通顺。
            </div>
            <Table
              rowSelection={rowSelection}
              pagination={false}
              bordered
              columns={columns}
              dataSource={data}
            />
            {/* 显示素材列表组件 */}
            <div className={styles.chosebutn}>
              <Space>
                <Button onClick={this.selectAllRows}>全选</Button>
                <Button onClick={this.reverSelectRows}>反选</Button>
                <Button onClick={this.deleteSelectedRows}>删除</Button>
              </Space>
            </div>
          </div>
          <Form
            ref={this.formRef}
            onFinish={this.onFinish}
            onFinishFailed={this.handleFailed}
            initialValues={{
              material: material,
            }}
          >
            <Form.Item
              name="material"
              rules={[
                { required: true, message: '请编辑素材' },
                { type: 'array', min: 15, max: 100, message: '素材个数在15~100个' },
              ]}
            ></Form.Item>
            <Form.Item>
              <div className={styles.addtitle_submit}>
                <Space>
                  <Button type="primary" htmlType="submit" loading={this.state.submiting}>
                    保存进入下一步
                  </Button>
                </Space>
              </div>
            </Form.Item>
          </Form>
        </div>

        {/* 批量导入弹窗 */}
        <Modal
          title="批量导入素材"
          open={this.state.batchImportVisible}
          onCancel={this.hideBatchImport}
          footer={null}
          width={600}
        >
          <div style={{ marginBottom: 16 }}>
            <Text type="secondary">
              说明：批量导入素材仅支持txt格式，系统根据素材里的空白行将上传素材自动分成多篇素材，每篇素材字数在50-5000字之间，字数不符合要求的素材将会舍弃。最多添加100篇，超出的素材也将会舍弃。
            </Text>
          </div>
          <Upload
            accept=".txt"
            beforeUpload={this.handleBatchImport}
            showUploadList={false}
            style={{ width: '100%' }}
          >
            <Button
              type="primary"
              icon={<UploadOutlined />}
              size="large"
              style={{ width: '100%', height: 60 }}
            >
              选择TXT文件上传
            </Button>
          </Upload>
          <div style={{ marginTop: 16, textAlign: 'center' }}>
            <Button onClick={this.hideBatchImport}>取消</Button>
          </div>
        </Modal>
      </div>
    );
  }
}

export default connect(({ home }) => ({ ...home }))(NewlyaddStep2);
