import React from 'react';
import { Steps, Button, Space, Form, message, Modal, Upload } from 'antd';
import { Table, Radio, Typography } from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import styles from './Custom.less';
import SourceTable from '@/components/SourceTable';
import router from 'umi/router';
import AddSource from '@/components/AddSource';
import SourceDig from '@/components/SourceDig';
import AiGeneration from '@/components/AiGeneration';
import { connect } from 'dva';
import { keyMap } from '@/utils/constant';
import calculateHash from '@/utils/hash';

const { Text } = Typography;

const getCacheKey = productId => {
  return `newlyadd_step2_cache_${productId || 'new'}`;
};

class NewlyaddStep2 extends React.PureComponent {
  formRef = React.createRef();

  state = {
    // eslint-disable-next-line react/no-unused-state
    product: {
      material: [], // 素材内容,未编辑的
    },
    preProduct: {
      material: [], // 原始素材内容
      status: 0,
    },
    material: [], // 素材内容,编辑中的
    submiting: false, // 提交中
    currentStep: 1,
    auditRes2: [],
    selectedRowKeys: [],
    fullAuditRes2: [],
    batchImportVisible: false, // 批量导入弹窗显示状态
    selectedFile: null, // 选中的文件
  };

  clearCache = id => {
    localStorage.removeItem(getCacheKey(id || this.state.id));
  };

  componentDidMount() {
    let { id } = this.props.match.params;
    if (!id) {
      router.push('/publish/products');
      return;
    }
    id = parseInt(id, 10);

    const cache = localStorage.getItem(getCacheKey(id));
    if (cache) {
      Modal.confirm({
        title: '发现未保存的编辑内容',
        content: '是否恢复上次未保存的编辑内容？',
        okText: '恢复',
        cancelText: '不需要',
        onOk: () => {
          const cachedData = JSON.parse(cache);
          this.setState(
            {
              product: cachedData.product,
              material: cachedData.material,
              id,
            },
            () => {
              this.formRef.current.setFieldsValue({
                material: cachedData.material,
              });
            },
          );
        },
        onCancel: () => {
          this.clearCache(id);
          this.loadProduct(id);
        },
      });
    } else {
      this.setState({ id });
      this.loadProduct(id);
    }
  }

  loadProduct = id => {
    this.props
      .dispatch({
        type: 'product/getProductById',
        payload: { product_id: id },
      })
      .then(result => {
        // 检查产品素材状态，如果素材已用完则显示提示弹窗
        if (result.status === 7) {
          Modal.warning({
            title: '提示',
            content:
              '该产品素材已用完，请重新添加新素材，否则直接影响推广效果，屡次不改会禁止更新本产品。',
            okText: '我知道了',
            zIndex: 9999,
          });
        }

        this.setState(
          ({ product }) => {
            // 处理result.audit_res2 只保留product也存在的属性
            const auditRes2 = {};
            for (const key in result.audit_res2) {
              if (Object.prototype.hasOwnProperty.call(product, key)) {
                auditRes2[key] = result.audit_res2[key];
              }
            }
            const updatedProduct = {
              ...product,
              material: result.material || [],
            };

            const hashStr = calculateHash(updatedProduct);

            return {
              id,
              product: updatedProduct,
              preProduct: {
                material: result.material || [],
                status: typeof result.status === 'number' ? result.status : 0,
              },
              material: [...updatedProduct.material],
              hashStr,
              auditRes2,
              fullAuditRes2: result.audit_res2,
            };
          },
          () => {
            // update form field
            this.formRef.current.setFieldsValue({ material: this.state.material });
          },
        );
      })
      .catch(err => {
        console.log(err);
      });
  };

  onStepChange = (current, step) => {
    if (current < this.state.currentStep) {
      router.push(`/publish/products/add${current + 1}/${this.state.id}`);
    }
  };

  goNext = () => {
    router.push('/publish/products/add3');
  };

  // 保存素材内容
  onContentChange = async (idx, content) => {
    try {
      await this.checkSimilarity(content, idx);
      this.saveToCache();
    } catch (error) {
      Modal.error({
        title: '提示',
        content: error.msg || error.message,
        zIndex: 9999,
      });
    }
  };

  onContentsAdded = async contents => {
    try {
      await this.checkSimilarity(contents, -1);
    } catch (error) {
      Modal.error({
        title: '提示',
        content: error.msg || error.message,
        zIndex: 9999,
      });
    }
  };

  selectAllRows = () => {
    const selectedRowKeys = this.state.material.map((item, idx) => idx);
    this.setState({ selectedRowKeys });
  };

  reverSelectRows = () => {
    // reserve the select
    const { selectedRowKeys } = this.state;
    const { material } = this.state;
    const newSelectedRowKeys = material
      .map((item, idx) => idx)
      .filter(item => !selectedRowKeys.includes(item));
    this.setState({ selectedRowKeys: newSelectedRowKeys });
  };

  // 删除选中的素材
  deleteSelectedRows = () => {
    const { selectedRowKeys } = this.state;
    this.setState(
      prevState => {
        const { material } = prevState;
        const newMaterial = material.filter((item, idx) => !selectedRowKeys.includes(idx));
        return { material: newMaterial, selectedRowKeys: [] };
      },
      () => {
        this.formRef.current.setFieldsValue({ material: this.state.material });
      },
    );
  };

  checkSimilarity = async (item, index) => {
    // 保存当前状态，以便在失败时恢复
    const originalMaterial = [...this.state.material];
    let list = [...this.state.material];

    if (index < 0) {
      list = list.concat(item);
      index = list.length - 1;
    } else {
      if (list[index] === item) {
        return { success: true, list: originalMaterial };
      }
      list[index] = item;
    }

    // 使用 dispatch 返回 Promise，让调用者处理错误
    const result = await this.props.dispatch({
      type: 'product/similar',
      payload: { data: list, index },
    });

    if (result.code === 'input_param_error') {
      console.log('checkSimilarity error:', result.msg);
      // 失败时不更新状态，保持原始状态
      return { success: false, msg: result.msg, list: originalMaterial };
    } else {
      console.log('checkSimilarity success:', list.length, 'items');
      // 成功时才更新状态
      this.setState({ material: [...list] }, () => {
        this.formRef.current.setFieldsValue({ material: list });
        this.saveToCache();
      });
      return { success: true, list };
    }
  };

  onFinish = values => {
    const { id: productId, auditRes2, product, preProduct, fullAuditRes2 } = this.state;
    let newAuditRes2 = { ...fullAuditRes2 };
    for (const key in auditRes2) {
      const element = values[key];
      if (JSON.stringify(element) === JSON.stringify(product[key])) {
        debugger;
        message.error(`请修改${keyMap[key]}的值: ${auditRes2[key]}`);
        return;
      }
      delete newAuditRes2[key];
    }

    // 判断素材是否已更新
    // 使用数组的副本进行排序比较，不修改原始数组
    const originalMaterial = JSON.stringify([...preProduct.material].sort());
    const currentMaterial = JSON.stringify([...values.material].sort());

    if (preProduct.status === 7 && originalMaterial === currentMaterial && productId !== 0) {
      Modal.warning({
        title: '提示',
        content:
          '该产品素材已用完，请重新添加新素材，否则直接影响推广效果，屡次不改会禁止更新本产品。',
        okText: '我知道了',
        zIndex: 9999,
      });
      return;
    }

    this.state.product.material = this.state.material;
    const newHash = calculateHash(this.state.product);
    if (newHash !== this.state.hashStr) {
      this.setState({ submiting: true });
      // 有变化， 调用更新接口
      this.props
        .dispatch({
          type: 'product/updateProductById',
          payload: {
            product_id: productId,
            material: values.material,
            audit_res2: newAuditRes2,
          },
        })
        .then(result => {
          this.setState({ submiting: false });
          this.clearCache();
          router.push(`/publish/products/add3/${productId}`);
        })
        .catch(err => {
          this.setState({ submiting: false });
          message.error(err.msg || err.message);
        });
    } else {
      this.setState({ submiting: false });
      this.clearCache();
      router.push(`/publish/products/add3/${productId}`);
    }
  };

  handleFailed = err => {
    message.error(err.errorFields[0].errors[0]);
  };

  saveToCache = () => {
    const cacheData = {
      product: this.state.product,
      material: this.state.material,
    };
    localStorage.setItem(getCacheKey(this.state.id), JSON.stringify(cacheData));
  };

  // 显示批量导入弹窗
  showBatchImport = () => {
    this.setState({
      batchImportVisible: true,
      selectedFile: null, // 清除上次选择的文件
    });
  };

  // 隐藏批量导入弹窗
  hideBatchImport = () => {
    this.setState({
      batchImportVisible: false,
      selectedFile: null,
    });
  };

  // 选择文件
  handleFileSelect = file => {
    this.setState({ selectedFile: file });
    return false; // 阻止默认上传行为
  };

  // 确认导入文件
  confirmBatchImport = () => {
    const { selectedFile } = this.state;
    if (!selectedFile) {
      message.error('请先选择文件');
      return;
    }

    const reader = new FileReader();
    reader.onload = e => {
      try {
        const content = e.target.result;
        this.processBatchContent(content);
      } catch (error) {
        message.error('文件读取失败，请检查文件格式');
      }
    };
    reader.readAsText(selectedFile, 'UTF-8');
  };

  // 处理批量导入的内容
  processBatchContent = async content => {
    // 按空白行分割内容
    const materials = content
      .split(/\n\s*\n/) // 按空白行分割
      .map(item => item.trim()) // 去除首尾空白
      .filter(item => item.length > 0); // 过滤空内容

    let validMaterials = [];
    let discardedCount = 0;
    const originalCount = this.state.material.length;

    // 过滤符合条件的素材
    for (const material of materials) {
      // 检查字数限制
      if (material.length < 50 || material.length > 5000) {
        discardedCount++;
        continue;
      }

      // 检查总数限制
      if (this.state.material.length + validMaterials.length >= 100) {
        discardedCount += materials.length - materials.indexOf(material);
        break;
      }

      validMaterials.push(material);
    }

    if (validMaterials.length === 0) {
      message.warning('没有符合要求的素材可以导入');
      this.setState({ batchImportVisible: false });
      return;
    }

    this.setState({ batchImportVisible: false });

    console.log(`开始批量导入 ${validMaterials.length} 篇素材`);

    // 使用async/await逐个添加素材
    let successCount = 0;
    for (let i = 0; i < validMaterials.length; i++) {
      const material = validMaterials[i];
      console.log(
        `处理第 ${i + 1}/${validMaterials.length} 篇素材，当前已有 ${
          this.state.material.length
        } 篇`,
      );

      // 检查总数限制
      if (this.state.material.length >= 100) {
        const remaining = validMaterials.length - i;
        console.log(`达到100篇限制，舍弃剩余 ${remaining} 篇素材`);
        discardedCount += remaining;
        break;
      }

      try {
        // 使用现有的 checkSimilarity 方法，但不显示错误弹窗
        const result = await this.checkSimilarity([material], -1, false);

        if (result.success) {
          console.log(`✓ 素材 ${i + 1} 添加成功，内容:`, material.substring(0, 50) + '...');
          successCount++;
        } else {
          console.log(`✗ 素材 ${i + 1} 被舍弃，原因:`, result.msg);
          discardedCount++;
        }
      } catch (error) {
        console.error(`✗ 素材 ${i + 1} 添加异常:`, error);
        // 网络错误或其他异常，舍弃当前素材但继续处理下一个
        discardedCount++;
      }
    }

    console.log(`批量导入完成，成功: ${successCount}，舍弃: ${discardedCount}`);

    // 显示导入结果
    if (discardedCount > 0) {
      message.success(
        `导入成功，部分素材不符合要求，已被舍弃。成功导入${successCount}篇，舍弃${discardedCount}篇。`,
      );
    } else {
      message.success(`导入成功，共导入${successCount}篇素材。`);
    }
  };

  render() {
    const { material, currentStep, auditRes2 } = this.state;
    console.log('auditRes2', auditRes2);
    const columns = [
      {
        title: '序号',
        dataIndex: 'number',
        key: 'number',
      },
      {
        title: '素材内容',
        dataIndex: 'content',
        key: 'content',
      },
      {
        title: '操作',
        dataIndex: 'operate',
        key: 'operate',
        width: 200,
        render: (text, record) => {
          return (
            <span>
              <AddSource
                key={`${record.key}-${record.content}`}
                idx={record.key}
                content={record.content}
                onSave={this.onContentChange}
              />
            </span>
          );
        },
      },
    ];
    const data = material.map((item, idx) => {
      return {
        key: idx,
        number: idx + 1,
        content: item,
      };
    });
    const rowSelection = {
      selectedRowKeys: this.state.selectedRowKeys,
      onChange: (selectedRowKeys, selectedRows) => {
        console.log(`selectedRowKeys: ${selectedRowKeys}`, 'selectedRows: ', selectedRows);
        this.setState({ selectedRowKeys });
      },
      getCheckboxProps: record => ({
        disabled: record.name === 'Disabled User', // Column configuration not to be checked
        name: record.name,
      }),
      onSelectAll: (selected, selectedRows, changeRows) => {
        if (selected) {
          // 全选操作
          this.setState({ selectedRowKeys: selectedRows.map((item, idx) => idx) });
        } else {
          // 取消全选操作
          this.setState({ selectedRowKeys: [] });
        }
      },
    };

    return (
      <div className={styles.custombox}>
        <div className={styles.alltitle}>
          <span>新增产品</span>
        </div>
        <div className={styles.customwrap}>
          {Object.keys(auditRes2).length > 0 && (
            <div className={styles.to_examine}>
              <p>审核未通过原因：</p>
              {Object.keys(auditRes2).map(item => (
                <p key={item}>
                  {keyMap[item]}：{auditRes2[item]}。
                </p>
              ))}
            </div>
          )}
          <div style={{ padding: '0 10px' }}>
            <div className={styles.stepbox}>
              <Steps
                current={currentStep}
                onChange={this.onStepChange}
                items={[
                  {
                    title: '添加标题',
                  },
                  {
                    title: '添加素材',
                  },
                  {
                    title: '图片视频',
                  },
                  {
                    title: '属性分类',
                  },
                ]}
              />
            </div>
            <div className={styles.topbox}>
              <AddSource idx={-1} content={''} onSave={this.onContentChange} />{' '}
              <SourceDig single={false} onSave={this.onContentsAdded} />
              {/* 挖掘素材内容组件 */}
              <AiGeneration
                onSave={item => {
                  this.onContentsAdded([item]);
                }}
              />
              {/* 智能生成素材内容组件 */}
              <Button
                type="primary"
                icon={<UploadOutlined />}
                onClick={this.showBatchImport}
                style={{ marginLeft: 16 }}
              >
                批量导入
              </Button>
              {/* 链入添加素材页面组件 */}
            </div>
            <div className={styles.tips}>
              素材内容在50-5000之间，上传15-100篇，素材内容为比较通用的产品定义、描述、外观、组成、特性、结构、范围、注意事项等，需要保证语句完整且通顺。
            </div>
            <Table
              rowSelection={rowSelection}
              pagination={false}
              bordered
              columns={columns}
              dataSource={data}
            />
            {/* 显示素材列表组件 */}
            <div className={styles.chosebutn}>
              <Space>
                <Button onClick={this.selectAllRows}>全选</Button>
                <Button onClick={this.reverSelectRows}>反选</Button>
                <Button onClick={this.deleteSelectedRows}>删除</Button>
              </Space>
            </div>
          </div>
          <Form
            ref={this.formRef}
            onFinish={this.onFinish}
            onFinishFailed={this.handleFailed}
            initialValues={{
              material: material,
            }}
          >
            <Form.Item
              name="material"
              rules={[
                { required: true, message: '请编辑素材' },
                { type: 'array', min: 15, max: 100, message: '素材个数在15~100个' },
              ]}
            ></Form.Item>
            <Form.Item>
              <div className={styles.addtitle_submit}>
                <Space>
                  <Button type="primary" htmlType="submit" loading={this.state.submiting}>
                    保存进入下一步
                  </Button>
                </Space>
              </div>
            </Form.Item>
          </Form>
        </div>

        {/* 批量导入弹窗 */}
        <Modal
          title="批量导入素材"
          open={this.state.batchImportVisible}
          onCancel={this.hideBatchImport}
          footer={[
            <Button key="cancel" onClick={this.hideBatchImport}>
              取消
            </Button>,
            <Button
              key="confirm"
              type="primary"
              onClick={this.confirmBatchImport}
              disabled={!this.state.selectedFile}
            >
              确认
            </Button>,
          ]}
          width={600}
        >
          <Upload
            accept=".txt"
            beforeUpload={this.handleFileSelect}
            showUploadList={true}
            maxCount={1}
            style={{ width: '100%' }}
            fileList={
              this.state.selectedFile
                ? [
                    {
                      uid: '1',
                      name: this.state.selectedFile.name,
                      status: 'done',
                    },
                  ]
                : []
            }
            onRemove={() => this.setState({ selectedFile: null })}
          >
            <Button icon={<UploadOutlined />} style={{ width: '100%', height: 60 }}>
              上传素材
            </Button>
          </Upload>
          <div style={{ marginTop: 16 }}>
            <Text type="secondary">
              说明：批量导入素材仅支持txt格式，系统根据素材里的
              <Text style={{ color: '#000', fontWeight: 'bold' }}>空白行</Text>
              将上传素材自动分成多篇素材，每篇素材字数在50-5000字之间，字数不符合要求的素材将会舍弃。最多添加100篇，超出的素材也将会舍弃。
            </Text>
          </div>
        </Modal>
      </div>
    );
  }
}

export default connect(({ home }) => ({ ...home }))(NewlyaddStep2);
