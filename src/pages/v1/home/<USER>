// @flow
import React, { PureComponent } from 'react';
import { connect } from 'dva';
import { Layout, Menu, Breadcrumb, Row, Col, Card, Space, Steps, List, Modal } from 'antd';
import { QqOutlined, PhoneOutlined } from '@ant-design/icons';
import 'antd/dist/antd.css';
import styles from './Home.less';
import { color } from '@/utils/theme';
import getToken from '@/utils/login';

const { Step } = Steps;

const { Header, Content } = Layout;

class Home extends PureComponent {
  state = {
    isCloseAlert: true,
    modal2Visible: false,
  };

  showCompany = () => {
    this.props.showCompany();
  };

  componentDidMount() {
    const that = this;
    if (getToken()) {
      this.props.dispatch({ type: 'merchant/queryMerchant', payload: {} });
      this.props.dispatch({ type: 'merchant/getMerchantStatus', payload: {} });
      this.props.dispatch({ type: 'info/getInfoStatu', payload: {} });
      this.props.dispatch({ type: 'product/getProductStat', payload: {}, callBack(result2) {} });
    }
  }
  renew = () => {
    this.props.showFunction();
    // this.setState({
    //   isShowManual:true,
    //  })
  };
  reAi = () => {
    this.props.showAi();
    // this.setState({
    //   isShowManual:true,
    //  })
  };
  alertClost = () => {
    this.props.dispatch({ type: 'home/save', payload: { isCloseAlert: true } });
  };

  setModal2Visible(modal2Visible) {
    this.setState({ modal2Visible });
  }

  stepClick = action => {
    if (action == 3) {
      this.setState({ modal2Visible: false });
      this.reAi();
    } else if (action == 1 || action == 2) {
      this.showCompany();
      this.setState({ modal2Visible: false });
    }
  };
  showShopList = () => {
    this.props.showShopList();
  };

  render() {
    const { seekPost, rankPost, canPost, isCloseAlert, InfoStatu = {}, productState } = this.props;
    const { isOpenSou, isOpenAi, isOpenBaixing, merchantStatusList = [] } = this.props;
    let zhuangtai = '已认证';

    for (let i = 0; i < merchantStatusList.length; i++) {
      for (let j = 0; j < merchantStatusList[i].steps.length; j++) {
        if (merchantStatusList[i].steps[j].status == 'error') {
          zhuangtai = '认证中';
        }
      }
    }

    let yesterdayCount = 0;
    Object.keys(InfoStatu).forEach(key => {
      {
        yesterdayCount += InfoStatu[key].count == '-' ? 0 : parseInt(InfoStatu[key].count);
      }
    });
    const Infoview = () => {
      const cateProPertyList = [];
      Object.keys(InfoStatu).forEach(key => {
        cateProPertyList.push(
          <Col>
            {' '}
            <Card className={styles.viewCardItem2} bordered bodyStyle={{ padding: 0 }}>
              <div className={styles.operation}> {InfoStatu[key].count}</div>
              <div className={styles.operation}>{InfoStatu[key].name}</div>
            </Card>
          </Col>,
        );
      });
      return (
        <Row wrap style={{ width: '100%', marginTop: '-5px' }}>
          {cateProPertyList}
        </Row>
      );
    };

    return (
      <div>
        {/* {!isCloseAlert&&<Alert
          type="info"
          description={"为了支持更多网站自动发布，本版本升级了图片管理系统，产品在编辑时需要重新上传图片，给您带来的不便深感抱歉，感谢使用。"}
          closable
          banner
          onClose={this.alertClost}
        /> }  */}

        <div style={{ background: '#fff', padding: 24 }}>
          <div>
            <Row gutter={16}>
              <Col span={12}>
                <div>
                  <Row gutter={16}>
                    <Col span={12}>
                      <Card className={styles.viewCard} bordered bodyStyle={{ padding: 0 }}>
                        <div className={styles.itemContent}>
                          <div className={styles.title}>公司认证</div>
                          <div className={styles.element1}>
                            <div className={styles.child1}>{zhuangtai}</div>
                          </div>
                        </div>
                        <Row style={{ marginTop: '10px' }}>
                          <Col span={12} style={{ textAlign: 'center', color: '#FF6600' }}>
                            <div className={styles.operation} onClick={this.showCompany}>
                              查看资料
                            </div>
                          </Col>
                          <Col span={12} style={{ textAlign: 'center', color: '#FF6600' }}>
                            <div
                              className={styles.operation}
                              onClick={() => {
                                this.setModal2Visible(true);
                              }}
                            >
                              开通进度
                            </div>
                          </Col>
                        </Row>
                      </Card>
                    </Col>

                    <Col span={12}>
                      <Card className={styles.viewCard} bordered bodyStyle={{ padding: 0 }}>
                        <div className={styles.itemContent}>
                          <div className={styles.title}>
                            {(!seekPost || !seekPost) && canPost && '自动发布'}
                            {seekPost && seekPost && !canPost && '查收录排名'}
                            {seekPost && seekPost && canPost && '全部功能'}
                          </div>
                          <div className={styles.element2}>
                            <div className={styles.child1} style={{ color: '#3385FF' }}>
                              已开通
                            </div>
                          </div>
                          <div
                            className={styles.operation}
                            style={{ color: '#3385FF' }}
                            onClick={this.renew}
                          >
                            查看
                          </div>
                        </div>
                      </Card>
                    </Col>
                  </Row>
                </div>
              </Col>

              <Col span={12}>
                <Card className={styles.viewCard2} bordered bodyStyle={{ padding: 0 }}>
                  <List
                    size="large"
                    header={
                      <div style={{ marginLeft: '15px', fontSize: '15px', fontWeight: 'bolder' }}>
                        通知
                      </div>
                    }
                  >
                    <List.Item>
                      <Row justify="space-between">
                        <Col>
                          <a
                            href="http://www.huangye88.com/help/wentis_251.html"
                            target="_blank"
                            rel="noreferrer"
                          >
                            發發助手产品详情发布标准
                          </a>
                        </Col>
                        <Col>2021-10-26</Col>
                      </Row>{' '}
                    </List.Item>

                    <List.Item>
                      <Row justify="space-between">
                        <Col>
                          <a
                            href="http://www.huangye88.com/help/fafazhushous_247.html"
                            target="_blank"
                            rel="noreferrer"
                          >
                            發發助手图片使用规则
                          </a>
                        </Col>
                        <Col>2021-10-26</Col>
                      </Row>{' '}
                    </List.Item>
                  </List>
                </Card>
              </Col>
            </Row>
            <Row>
              <Col span={12}>
                <Card className={styles.viewCard9} bordered bodyStyle={{ padding: 0 }}>
                  <div style={{ marginTop: '5px', marginBottom: '5px' }}>统计数据</div>

                  <Row span={11}>
                    <Col span={6}>
                      <Card className={styles.viewCardtongji1} bordered bodyStyle={{ padding: 0 }}>
                        <div>{productState.productSize}</div>
                        <div style={{ fontSize: '12px' }}>已添加产品数</div>
                      </Card>
                    </Col>
                    <Col span={18}>
                      <Row wrap style={{ width: '100%', marginTop: '-5px' }}>
                        <Col>
                          {' '}
                          <Card className={styles.viewCardItem} bordered bodyStyle={{ padding: 0 }}>
                            <div className={styles.operation}> {productState.draft} </div>
                            <div className={styles.operation}>草稿</div>
                          </Card>
                        </Col>
                        <Col>
                          {' '}
                          <Card className={styles.viewCardItem} bordered bodyStyle={{ padding: 0 }}>
                            <div className={styles.operation}> {productState.Pending} </div>
                            <div className={styles.operation}>待审核</div>
                          </Card>
                        </Col>
                        <Col>
                          {' '}
                          <Card className={styles.viewCardItem} bordered bodyStyle={{ padding: 0 }}>
                            <div className={styles.operation}>{productState.unPassed} </div>
                            <div className={styles.operation}>审核未通过</div>
                          </Card>
                        </Col>
                        <Col>
                          {' '}
                          <Card className={styles.viewCardItem} bordered bodyStyle={{ padding: 0 }}>
                            <div className={styles.operation}> {productState.passed} </div>
                            <div className={styles.operation}>审核通过</div>
                          </Card>
                        </Col>
                        <Col>
                          {' '}
                          <Card className={styles.viewCardItem} bordered bodyStyle={{ padding: 0 }}>
                            <div className={styles.operation}> {productState.Promoting} </div>
                            <div className={styles.operation}>推广中</div>
                          </Card>
                        </Col>
                        <Col>
                          {' '}
                          <Card className={styles.viewCardItem} bordered bodyStyle={{ padding: 0 }}>
                            <div className={styles.operation}> {productState.NoData} </div>
                            <div className={styles.operation}>素材已用完</div>
                          </Card>
                        </Col>
                        <Col>
                          {' '}
                          <Card className={styles.viewCardItem} bordered bodyStyle={{ padding: 0 }}>
                            <div className={styles.operation}> {productState.NoMsgTitle}</div>
                            <div className={styles.operation}>信息标题已用完</div>
                          </Card>
                        </Col>
                        <Col>
                          {' '}
                          <Card className={styles.viewCardItem} bordered bodyStyle={{ padding: 0 }}>
                            <div className={styles.operation}> {productState.NoProductTitle}</div>
                            <div className={styles.operation}>产品标题已用完</div>
                          </Card>
                        </Col>
                      </Row>
                    </Col>
                  </Row>
                  <Row span={11} style={{ marginTop: '10px' }}>
                    <Col span={6}>
                      <Card className={styles.viewCardtongji2} bordered bodyStyle={{ padding: 0 }}>
                        <div>{yesterdayCount}</div>
                        <div style={{ fontSize: '12px' }}>昨天新增信息数</div>
                      </Card>
                    </Col>
                    <Col span={18}>
                      <Infoview />
                    </Col>
                  </Row>
                </Card>
              </Col>
              <Col span={11} style={{ marginTop: '15px', marginLeft: '15px' }}>
                <Row span={24} justify="space-between">
                  <Col span={6}>
                    <Card
                      className={styles.viewCard3}
                      bordered
                      bodyStyle={{ padding: 0 }}
                      onClick={() => {
                        this.props.showRanking();
                      }}
                    >
                      <div className={styles.iconWarp}>
                        {' '}
                        <img alt="" width="45px" height="45px" src={require('@/assets/rank.jpg')} />
                      </div>
                      <div className={styles.operation}>排名统计</div>
                    </Card>
                  </Col>

                  <Col span={6}>
                    <Card
                      className={styles.viewCard3}
                      bordered
                      bodyStyle={{ padding: 0 }}
                      onClick={() => {
                        this.props.showHistoryList();
                      }}
                    >
                      <div className={styles.iconWarp}>
                        <img
                          alt=""
                          width="45px"
                          height="45px"
                          src={require('@/assets/history.jpg')}
                        />
                      </div>
                      <div className={styles.operation}>发布历史</div>
                    </Card>
                  </Col>
                  <Col span={6}>
                    <Card
                      className={styles.viewCard3}
                      bordered
                      bodyStyle={{ padding: 0 }}
                      onClick={this.showShopList}
                    >
                      <div className={styles.iconWarp}>
                        <img alt="" width="45px" height="45px" src={require('@/assets/shop.png')} />
                      </div>
                      <div className={styles.operation}>B2B商铺</div>
                    </Card>
                  </Col>
                </Row>
                <Row>
                  <Card className={styles.viewCard10} bordered bodyStyle={{ padding: 0 }}>
                    <Row justify="space-around" align="middle">
                      <Col>
                        <Row justify="space-around" style={{ height: '100%', textAlign: 'center' }}>
                          <Col>
                            <div className={styles.iconWarp}>
                              <PhoneOutlined alt="" width="40px" height="40px" />
                            </div>
                          </Col>
                          <Col style={{ marginLeft: '5px' }}>
                            <Row>电话：************</Row>
                            <Row>QQ号：1643503640</Row>
                          </Col>
                        </Row>
                      </Col>
                      <Col>
                        <div className={styles.iconWarp}>
                          <img
                            alt=""
                            width="60%"
                            height="60%"
                            src={'http://mentalroad-adv.oss-cn-shanghai.aliyuncs.com/wechart.jpg'}
                          />
                        </div>
                        <div className={styles.operation}>微信公众号</div>
                      </Col>
                    </Row>
                  </Card>
                </Row>
              </Col>
            </Row>
          </div>
          <Modal
            title="平台开通进度"
            centered
            width={1000}
            visible={this.state.modal2Visible}
            onOk={() => this.setModal2Visible(false)}
            onCancel={() => this.setModal2Visible(false)}
          >
            <List
              itemLayout="horizontal"
              dataSource={merchantStatusList}
              renderItem={item => (
                <List.Item>
                  <Row wrap>
                    <Col style={{ lineHeight: '35px', marginLeft: '10px' }}>{item.name}</Col>
                    <Col style={{ lineHeight: '35px', marginLeft: '10px' }}>
                      {' '}
                      <Steps size="small" current={item.current} className="site-navigation-steps">
                        {item.steps.map((stepItem, index) => {
                          return (
                            <Step
                              status={stepItem.status}
                              title={stepItem.title}
                              disabled={stepItem.disabled}
                              description={stepItem.reason}
                              onClick={() => {
                                this.stepClick(stepItem.action);
                              }}
                            />
                          );
                        })}
                      </Steps>
                    </Col>
                  </Row>
                </List.Item>
              )}
            />
          </Modal>
        </div>
      </div>
    );
  }
}
export default connect(({ rancking, home, merchant, info, product }) => ({
  ...rancking,
  ...home,
  ...merchant,
  ...info,
  ...product,
}))(Home);
