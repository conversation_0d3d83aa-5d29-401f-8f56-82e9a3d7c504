.itemView {
  border: 1px solid #e6e6e6;
  height: 60px;
  align-items: center;
  padding: 10px 15px;
}

.viewCard2 {
  height: 13rem;
  width: 100%;
  cursor: pointer;
  overflow-y: scroll;
  overflow-x: hidden;
  margin-top: 15px;
  :global {
    .ant-steps-item-description {
      font-size: 10px;
    }
    .ant-steps-item-title {
      font-size: 12px;
    }
    .ant-steps {
      display: block;
    }
  }
}

.viewCard3 {
  height: 6rem;
  width: 100%;
  cursor: pointer;
  justify-content: center;
  display: flex;
  align-items: center;
  align-content: center;

  .iconWarp {
    text-align: center;
  }
  .operation {
    text-align: center;
    margin-top: 5px;
  }
}
.viewCardItem {
  border-radius: 5px;
  margin-left: 5px;
  height: 38px;
  width: 5rem;
  cursor: pointer;
  justify-content: center;
  display: flex;
  align-items: center;
  align-content: center;
  text-align: center;
  font-size: 13px;
  margin-top: 5px;
  color: #9f6414;
  min-width: 5rem;
  padding-left: 5px;
  padding-right: 5px;
  padding-bottom: 5px;
  padding-top: 5px;
}
.viewCardItem2 {
  border-radius: 5px;
  margin-left: 5px;
  height: 38px;
  min-width: 5rem;
  padding-left: 5px;
  padding-right: 5px;
  padding-bottom: 5px;
  padding-top: 5px;
  cursor: pointer;
  justify-content: center;
  display: flex;
  align-items: center;
  align-content: center;
  text-align: center;
  font-size: 13px;
  margin-top: 5px;
  color: #05429c;
}

.viewCardtongji1 {
  border-radius: 5px;
  height: 5rem;
  width: 100%;
  cursor: pointer;
  justify-content: center;
  display: flex;
  align-items: center;
  align-content: center;
  background-color: #fce3c2;
  text-align: center;
  color: #9f6414;
}

.viewCardtongji2 {
  border-radius: 5px;
  height: 5rem;
  width: 100%;
  cursor: pointer;
  justify-content: center;
  display: flex;
  align-items: center;
  align-content: center;
  background-color: #c6d8f9;
  text-align: center;
  color: #05429c;
}
.viewCard10 {
  padding: 10px 15px;
  height: 7rem;
  width: 100%;
  cursor: pointer;
  margin-top: 15px;
}

.viewCard9 {
  padding: 10px 15px;
  height: 14rem;
  width: 100%;
  cursor: pointer;
  margin-top: 15px;
  .itemContent {
    width: 100%;
    height: 100%;
    text-align: center;
    display: flex;
    flex-wrap: wrap;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    align-self: center;
    .iconWarp {
      font-size: 54px;
      float: center;
      text-align: center;
      display: flex;
    }
    .element1 {
      width: 8rem;
      height: 8rem;
      background: linear-gradient(90deg, #ff8931, #ffb84c);
      border-radius: 50%;
      justify-content: center;
      display: flex;
      align-items: center;
    }
    .element2 {
      width: 8rem;
      height: 8rem;
      background: linear-gradient(90deg, #3b8aff, #95c0ff);
      border-radius: 50%;
      justify-content: center;
      display: flex;
      align-items: center;
    }
    .child1 {
      width: 6rem;
      height: 6rem;
      border-radius: 50%;
      background-color: #ffffff;
      position: relative;
      justify-content: center;
      display: flex;
      text-align: center;
      align-items: center;
      color: #ff6600;
      font-size: 14px;
    }
    .element3 {
      width: 8rem;
      height: 8rem;
      background: linear-gradient(90deg, #fb6264, #f1b5b2);
      border-radius: 50%;
      justify-content: center;
      display: flex;
      align-items: center;
    }
    .child2 {
      position: relative;
      justify-content: center;
      display: flex;
      text-align: center;
      align-items: center;
      color: #ff6600;
      font-size: 36px;
      font-weight: 600;
    }

    .title {
      line-height: 16px;
      font-size: 16px;
      margin-bottom: 8px;
      margin-top: 5px;
      height: 16px;
      text-align: center;
      display: flex;
      font-weight: bold;
    }

    .operation {
      align-content: flex-end;
      line-height: 14px;
      font-size: 14px;
      height: 14px;
      text-align: center;
      margin-top: 10px;
      color: #ff6600;
      display: flex;
    }
  }
}
.viewCard {
  padding: 10px 15px;
  height: 13rem;
  width: 100%;
  cursor: pointer;
  margin-top: 15px;
  .itemContent {
    width: 100%;
    height: 100%;
    text-align: center;
    display: flex;
    flex-wrap: wrap;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    align-self: center;
    .iconWarp {
      font-size: 54px;
      float: center;
      text-align: center;
      display: flex;
    }
    .element1 {
      width: 8rem;
      height: 8rem;
      background: linear-gradient(90deg, #ff8931, #ffb84c);
      border-radius: 50%;
      justify-content: center;
      display: flex;
      align-items: center;
    }
    .element2 {
      width: 8rem;
      height: 8rem;
      background: linear-gradient(90deg, #3b8aff, #95c0ff);
      border-radius: 50%;
      justify-content: center;
      display: flex;
      align-items: center;
    }
    .child1 {
      width: 6rem;
      height: 6rem;
      border-radius: 50%;
      background-color: #ffffff;
      position: relative;
      justify-content: center;
      display: flex;
      text-align: center;
      align-items: center;
      color: #ff6600;
      font-size: 14px;
    }
    .element3 {
      width: 8rem;
      height: 8rem;
      background: linear-gradient(90deg, #fb6264, #f1b5b2);
      border-radius: 50%;
      justify-content: center;
      display: flex;
      align-items: center;
    }
    .child2 {
      position: relative;
      justify-content: center;
      display: flex;
      text-align: center;
      align-items: center;
      color: #ff6600;
      font-size: 36px;
      font-weight: 600;
    }

    .title {
      line-height: 16px;
      font-size: 16px;
      margin-bottom: 8px;
      margin-top: 5px;
      height: 16px;
      text-align: center;
      display: flex;
      font-weight: bold;
    }

    .operation {
      align-content: flex-end;
      line-height: 14px;
      font-size: 14px;
      height: 14px;
      text-align: center;
      margin-top: 10px;
      color: #ff6600;
      display: flex;
    }
  }
}
.kftop {
  width: 100%;
  display: flex;
  flex-direction: row;
  line-height: 30px;
  background-color: #fff3e8;
  font-size: 12px;
  padding-left: 10px;
}
.kfcontent {
  width: 100%;
  display: flex;
  flex-direction: row;
  padding: 10px;
  .iconView {
    justify-content: center;
    font-size: 20px;
  }
  .kfcontent1 {
    display: flex;
    flex-direction: column;
    flex: 1;
  }
  .kfcontent2 {
    display: flex;
    flex-direction: column;
    flex: 1;
    justify-content: flex-start;
  }
  .kfIconContent {
    margin-top: 10px;
    display: flex;
    flex-direction: row;
    align-items: center;
  }
  .kfTextContent {
    margin-left: 5px;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
  .erweima {
    width: 50px;
    height: 50px;
  }
}
