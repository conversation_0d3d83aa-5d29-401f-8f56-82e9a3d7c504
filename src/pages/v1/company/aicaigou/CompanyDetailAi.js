// @flow
import React, { PureComponent } from 'react';
import { connect } from 'dva';
import {
  Form,
  Input,
  Tooltip,
  Alert,
  Cascader,
  Select,
  Row,
  Col,
  Radio,
  Image,
  Button,
  AutoComplete,
  Upload,
  Layout,
  message,
  Space,
  Modal,
  DatePicker,
} from 'antd';
import moment from 'moment';
import { CloseOutlined, PhoneOutlined, UploadOutlined } from '@ant-design/icons';
import 'antd/dist/antd.css';
import { baseUrl } from '@/configs/config';
import styles from './CompanyDetailAi.less';
import ImgUpload from '@/components/ImgUpload';

const { RangePicker } = DatePicker;

const { TextArea } = Input;
const { Option } = Select;
const AutoCompleteOption = AutoComplete.Option;

const { Header, Content } = Layout;
function getBase64(img, callback) {
  const reader = new FileReader();
  reader.addEventListener('load', () => callback(reader.result));
  reader.readAsDataURL(img);
}

class CompanyDetailForm extends PureComponent {
  formRef = React.createRef();
  state = {
    confirmDirty: false,
    company: {},
    fileList: [],
    imageUrlcontract_file: '',
    imageUrlLogo: '',
    imageUrlbusiness_img: '',
    company_type: '企业',
    isChange: false,
    CateList: [],
    cityList: [],
    contract_begin_date: '',
    contract_end_date: '',
    contract_date: [],
    isUpdate: false,
    FormatError: false,
  };
  componentDidMount() {
    const that = this;
    const form = this.formRef.current;
    const { company = {}, cate, city } = this.props;
    this.props.dispatch({ type: 'merchant/queryMerchant', payload: {} });
    that.setState({
      CateList: cate,
      cityList: city,
    });

    this.props.dispatch({
      type: 'company/getMyAiCompany',
      payload: {},
      callBack(company) {
        that.setState(
          {
            company,
            isChange: !that.state.isChange,
          },
          () => {
            const {
              contract_file,
              company_logo,
              business_img,
              contract_begin_date,
              contract_end_date,
              id,
              company_areaids,
              company_type,
            } = company;
            console.log(id);
            if (id != undefined) {
              that.setState({
                isUpdate: true,
              });
            }
            const dateFormat = 'YYYY-MM-DD';
            if (contract_begin_date) {
              that.setState({
                contract_begin_date,
                contract_end_date,
                contract_date: [
                  moment(contract_begin_date, dateFormat),
                  moment(contract_end_date, dateFormat),
                ],
                company_areaids,
                company_type,
              });
            }

            if (contract_file) {
              if (contract_file.indexOf('http') != -1) {
                that.setState({
                  imageUrlcontract_file: contract_file,
                });
              } else {
                that.setState({
                  imageUrlcontract_file: `${baseUrl}/${contract_file}`,
                });
              }
            }

            if (company_logo) {
              if (company_logo.indexOf('http') != -1) {
                that.setState({
                  imageUrlLogo: company_logo,
                });
                that.formRef.current.setFieldsValue({
                  company_logo,
                });
              } else {
                that.setState({
                  imageUrlLogo: `${baseUrl}/${company_logo}`,
                });
              }
            }
            console.log(business_img);
            if (business_img) {
              if (business_img.indexOf('http') != -1) {
                that.setState({
                  imageUrlbusiness_imgList: [{ uid: '1', url: business_img }],
                  imageUrlbusiness_img: business_img,
                });
                that.formRef.current.setFieldsValue({
                  business_img,
                });
              } else {
                that.setState({
                  imageUrlbusiness_img: `${baseUrl}/${business_img}`,
                  imageUrlbusiness_imgList: [{ uid: '1', url: `${baseUrl}/${business_img}` }],
                });
              }
            }
            form.resetFields();
          },
        );
      },
    });
  }
  handleSubmit = values => {
    const { cate } = values;
    const { company = {} } = this.state;
    const { isOpenSou } = this.props;

    const {
      imageUrlcontract_file,
      imageUrlLogo,
      imageUrlbusiness_img,
      company_type,
      contract_begin_date,
      contract_end_date,
      isUpdate,
      FormatError,
    } = this.state;
    const that = this;
    const payload = {
      ...values,
      contract_file: imageUrlcontract_file,
      company_logo: imageUrlLogo,
      business_img: imageUrlbusiness_img,
      company_type,
      contract_begin_date,
      contract_end_date,
      isUpdate,
    };
    const name = values.company_name;
    if (name == null || name.length == 0) {
      Modal.error({
        content: '公司名称不能为空',
        zIndex: 9999,
      });
      return;
    }
    console.log(contract_begin_date);
    console.log(contract_end_date);
    if (name.length > 50) {
      Modal.error({
        content: '公司名称不超过50字',
        zIndex: 9999,
      });
      return;
    }
    const company_areaids = values.company_areaids;
    if (company_areaids == null || company_areaids.length < 2) {
      Modal.error({
        content: '公司地址不能为空，且至少选到第二级',
        zIndex: 9999,
      });
      return;
    }
    const bank_areaids = values.bank_areaids;
    if (bank_areaids == null || bank_areaids.length < 2) {
      Modal.error({
        content: '银行地址不能为空，且至少选到第二级',
        zIndex: 9999,
      });
      return;
    }

    const webreg = /(((^https?:(?:\/\/)?)(?:[-;:&=\+\$,\w]+@)?[A-Za-z0-9.-]+(?::\d+)?|(?:www.|[-;:&=\+\$,\w]+@)[A-Za-z0-9.-]+)((?:\/[\+~%\/.\w-_]*)?\??(?:[-\+=&;%@.\w_]*)#?(?:[\w]*))?)$/;

    const company_web = values.company_web;
    if (company_web == null || company_web.length == 0) {
      Modal.error({
        content: '公司网址不能为空',
        zIndex: 9999,
      });
      return;
    }

    if (!webreg.test(company_web)) {
      // 正则验证不通过，格式不对
      Modal.error({
        content: '公司网址格式不正确',
        zIndex: 9999,
      });
      return;
    }

    const brank_name = values.brank_name;
    if (brank_name == null || brank_name.length == 0) {
      Modal.error({
        content: '银行名称不能为空',
        zIndex: 9999,
      });
      return;
    }
    if (brank_name.length > 50) {
      Modal.error({
        content: '银行名称不超过20字',
        zIndex: 9999,
      });
      return;
    }

    const open_branch = values.open_branch;
    if (open_branch == null || open_branch.length == 0) {
      Modal.error({
        content: '开户支行不能为空',
        zIndex: 9999,
      });
      return;
    }
    if (open_branch.length > 50) {
      Modal.error({
        content: '开户支行不超过20字',
        zIndex: 9999,
      });
      return;
    }

    const card_number = values.card_number;
    if (card_number == null || card_number.length == 0) {
      Modal.error({
        content: '银行卡号不能为空',
        zIndex: 9999,
      });
      return;
    }
    if (card_number.length > 30) {
      Modal.error({
        content: '银行卡号不超过30字',
        zIndex: 9999,
      });
      return;
    }
    const credit_codeReg = /[^_IOZSVa-z\W]{2}\d{6}[^_IOZSVa-z\W]{10}$/g;

    const social_credit_code = values.social_credit_code;
    if (social_credit_code == null || social_credit_code.length == 0) {
      Modal.error({
        content: '社会统一信用代码不能为空',
        zIndex: 9999,
      });
      return;
    }
    if (social_credit_code.length > 20) {
      Modal.error({
        content: '社会统一信用代码不超过20字',
        zIndex: 9999,
      });
      return;
    } else if (!credit_codeReg.test(social_credit_code)) {
      // 正则验证不通过，格式不对
      Modal.error({
        content: '社会统一信用代码格式不正确',
        zIndex: 9999,
      });
      return;
    }

    const link_person = values.link_person;
    if (link_person == null || link_person.length == 0) {
      Modal.error({
        content: '联系人不能为空',
        zIndex: 9999,
      });
      return;
    }
    if (link_person.length > 20) {
      Modal.error({
        content: '联系人姓名不超过20字',
        zIndex: 9999,
      });
      return;
    }

    const phoneReg = /^1\d{10}$/;
    const LinkPhone = values.LinkPhone;
    if (LinkPhone == null || LinkPhone.length == 0) {
      Modal.error({
        content: '手机号不能为空',
        zIndex: 9999,
      });
      return;
    }
    if (LinkPhone.length != 11) {
      Modal.error({
        content: '手机号必须为11位',
        zIndex: 9999,
      });
      return;
    } else if (!phoneReg.test(LinkPhone)) {
      // 正则验证不通过，格式不对
      Modal.error({
        content: '手机号格式不正确',
        zIndex: 9999,
      });
      return;
    }
    const reg = /^[a-zA-Z0-9.!#$%&'*+\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;
    const link_email = values.link_email;
    if (link_email == null || link_email.length == 0) {
      Modal.error({
        content: '邮箱地址不能为空',
        zIndex: 9999,
      });
      return;
    }
    if (link_email.length > 50) {
      Modal.error({
        content: '邮箱地址不超过20字',
        zIndex: 9999,
      });
      return;
    } else if (!reg.test(link_email)) {
      // 正则验证不通过，格式不对
      Modal.error({
        content: '邮箱格式不正确',
        zIndex: 9999,
      });
      return;
    }
    if (FormatError) {
      Modal.error({
        content: '合同格式不正确',
        zIndex: 9999,
      });
      return;
    }
    if (imageUrlcontract_file == null || imageUrlcontract_file.length == 0) {
      Modal.error({
        content: '合同不能为空',
        zIndex: 9999,
      });
      return;
    }
    if (imageUrlLogo == null || imageUrlLogo.length == 0) {
      Modal.error({
        content: '公司LOGO不能为空',
        zIndex: 9999,
      });
      return;
    }
    if (imageUrlbusiness_img == null || imageUrlbusiness_img.length == 0) {
      Modal.error({
        content: '营业执照不能为空',
        zIndex: 9999,
      });
      return;
    }
    this.props.dispatch({
      type: 'company/changeAiCompany',
      payload,
      callBack(data) {
        if (data.msg == 'success') {
          message.success('保存成功');
        } else {
          message.error(`保存失败,${data.msg}`);
        }
      },
    });
  };

  handleChangeBusiness = info => {
    const { file, event = {} } = info;
    const { percent = 0 } = event;
    const that = this;
    if (!this.state.business_imgBefor) {
      that.setState({
        imageUrlbusiness_imgList: [],
        imageUrlbusiness_img: '',
        loading: false,
      });
      return;
    }

    if (file && file.status == 'done') {
      const licenseSrc = file.response.data[0].url;
      that.formRef.current.setFieldsValue({
        business_img: licenseSrc,
      });
      that.setState({
        imageUrlbusiness_imgList: [{ uid: '1', url: licenseSrc }],
        imageUrlbusiness_img: licenseSrc,
        loading: false,
      });
    }
  };
  beforeUploadbusiness_img = file => {
    console.log(file);
    const isJPG = file.type === 'image/jpeg';
    if (!isJPG) {
      message.error('图片上传格式必须为jpg');
    } else if (
      file.name &&
      file.name.substr(file.name.lastIndexOf('.') + 1, file.name.length) != 'jpg'
    ) {
      message.error('图片名称后缀必须为.jpg');
      return false;
    }
    if (isJPG) {
      this.setState({
        business_imgBefor: true,
      });
    } else {
      this.setState({
        business_imgBefor: false,
      });
    }
    return isJPG;
  };

  onChangeLicense = img => {
    this.setState({
      imageUrlbusiness_img: img,
    });
    this.formRef.current.setFieldsValue({
      business_img: img,
    });
  };

  onChangeLogo = img => {
    this.setState({
      imageUrlLogo: img,
    });
    this.formRef.current.setFieldsValue({
      company_logo: img,
    });
  };

  beforeUploadcontract_file = file => {
    const isPDF = file.type === 'application/pdf';
    if (!isPDF) {
      message.error('合同上传格式必须为PDF格式');
    }
    const isLt2M = file.size / 1024 / 1024 < 8;
    if (!isLt2M) {
      message.error('合同大小必须小于 8MB!');
    }
  };

  handleChangeContractFile = info => {
    const { file, event = {} } = info;
    const { percent = 0 } = event;
    const that = this;

    if (file && file.status == 'done') {
      console.log(file.response.data[0].msg);
      if (file.response.data[0].msg != '') {
        message.error(file.response.data[0].msg);
        that.setState({
          FormatError: true,
        });
      } else {
        const licenseSrc = file.response.data[0].url;
        that.formRef.current.setFieldsValue({
          contract_file: licenseSrc,
        });
        that.setState({
          imageUrlcontract_file: licenseSrc,
          FormatError: false,
          loading: false,
        });
        message.success('提交成功');
      }
    }
  };

  onRemoveContractFile = info => {
    const that = this;
    that.setState({
      imageUrlcontract_file: '',
    });
    that.formRef.current.setFieldsValue({
      contract_file: '',
    });
  };

  handleChangeCompanyType = value => {
    this.setState({
      company_type: value,
    });
  };

  onChange = (value, selectedOptions) => {
    const that = this;
    const cate = [];
    selectedOptions.map(item => {
      cate.push(item.value);
    });
    this.setState({ cateChange: true });

    this.formRef.current.setFieldsValue({
      cate,
    });
    if (cate.length > 2) {
      that.props.dispatch({
        type: 'product/getChildCateById',
        payload: { id: cate[cate.length - 1] },
        callBack(result2) {
          if (result2.length == 0) {
            that.props.dispatch({
              type: 'product/getCateProperty',
              payload: { id: cate[cate.length - 1] },
              callBack(result3) {
                that.setState({
                  propertiesChange: true,
                  properties: {},
                  cateProPerty: result3,
                });
              },
            });
          }
        },
      });
    }
  };
  onCateLoadData = selectedOptions => {
    console.log(selectedOptions);
    const { CateList, isUpdateView } = this.state;
    const that = this;
    let id = 0;
    selectedOptions.map(item => {
      console.log(item);
      id = item.value;
    });

    const targetOption = selectedOptions[selectedOptions.length - 1];
    targetOption.loading = true;
    this.props.dispatch({
      type: 'product/getCateByPid',
      payload: { id, cateLength: selectedOptions.length },
      callBack(result2) {
        for (let i = 0; i < CateList.length; i++) {
          if (CateList[i].value == targetOption.value) {
            if (result2 != undefined) {
              CateList[i].children = result2;
            } else {
              CateList[i].isLeaf = true;
            }
            CateList[i].loading = false;
            console.log(CateList);
            that.setState({
              cateChange: true,
              isUpdateView: !isUpdateView,
              CateList,
            });
            return;
          }
          console.log(CateList[i].children);
          if (CateList[i].children != undefined && CateList[i].children.length > 0) {
            const children = CateList[i].children;
            for (let j = 0; j < children.length; j++) {
              if (children[j].value == targetOption.value) {
                if (result2 != undefined) {
                  CateList[i].children[j].children = result2;
                } else {
                  CateList[i].children[j].isLeaf = true;
                }
                CateList[i].children[j].loading = false;
                console.log(CateList);
                that.setState({
                  cateChange: true,
                  isUpdateView: !isUpdateView,
                  CateList,
                });
                return;
              }
              if (
                CateList[i].children[j].children != undefined &&
                CateList[i].children[j].children.length > 0
              ) {
                const childrenz = CateList[i].children[j].children;
                for (let z = 0; z < childrenz.length; z++) {
                  if (childrenz[z].value == targetOption.value) {
                    if (result2 != undefined) {
                      CateList[i].children[j].children[z].children = result2;
                    } else {
                      CateList[i].children[j].children[z].isLeaf = true;
                    }
                    CateList[i].children[j].children[z].loading = false;
                    console.log(CateList);
                    that.setState({
                      cateChange: true,
                      isUpdateView: !isUpdateView,
                      CateList,
                    });
                    return;
                  }
                  if (
                    CateList[i].children[j].children[z].children != undefined &&
                    CateList[i].children[j].children[z].children.length > 0
                  ) {
                    const childrenk = CateList[i].children[j].children[z].children;
                    for (let k = 0; k < childrenk.length; k++) {
                      if (childrenk[k].value == targetOption.value) {
                        if (result2 != undefined) {
                          CateList[i].children[j].children[z].children[k].children = result2;
                        } else {
                          CateList[i].children[j].children[z].children[k].isLeaf = true;
                        }
                        CateList[i].children[j].children[z].children[k].loading = false;
                        console.log(CateList);
                        that.setState({
                          cateChange: true,
                          isUpdateView: !isUpdateView,
                          CateList,
                        });
                        return;
                      }
                    }
                  }
                }
              }
            }
          }
        }
      },
    });
  };

  onCityLoadData = selectedOptions => {
    console.log(selectedOptions);
    const { cityList, isUpdateView } = this.state;
    const that = this;
    let id = 0;
    selectedOptions.map(item => {
      console.log(item);
      id = item.value;
    });

    const targetOption = selectedOptions[selectedOptions.length - 1];
    targetOption.loading = true;
    this.props.dispatch({
      type: 'product/getCityByPid',
      payload: { id },
      callBack(result2) {
        for (let i = 0; i < cityList.length; i++) {
          if (cityList[i].value == targetOption.value) {
            if (result2 != undefined) {
              cityList[i].children = result2;
            } else {
              cityList[i].isLeaf = true;
            }
            cityList[i].loading = false;

            that.setState({
              areaidsChange: true,
              isUpdateView: !isUpdateView,
              cityList,
            });
            return;
          }
          console.log(cityList[i].children);
          if (cityList[i].children != undefined && cityList[i].children.length > 0) {
            const children = cityList[i].children;
            for (let j = 0; j < children.length; j++) {
              if (children[j].value == targetOption.value) {
                if (result2 != undefined) {
                  cityList[i].children[j].children = result2;
                } else {
                  cityList[i].children[j].isLeaf = true;
                }
                cityList[i].children[j].loading = false;
                console.log(cityList);
                that.setState({
                  areaidsChange: true,
                  isUpdateView: !isUpdateView,
                  cityList,
                });
                return;
              }
              if (
                cityList[i].children[j].children != undefined &&
                cityList[i].children[j].children.length > 0
              ) {
                const childrenz = cityList[i].children[j].children;
                for (let z = 0; z < childrenz.length; z++) {
                  if (childrenz[z].value == targetOption.value) {
                    if (result2 != undefined) {
                      cityList[i].children[j].children[z].children = result2;
                    } else {
                      cityList[i].children[j].children[z].isLeaf = true;
                    }
                    cityList[i].children[j].children[z].loading = false;
                    console.log(cityList);
                    that.setState({
                      areaidsChange: true,
                      isUpdateView: !isUpdateView,
                      cityList,
                    });
                    return;
                  }
                }
              }
            }
          }
        }
      },
    });
  };

  onChangeCity = (value, selectedOptions) => {
    const that = this;
    const areaids = [];
    console.log(selectedOptions);
    selectedOptions.map(item => {
      areaids.push(item.value);
    });
    that.formRef.current.setFieldsValue({
      company_areaids: areaids,
    });

    this.setState({
      areaidsChange: true,
      company_areaids: areaids,
    });
  };
  onChangeBankCity = (value, selectedOptions) => {
    const that = this;
    const areaids = [];
    console.log(selectedOptions);
    selectedOptions.map(item => {
      areaids.push(item.value);
    });

    this.setState({
      areaidsChange: true,
      bank_areaids: areaids,
    });
  };

  onChangeGender = e => {
    this.formRef.current.setFieldsValue({
      gender: e.target.value,
    });
  };

  filter = (inputValue, path) => {
    return path.some(option => option.label.toLowerCase().indexOf(inputValue.toLowerCase()) > -1);
  };

  onContractDateChange = (date, dateString) => {
    this.setState({
      contract_begin_date: dateString[0],
      contract_end_date: dateString[1],
      contract_date: dateString,
    });
  };

  render() {
    const {
      imageUrlcontract_file,
      imageUrlLogo,
      imageUrlbusiness_img,
      contract_date,
      imageUrlLogoList,
    } = this.state;
    console.log(imageUrlbusiness_img);

    const uploadButton = <div />;

    const formItemLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 14 },
    };
    const tailFormItemLayout = {
      wrapperCol: {
        xs: {
          span: 24,
          offset: 0,
        },
        sm: {
          span: 16,
          offset: 8,
        },
      },
    };
    const { company = {} } = this.state;
    const Authorization = {
      Authorization: `Bearer ${window.sessionStorage.getItem('Authorization')}`,
      multiple: '',
    };
    const { reason } = company;
    const { isOpenSou } = this.props;
    const can_edit = true;
    const monthFormat = 'YYYY-MM-DD';

    return (
      <div style={{ background: '#fff', padding: 24 }}>
        <div>
          <div className={styles.titleName}>爱采购认证 </div>

          <Form
            ref={this.formRef}
            className={styles.tableView}
            {...formItemLayout}
            onFinish={this.handleSubmit}
            initialValues={{
              company_name: company.company_name,
              company_type: company.company_type ? company.company_type : '企业',
              company_areaids:
                company && company.company_areaids && company.company_areaids[0] == '0'
                  ? []
                  : company.company_areaids,
              bank_areaids:
                company && company.bank_areaids && company.bank_areaids[0] == '0'
                  ? []
                  : company.bank_areaids,
              brank_name: company.brank_name,
              card_number: company.card_number,

              company_web: company.company_web,
              link_email: company.link_email,
              link_person: company.link_person,
              open_branch: company.open_branch,
              social_credit_code: company.social_credit_code,
              LinkPhone: company.LinkPhone,
            }}
          >
            <Form.Item
              label={<span className={styles.lableName}>公司名称&nbsp;</span>}
              name="company_name"
              rules={[{ required: true, message: '请输入公司名称', whitespace: true }]}
            >
              <Input placeholder="请输入公司名称" disabled={!can_edit} />
            </Form.Item>
            <Form.Item
              label={
                <span>
                  <span style={{ color: 'red' }}>*</span> 公司地址&nbsp;
                </span>
              }
              name="company_areaids"
            >
              <Cascader
                options={this.state.cityList}
                loadData={this.onCityLoadData}
                onChange={this.onChangeCity}
                placeholder="全国"
                defaultValue={company.company_areaids}
                changeOnSelect
                disabled={!can_edit}
                getPopupContainer={triggerNode => triggerNode.parentNode}
              />
            </Form.Item>
            <Form.Item label={<span>公司类型&nbsp;</span>} name="company_type">
              <Select
                defaultValue="企业"
                style={{ width: 120 }}
                value={this.state.company_type}
                onChange={this.handleChangeCompanyType}
                getPopupContainer={triggerNode => triggerNode.parentNode}
              >
                <Option value="企业">企业</Option>
                <Option value="个体工商户">个体工商户</Option>
              </Select>
            </Form.Item>

            <Form.Item
              label={<span>公司网址&nbsp;</span>}
              name="company_web"
              rules={[{ required: true, message: '请输入公司网址', whitespace: true }]}
            >
              <Input disabled={!can_edit} placeholder="请输入公司网址" />
            </Form.Item>
            <Form.Item
              label={<span>统一社会信用代码&nbsp;</span>}
              rules={[{ required: true, message: '请输入统一社会信用代码', whitespace: true }]}
              name="social_credit_code"
            >
              <Input disabled={!can_edit} placeholder="请输入统一社会信用代码" />
            </Form.Item>
            <Form.Item label={<span>合同有效期&nbsp;</span>} name="contract_date">
              <RangePicker
                onChange={this.onContractDateChange}
                defaultValue={contract_date}
                format={monthFormat}
              />
            </Form.Item>
            <Form.Item
              label={<span>银行名称&nbsp;</span>}
              rules={[{ required: true, message: '请输入银行名称', whitespace: true }]}
              name="brank_name"
            >
              <Input disabled={!can_edit} placeholder="请输入银行名称" />
            </Form.Item>
            <Form.Item
              label={<span>开户支行&nbsp;</span>}
              rules={[{ required: true, message: '请输入开户支行', whitespace: true }]}
              name="open_branch"
            >
              <Input disabled={!can_edit} placeholder="请输入开户支行" />
            </Form.Item>
            <Form.Item
              label={<span>银行卡号&nbsp;</span>}
              rules={[{ required: true, message: '请输入银行卡号', whitespace: true }]}
              name="card_number"
            >
              <Input disabled={!can_edit} placeholder="请输入银行卡号" />
            </Form.Item>
            <Form.Item label={<span>开户行所在地&nbsp;</span>} name="bank_areaids">
              <Cascader
                options={this.state.cityList}
                loadData={this.onCityLoadData}
                onChange={this.onChangeBankCity}
                placeholder="全国"
                defaultValue={company.bank_areaids}
                changeOnSelect
                disabled={!can_edit}
                getPopupContainer={triggerNode => triggerNode.parentNode}
              />
            </Form.Item>

            <Row>
              <Col span={8}>
                <Form.Item
                  label={<span>联系人姓名&nbsp;</span>}
                  labelCol={{ span: 10 }}
                  wrapperCol={{ span: 14 }}
                  name="link_person"
                  rules={[{ required: true, message: '请输入联系人姓名', whitespace: true }]}
                >
                  <Input disabled={!can_edit} />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  label={<span>手机号&nbsp;</span>}
                  labelCol={{ span: 10 }}
                  wrapperCol={{ span: 14 }}
                  name="LinkPhone"
                  type="number"
                  rules={[{ required: true, message: '请输入手机', whitespace: true }]}
                >
                  <Input disabled={!can_edit} />
                </Form.Item>
              </Col>
            </Row>
            <Row>
              <Col span={8}>
                <Form.Item
                  label={
                    <span>
                      <span style={{ color: 'red' }}>*</span> 邮箱&nbsp;
                    </span>
                  }
                  labelCol={{ span: 10 }}
                  wrapperCol={{ span: 14 }}
                  name="link_email"
                >
                  <Input disabled={!can_edit} />
                </Form.Item>
              </Col>
              <Col span={8} />
            </Row>

            <Row>
              <Col span={8}>
                <Form.Item
                  labelCol={{ span: 10 }}
                  wrapperCol={{ span: 14 }}
                  help="格式为pdf，小于8M，请按顺序合并合同"
                  label={
                    <span>
                      <span style={{ color: 'red' }}>*</span>爱采购合同&nbsp;
                    </span>
                  }
                  name="contract_file"
                  valuePropName="contract_file"
                  getValueFromEvent={this.normFile}
                >
                  <Upload
                    name="contract_file"
                    listType="text"
                    method="post"
                    maxCount={1}
                    multiple={false}
                    showUploadList
                    headers={Authorization}
                    data={file => ({
                      // data里存放的是接口的请求参数
                      file, // file 是当前正在上传的图片
                      purpose: 4,
                    })}
                    defaultFileList={
                      imageUrlcontract_file && [
                        {
                          url: imageUrlcontract_file,
                          name:
                            imageUrlcontract_file &&
                            imageUrlcontract_file.substr(
                              imageUrlcontract_file.lastIndexOf('/') + 1,
                              imageUrlcontract_file.length,
                            ),
                          status: 'done',
                          uid: '1',
                        },
                      ]
                    }
                    action={`${baseUrl}/v2/album/upload/0?purpose=4`}
                    beforeUpload={this.beforeUploadcontract_file}
                    onChange={this.handleChangeContractFile}
                    disabled={!can_edit}
                    onRemove={this.onRemoveContractFile}
                  >
                    {/* {imageUrlcontract_file ? <img className={styles.imagePic} src={imageUrlcontract_file} alt="avatar" /> : uploadButton} */}
                    <Button icon={<UploadOutlined />}>上传合同</Button>
                  </Upload>
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  labelCol={{ span: 10 }}
                  wrapperCol={{ span: 14 }}
                  label={
                    <span>
                      <span style={{ color: 'red' }}>*</span>营业执照&nbsp;
                    </span>
                  }
                  help="图片格式必须为jpg"
                  name="business_img"
                >
                  <ImgUpload
                    purpose={3}
                    onChange={this.onChangeLicense}
                  />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  label={
                    <span>
                      <span style={{ color: 'red' }}>*</span>LOGO&nbsp;
                    </span>
                  }
                  help="图片尺寸比例为170*170，格式必须为jpg"
                  labelCol={{ span: 10 }}
                  wrapperCol={{ span: 14 }}
                  name="company_logo"
                  valuePropName="company_logo"
                  getValueFromEvent={this.normFile}
                >
                  <ImgUpload
                    purpose={1}
                    onChange={this.onChangeLogo}
                    disabled={!can_edit}
                    imgUrl={imageUrlLogo}
                  />
                </Form.Item>
              </Col>
            </Row>
            <Space direction="vertical" />

            <Space direction="vertical" />
            <Form.Item {...tailFormItemLayout}>
              <Button type="primary" htmlType="submit" disabled={!can_edit}>
                确定
              </Button>
            </Form.Item>
          </Form>
        </div>
      </div>
    );
  }
}

export default connect(({ home, company, product, merchant }) => ({
  ...company,
  ...home,
  ...product,
  ...merchant,
}))(CompanyDetailForm);
