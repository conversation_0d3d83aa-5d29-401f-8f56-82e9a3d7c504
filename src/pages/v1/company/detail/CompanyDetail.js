// @flow
import React, { PureComponent } from 'react';
import { connect } from 'dva';
import {
  Alert,
  AutoComplete,
  Button,
  Cascader,
  Col,
  Form,
  Input,
  Layout,
  message,
  Modal,
  Radio,
  Row,
  Select,
  Space,
  DatePicker,
} from 'antd';
import { CloseOutlined } from '@ant-design/icons';
import 'antd/dist/antd.css';
import { baseUrl } from '@/configs/config';
import styles from './CompanyDetail.less';
import ImgUpload from '@/components/ImgUpload';
import dayjs from 'dayjs';
import moment from 'moment';
const { TextArea } = Input;
const { Option } = Select;
const AutoCompleteOption = AutoComplete.Option;

const { Header, Content } = Layout;

function getBase64(img, callback) {
  const reader = new FileReader();
  reader.addEventListener('load', () => callback(reader.result));
  reader.readAsDataURL(img);
}

class CompanyDetailForm extends PureComponent {
  // eslint-disable-next-line react/sort-comp
  formRef = React.createRef();
  state = {
    confirmDirty: false,
    company: {},
    fileList: [],
    imageUrlLicense: '',
    imageUrlLogo: '',
    imageUrlFront: '',
    imageUrlBack: '',
    imageUrlHand: '',
    isauditing: true,
    isChange: false,
    CateList: [],
    cityList: [],
    main_brand: '',
    short_name: '',
    address: '',
    company_type: '私营企业',
    working_model: '生产型',
  };

  // businessOptions = ["数据库管理及技术开发","计算机系统分析","计算机技术咨询","计算机软、硬件的技术开发、系统集成及销售","电子商务的技术开发","经营电子商务","计算机网络技术的研发"];
  workingModelOptions = ['生产型', '贸易型', '服务型', '政府', '其他机构'];
  companyTypeOptions = [
    '私营企业',
    '国有企业',
    '集体所有制企业',
    '合资企业',
    '外资企业',
    '股份企业',
    '个体经营',
    '事业单位',
    '社会团体',
    '个人',
    '其他',
  ];
  componentDidMount() {
    const that = this;
    const form = this.formRef.current;
    const { company = {}, cate, city } = this.props;
    this.props.dispatch({ type: 'merchant/queryMerchant', payload: {} });
    that.setState({
      CateList: cate,
      cityList: city,
    });

    this.props
      .dispatch({
        type: 'company/getMyCompany',
        payload: {},
      })
      .then(company => {
        that.setState(
          {
            company,
            isChange: !that.state.isChange,
          },
          () => {
            const {
              license,
              logo,
              frontimage,
              back_image,
              handimage,
              qrcode,
              isauditing,
              company_type,
              working_model,
            } = company;

            that.setState({ isauditing });
            if (qrcode) {
              if (qrcode.indexOf('http') !== -1) {
                that.setState({
                  imageUrlQrcode: qrcode,
                });
              } else {
                that.setState({
                  imageUrlQrcode: `${baseUrl}/${qrcode}`,
                });
              }
            }

            if (company_type !== undefined) {
              that.setState({
                company_type,
              });
            }
            if (working_model !== undefined) {
              that.setState({
                working_model,
              });
            }

            console.log(logo);
            console.log(license);
            console.log(frontimage);
            console.log(handimage);
            if (logo) {
              if (logo.indexOf('http') !== -1) {
                that.setState({
                  imageUrlLogo: logo,
                });
                that.formRef.current.setFieldsValue({
                  logo,
                });
              } else {
                that.setState({
                  imageUrlLogo: `${baseUrl}/${logo}`,
                });
              }
            }
            if (license) {
              if (license.indexOf('http') !== -1) {
                that.setState({
                  imageUrlLicense: license,
                });
                that.formRef.current.setFieldsValue({
                  license,
                });
              } else {
                that.setState({
                  imageUrlLicense: `${baseUrl}/${license}`,
                });
              }
            }

            if (frontimage) {
              if (frontimage.indexOf('http') !== -1) {
                that.setState({
                  imageUrlFront: frontimage,
                });
                that.formRef.current.setFieldsValue({
                  frontimage,
                });
              } else {
                that.setState({
                  imageUrlFront: `${baseUrl}/${frontimage}`,
                });
              }
            }
            if (back_image) {
              if (back_image.indexOf('http') !== -1) {
                that.setState({
                  imageUrlBack: back_image,
                });
                that.formRef.current.setFieldsValue({
                  back_image,
                });
              } else {
                that.setState({
                  imageUrlBack: `${baseUrl}/${back_image}`,
                });
              }
            }
            if (handimage) {
              if (handimage.indexOf('http') !== -1) {
                that.setState({
                  imageUrlHand: handimage,
                });
                that.formRef.current.setFieldsValue({
                  handimage,
                });
              } else {
                that.setState({
                  imageUrlHand: `${baseUrl}/${handimage}`,
                });
              }
            }
            form.resetFields();
          },
        );
      });

    // this.props.dispatch({
    //   type: 'product/getCateByPid', payload: { 'id': 0  }, callBack: function (result2) {
    //     that.setState({
    //       CateList: result2
    //     })

    //   }
    // });
  }

  handleSubmit = values => {
    const { cate } = values;
    const { features } = this.props;
    const {
      imageUrlLicense,
      imageUrlLogo,
      imageUrlFront,
      imageUrlBack,
      imageUrlHand,
      company_type,
      working_model,
    } = this.state;
    const that = this;
    let payload = {
      ...values,
      license: imageUrlLicense,
      logo: imageUrlLogo,
      frontimage: imageUrlFront,
      back_image: imageUrlBack,
      handimage: imageUrlHand,
    };
    if (payload.gender) {
      payload.gender = parseInt(payload.gender, 10);
    }
    if (features.validPeriod) {
      payload.valid_period =
        payload.valid_period_start.format('YYYY-MM-DD') +
        ' - ' +
        (payload.valid_period_end_type == '0'
          ? '永续经营'
          : payload.valid_period_end.format('YYYY-MM-DD'));
    }
    payload.reg_date = (payload.reg_date && payload.reg_date.format('YYYY-MM-DD')) || '';

    const name = values.name;
    if (name.length > 50) {
      Modal.error({
        content: '公司名称不超过50字',
        zIndex: 9999,
      });
      return;
    }

    const license = imageUrlLicense;
    const logo = imageUrlLogo;
    const imagefront = imageUrlFront;
    const imageback = imageUrlBack;
    const imagehand = imageUrlHand;
    const introduce = values.introduce;
    const email = values.email;

    const address = values.address;
    const main_brand = values.main_brand;
    const short_name = values.short_name;
    if (features.address) {
      if (address == null || address.length == 0) {
        Modal.error({
          content: '请填写公司详细地址',
          zIndex: 9999,
        });
        return;
      }
    }
    const idcardNo = values.id_card_no;
    if (features.idCardNo && !idcardNo) {
      Modal.error({
        content: '请填写身份证号码',
        zIndex: 9999,
      });
      return;
    }
    if (features.mainBrand) {
      if (main_brand == null || main_brand.length === 0) {
        Modal.error({
          content: '请填写公司主要品牌',
          zIndex: 9999,
        });
        return;
      }
      if (short_name == null || short_name.length === 0) {
        Modal.error({
          content: '请填写公司简称',
          zIndex: 9999,
        });
        return;
      }
      payload = { ...payload, company_type, working_model };
    }
    if (features.imageFront && !imagefront) {
      Modal.error({
        content: '请上传身份证正面照片',
        zIndex: 9999,
      });
      return;
    }

    if (features.imageBack && !imageback) {
      Modal.error({
        content: '请上传身份证国徽照片',
        zIndex: 9999,
      });
      return;
    }

    if (features.imageHand && !imagehand) {
      Modal.error({
        content: '请上传手执身份证照片',
        zIndex: 9999,
      });
      return;
    }

    const legal = values.legal;
    if (features.legal && legal.length > 20) {
      Modal.error({
        content: '法定代表人不超过20字',
        zIndex: 9999,
      });
      return;
    }

    if (license == null || license.length === 0) {
      Modal.error({
        content: '请上传营业执照',
        zIndex: 9999,
      });
      return;
    }
    if (logo == null || logo.length === 0) {
      Modal.error({
        content: '请上传公司LOGO',
        zIndex: 9999,
      });
      return;
    }
    if (introduce.length > 800 || introduce.length < 50) {
      Modal.error({
        content: '公司介绍字数在50-800字之间',
        zIndex: 9999,
      });
      return;
    }
    const contact_name = values.contact_name;
    if (contact_name.length > 20) {
      Modal.error({
        content: '联系人姓名不超过20字',
        zIndex: 9999,
      });
      return;
    }

    const areaids = values.areaids;
    if (areaids.length < 2) {
      Modal.error({
        content: '公司地址至少选到第二级',
        zIndex: 9999,
      });
      return;
    }

    const phone = values.phone;
    console.log(phone);
    if (phone.length !== 11) {
      Modal.error({
        content: '手机号只能填写11位的数字',
        zIndex: 9999,
      });
      return;
    }

    const qq = values.qq;
    if (qq == null || qq.length === 0) {
      // 输入不能为空
      Modal.error({
        content: 'QQ不能为空',
        zIndex: 9999,
      });
      return;
    }
    if (qq.length > 20) {
      Modal.error({
        content: '只能填写数字，不超过20字',
        zIndex: 9999,
      });
      return;
    }
    const main_product = values.main_product;
    if (main_product.length > 50) {
      Modal.error({
        content: '主营产品不超过50字',
        zIndex: 9999,
      });
      return;
    }

    const reg = /^\w+((.\w+)|(-\w+))@[A-Za-z0-9]+((.|-)[A-Za-z0-9]+).[A-Za-z0-9]+$/; // 正则表达式

    if (email == null || email.length === 0) {
      // 输入不能为空
      Modal.error({
        content: '邮箱不能为空',
        zIndex: 9999,
      });
      return;
    } else if (!reg.test(email)) {
      // 正则验证不通过，格式不对
      Modal.error({
        content: '邮箱格式不正确',
        zIndex: 9999,
      });
      return;
    }

    that.props
      .dispatch({
        type: 'product/getChildCateById',
        payload: { id: cate[cate.length - 1] },
      })
      .then(result => {
        if (result.length > 0) {
          Modal.error({
            content: '公司行业需选到最后一级',
            zIndex: 9999,
          });
        } else {
          that.props
            .dispatch({
              type: 'company/changeCompany',
              payload,
            })
            .then(data => {
              if (data.msg === 'success') {
                message.success('保存成功');
              } else {
                message.error(`保存失败,${data.msg}`);
              }
            })
            .catch(err => {
              message.error(`保存失败,${err.msg}`);
            });
        }
      })
      .catch(err => {
        message.error(`保存失败,${err.msg}`);
      });
  };

  handleConfirmBlur = e => {
    const value = e.target.value;
    this.setState({ confirmDirty: this.state.confirmDirty || !!value });
  };

  compareToFirstPassword = (rule, value, callback) => {
    const form = this.formRef.current;
    if (value && value !== form.getFieldValue('password')) {
      callback('Two passwords that you enter is inconsistent!');
    } else {
      callback();
    }
  };

  validateToNextPassword = (rule, value, callback) => {
    const form = this.formRef.current;
    if (value && this.state.confirmDirty) {
      form.validateFields(['confirm'], { force: true });
    }
    callback();
  };

  beforeUploadLogo = file => {
    const isJPG = file.type === 'image/jpeg' || file.type === 'image/png';
    console.log('before');
    if (!isJPG) {
      message.error({
        content: '图片上传格式必须为jpg/png',

        style: {
          zIndex: 9999,
        },
      });
    }
    const isLt2M = file.size / 1024 / 1024 < 1;
    console.log(isLt2M);
    if (!isLt2M) {
      message.error({
        content: '图片大小必须小于 1MB!',

        style: {
          zIndex: 9999,
        },
      });
    }
    if (isJPG && isLt2M) {
      this.setState({
        LogoBefor: true,
      });
    } else {
      this.setState({
        LogoBefor: false,
      });
    }
    return isJPG && isLt2M;
  };

  onChangeLicense = img => {
    this.setState({
      imageUrlLicense: img,
    });
    this.formRef.current.setFieldsValue({
      license: img,
    });
  };

  onChangeFront = img => {
    this.setState({
      imageUrlFront: img,
    });
    this.formRef.current.setFieldsValue({
      imagefront: img,
    });
  };

  onChangeBack = img => {
    this.setState({
      imageUrlBack: img,
    });
    this.formRef.current.setFieldsValue({
      image_back: img,
    });
  };

  onChangeHand = img => {
    this.setState({
      imageUrlHand: img,
    });
    this.formRef.current.setFieldsValue({
      imagehand: img,
    });
  };

  onChangeLogo = img => {
    this.setState({
      imageUrlLogo: img,
    });
    this.formRef.current.setFieldsValue({
      logo: img,
    });
  };

  handleChangeCompanyType = value => {
    this.setState({
      company_type: value,
    });
  };
  handleChangeBusiness = value => {
    this.setState({
      business: value,
    });
  };
  handleChangeWorkingModelStatus = value => {
    this.setState({
      working_model: value,
    });
  };
  onChange = (value, selectedOptions) => {
    const that = this;
    const cate = [];
    const { company } = this.props;
    selectedOptions.map(item => {
      cate.push(item.value);
    });
    this.setState({ cateChange: true });

    this.formRef.current.setFieldsValue({
      cate,
    });
    if (cate.length > 2) {
      that.props.dispatch({
        type: 'product/getChildCateById',
        payload: { id: cate[cate.length - 1] },
        callBack(result2) {
          if (result2.length == 0) {
            that.props.dispatch({
              type: 'product/getCateNotSupportPlatform',
              payload: { id: cate[cate.length - 1], cid: company.id },
              callBack(data) {
                if (data && data.length > 0) {
                  that.setState({
                    cateTip: data.toString(),
                  });
                } else {
                  that.setState({
                    cateTip: '',
                  });
                }
              },
            });
            that.props.dispatch({
              type: 'product/getCateProperty',
              payload: { id: cate[cate.length - 1] },
              callBack(result3) {
                that.setState({
                  propertiesChange: true,
                  properties: {},
                  cateProPerty: result3,
                });
              },
            });
          }
        },
      });
    }
  };
  onCateLoadData = selectedOptions => {
    console.log(selectedOptions);
    const { CateList, isUpdateView } = this.state;
    const that = this;
    let id = 0;
    selectedOptions.map(item => {
      console.log(item);
      id = item.value;
    });

    const targetOption = selectedOptions[selectedOptions.length - 1];
    targetOption.loading = true;
    this.props.dispatch({
      type: 'product/getCateByPid',
      payload: { id, cateLength: selectedOptions.length },
      callBack(result2) {
        for (let i = 0; i < CateList.length; i++) {
          if (CateList[i].value == targetOption.value) {
            if (result2 != undefined) {
              CateList[i].children = result2;
            } else {
              CateList[i].isLeaf = true;
            }
            CateList[i].loading = false;
            console.log(CateList);
            that.setState({
              cateChange: true,
              isUpdateView: !isUpdateView,
              CateList,
            });
            return;
          }
          console.log(CateList[i].children);
          if (CateList[i].children != undefined && CateList[i].children.length > 0) {
            const children = CateList[i].children;
            for (let j = 0; j < children.length; j++) {
              if (children[j].value == targetOption.value) {
                if (result2 != undefined) {
                  CateList[i].children[j].children = result2;
                } else {
                  CateList[i].children[j].isLeaf = true;
                }
                CateList[i].children[j].loading = false;
                console.log(CateList);
                that.setState({
                  cateChange: true,
                  isUpdateView: !isUpdateView,
                  CateList,
                });
                return;
              }
              if (
                CateList[i].children[j].children != undefined &&
                CateList[i].children[j].children.length > 0
              ) {
                const childrenz = CateList[i].children[j].children;
                for (let z = 0; z < childrenz.length; z++) {
                  if (childrenz[z].value == targetOption.value) {
                    if (result2 != undefined) {
                      CateList[i].children[j].children[z].children = result2;
                    } else {
                      CateList[i].children[j].children[z].isLeaf = true;
                    }
                    CateList[i].children[j].children[z].loading = false;
                    console.log(CateList);
                    that.setState({
                      cateChange: true,
                      isUpdateView: !isUpdateView,
                      CateList,
                    });
                    return;
                  }
                  if (
                    CateList[i].children[j].children[z].children != undefined &&
                    CateList[i].children[j].children[z].children.length > 0
                  ) {
                    const childrenk = CateList[i].children[j].children[z].children;
                    for (let k = 0; k < childrenk.length; k++) {
                      if (childrenk[k].value == targetOption.value) {
                        if (result2 != undefined) {
                          CateList[i].children[j].children[z].children[k].children = result2;
                        } else {
                          CateList[i].children[j].children[z].children[k].isLeaf = true;
                        }
                        CateList[i].children[j].children[z].children[k].loading = false;
                        console.log(CateList);
                        that.setState({
                          cateChange: true,
                          isUpdateView: !isUpdateView,
                          CateList,
                        });
                        return;
                      }
                    }
                  }
                }
              }
            }
          }
        }
      },
    });
  };

  onCityLoadData = selectedOptions => {
    console.log(selectedOptions);
    const { cityList, isUpdateView } = this.state;
    const that = this;
    let id = 0;
    selectedOptions.map(item => {
      console.log(item);
      id = item.value;
    });

    const targetOption = selectedOptions[selectedOptions.length - 1];
    targetOption.loading = true;
    this.props.dispatch({
      type: 'product/getCityByPid',
      payload: { id },
      callBack(result2) {
        for (let i = 0; i < cityList.length; i++) {
          if (cityList[i].value == targetOption.value) {
            if (result2 != undefined) {
              cityList[i].children = result2;
            } else {
              cityList[i].isLeaf = true;
            }
            cityList[i].loading = false;

            that.setState({
              areaidsChange: true,
              isUpdateView: !isUpdateView,
              cityList,
            });
            return;
          }
          console.log(cityList[i].children);
          if (cityList[i].children != undefined && cityList[i].children.length > 0) {
            const children = cityList[i].children;
            for (let j = 0; j < children.length; j++) {
              if (children[j].value == targetOption.value) {
                if (result2 != undefined) {
                  cityList[i].children[j].children = result2;
                } else {
                  cityList[i].children[j].isLeaf = true;
                }
                cityList[i].children[j].loading = false;
                console.log(cityList);
                that.setState({
                  areaidsChange: true,
                  isUpdateView: !isUpdateView,
                  cityList,
                });
                return;
              }
              if (
                cityList[i].children[j].children != undefined &&
                cityList[i].children[j].children.length > 0
              ) {
                const childrenz = cityList[i].children[j].children;
                for (let z = 0; z < childrenz.length; z++) {
                  if (childrenz[z].value == targetOption.value) {
                    if (result2 != undefined) {
                      cityList[i].children[j].children[z].children = result2;
                    } else {
                      cityList[i].children[j].children[z].isLeaf = true;
                    }
                    cityList[i].children[j].children[z].loading = false;
                    console.log(cityList);
                    that.setState({
                      areaidsChange: true,
                      isUpdateView: !isUpdateView,
                      cityList,
                    });
                    return;
                  }
                }
              }
            }
          }
        }
      },
    });
  };

  onChangeCity = (value, selectedOptions) => {
    console.log('ddd');
    const that = this;
    const areaids = [];
    const { company } = this.props;
    selectedOptions.map(item => {
      that.props.dispatch({
        type: 'product/getCityNotSupportPlatform',
        payload: { id: item.value, cid: company.id },
        callBack(data) {
          if (data && data.length > 0) {
            that.setState({
              areaidsTip: data.toString(),
            });
          } else {
            that.setState({
              areaidsTip: '',
            });
          }
        },
      });

      areaids.push(item.value);
    });

    this.formRef.current.setFieldsValue({
      areaids,
    });
    this.setState({
      areaidsChange: true,
      areaids,
    });
  };

  onChangeGender = e => {
    this.formRef.current.setFieldsValue({
      gender: e.target.value,
    });
  };

  onRegDateChanged = e => {
    this.formRef.current.setFieldsValue({
      reg_date: e,
    });
  };

  filter = (inputValue, path) => {
    return path.some(option => option.label.toLowerCase().indexOf(inputValue.toLowerCase()) > -1);
  };

  render() {
    const { imageUrlLicense, imageUrlLogo, imageUrlFront, imageUrlBack, imageUrlHand } = this.state;
    console.log(imageUrlLicense, imageUrlLogo);
    const formItemLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 14 },
    };
    const tailFormItemLayout = {
      wrapperCol: {
        xs: {
          span: 24,
          offset: 0,
        },
        sm: {
          span: 16,
          offset: 8,
        },
      },
    };
    const { company = {} } = this.state;
    const { can_edit: canEdit = false, reason } = company;
    const { features } = this.props;
    console.log(company);
    return (
      <div style={{ background: '#fff', padding: 24 }}>
        <div>
          <div className={styles.titleName}>公司资料</div>
          {!canEdit && <Alert type="info" description={reason} closable banner />}

          <Form
            ref={this.formRef}
            className={styles.tableView}
            {...formItemLayout}
            onFinish={this.handleSubmit}
            initialValues={{
              name: company.name,
              address: company.address,
              license: this.state.imageUrlLicense,
              logo: this.state.imageUrlLogo,
              imagefront: this.state.imageUrlFront,
              imagehand: this.state.imageUrlHand,
              qrcode: company.qrcode,
              introduce: company.introduce,
              contact_name: company.contact_name,
              cate: company && company.cate,
              areaids: company.areaids,
              gender: company.gender ? company.gender + '' : '1',
              id_card_no: company.id_card_no,
              phone: company.phone ? company.phone[0] : '',
              qq: company.qq,
              main_product: company.main_product,
              reg_no: company.reg_no,
              legal: company.legal,
              corp_type: company.corp_type,
              reg_addr: company.reg_addr,
              reg_money: company.reg_money,
              reg_date: company.reg_date ? moment(company.reg_date, 'YYYY-MM-DD') : '',
              valid_period_start: company.valid_period
                ? moment(company.valid_period.split(' - ')[0], 'YYYY-MM-DD')
                : '',
              valid_period_end: company.valid_period
                ? company.valid_period.indexOf('永续经营') == -1
                  ? moment(company.valid_period.split(' - ')[1], 'YYYY-MM-DD')
                  : ''
                : '',
              valid_period_end_type:
                company.valid_period && company.valid_period.indexOf('永续经营') > -1 ? '0' : '1',
              business: company.business,
              reg_authority: company.reg_authority,
              email: company.email,
              main_brand: company.main_brand,
              company_type: company.company_type,
              short_name: company.short_name,
              working_model: company.working_model,
            }}
          >
            <Form.Item
              label={<span className={styles.lableName}>公司名称&nbsp;</span>}
              help="审核通过后不可修改请谨慎填写"
              name="name"
              rules={[{ required: true, message: '请输入公司名称', whitespace: true }]}
            >
              <Input placeholder="请输入公司名称" disabled={!canEdit} />
            </Form.Item>
            <Form.Item
              label={
                <span>
                  <span style={{ color: 'red' }}>*</span> 公司地址&nbsp;
                </span>
              }
              name="areaids"
            >
              <Cascader
                options={this.state.cityList}
                loadData={this.onCityLoadData}
                onChange={this.onChangeCity}
                placeholder="全国"
                defaultValue={company.area_names}
                changeOnSelect
                disabled={!canEdit}
                getPopupContainer={triggerNode => triggerNode.parentNode}
              />
              {this.state.areaidsTip && this.state.areaidsTip.length > 0 && (
                <Alert
                  type="warning"
                  description={`当前选择城市无法发布到以下平台：${this.state.areaidsTip}`}
                  closable
                  banner
                />
              )}
            </Form.Item>

            <Form.Item
              label={
                <span>
                  <span style={{ color: 'red' }}>*</span> 公司行业&nbsp;
                </span>
              }
              name="cate"
            >
              <Cascader
                options={this.state.CateList}
                loadData={this.onCateLoadData}
                onChange={this.onChange}
                changeOnSelect
                defaultValue={company.cate}
                placeholder="分类需要选择到最后一级"
                disabled={!canEdit}
                showSearch={this.filter}
                getPopupContainer={triggerNode => triggerNode.parentNode}
              />
              {this.state.cateTip && this.state.cateTip.length > 0 && (
                <Alert
                  type="warning"
                  description={`当前选择分类无法发布到以下平台：${this.state.cateTip}`}
                  closable
                  banner
                />
              )}
            </Form.Item>

            <Form.Item
              label={<span>主营产品&nbsp;</span>}
              name="main_product"
              rules={[{ required: true, message: '例如：挖掘机、装载机', whitespace: true }]}
            >
              <Input disabled={!canEdit} />
            </Form.Item>
            <Form.Item
              label={<span>公司介绍&nbsp;</span>}
              help="公司介绍在50-800字。"
              name="introduce"
              rules={[{ required: true, message: '请输入介绍', whitespace: true }]}
            >
              <TextArea rows={6} disabled={!canEdit} />
            </Form.Item>

            <Row>
              <Col span={8}>
                <Form.Item
                  labelCol={{ span: 10 }}
                  wrapperCol={{ span: 14 }}
                  label={
                    <span>
                      <span style={{ color: 'red' }}>*</span>营业执照&nbsp;
                    </span>
                  }
                  name="license"
                  valuePropName="license"
                  getValueFromEvent={this.normFile}
                >
                  <ImgUpload
                    purpose={1}
                    onChange={this.onChangeLicense}
                    disabled={!canEdit}
                    imgUrl={imageUrlLicense}
                  />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  label={
                    <span>
                      <span style={{ color: 'red' }}>*</span>LOGO&nbsp;
                    </span>
                  }
                  labelCol={{ span: 10 }}
                  wrapperCol={{ span: 14 }}
                  name="logo"
                  valuePropName="logo"
                  getValueFromEvent={this.normFile}
                >
                  <ImgUpload
                    purpose={0}
                    onChange={this.onChangeLogo}
                    disabled={!canEdit}
                    imgUrl={imageUrlLogo}
                  />
                </Form.Item>
              </Col>
            </Row>
            {features.imageHand && (
              <Row>
                {features.imageFront && (
                  <Col span={8}>
                    <Form.Item
                      labelCol={{ span: 10 }}
                      wrapperCol={{ span: 14 }}
                      label={
                        <span>
                          <span style={{ color: 'red' }}>*</span>联系人身份证人像面&nbsp;
                        </span>
                      }
                      name="imagefront"
                      valuePropName="imagefront"
                      getValueFromEvent={this.normFile}
                    >
                      <ImgUpload
                        purpose={5}
                        onChange={this.onChangeFront}
                        disabled={!canEdit}
                        imgUrl={imageUrlFront}
                      />
                    </Form.Item>
                  </Col>
                )}
                {features.imageBack && (
                  <Col span={8}>
                    <Form.Item
                      labelCol={{ span: 10 }}
                      wrapperCol={{ span: 14 }}
                      label={
                        <span>
                          <span style={{ color: 'red' }}>*</span>身份证反面&nbsp;
                        </span>
                      }
                      name="back_image"
                      valuePropName="back_image"
                      getValueFromEvent={this.normFile}
                    >
                      <ImgUpload
                        purpose={5}
                        onChange={this.onChangeBack}
                        disabled={!canEdit}
                        imgUrl={imageUrlBack}
                      />
                    </Form.Item>
                  </Col>
                )}
                {features.imageHand && (
                  <Col span={8}>
                    <Form.Item
                      label={
                        <span>
                          <span style={{ color: 'red' }}>*</span>联系人手持身份证照片&nbsp;
                        </span>
                      }
                      labelCol={{ span: 10 }}
                      wrapperCol={{ span: 14 }}
                      name="imagehand"
                      valuePropName="imagehand"
                      getValueFromEvent={this.normFile}
                    >
                      <ImgUpload
                        purpose={6}
                        onChange={this.onChangeHand}
                        disabled={!canEdit}
                        imgUrl={imageUrlHand}
                      />
                    </Form.Item>
                  </Col>
                )}
              </Row>
            )}
            <Space direction="vertical" />
            <Row>
              <Col span={8}>
                <Form.Item
                  label={<span>联系人姓名&nbsp;</span>}
                  labelCol={{ span: 10 }}
                  wrapperCol={{ span: 14 }}
                  name="contact_name"
                  rules={[{ required: true, message: '请输入联系人姓名', whitespace: true }]}
                >
                  <Input disabled={!canEdit} />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  label={<span>手机号&nbsp;</span>}
                  labelCol={{ span: 10 }}
                  wrapperCol={{ span: 14 }}
                  name="phone"
                  rules={[{ required: true, message: '请输入手机', whitespace: true }]}
                >
                  <Input disabled={!canEdit} />
                </Form.Item>
              </Col>
            </Row>
            {(features.gender || features.idCardNo) && (
              <Row>
                {features.gender && (
                  <Col span={8}>
                    <Form.Item
                      label={<span>性别&nbsp;</span>}
                      labelCol={{ span: 10 }}
                      wrapperCol={{ span: 14 }}
                      name="gender"
                      rules={[{ required: true, message: '请选择性别', whitespace: true }]}
                    >
                      <Radio.Group disabled={!canEdit} onChange={this.onChangeGender}>
                        <Radio value="1">男</Radio>
                        <Radio value="2">女</Radio>
                      </Radio.Group>
                    </Form.Item>
                  </Col>
                )}
                {features.idCardNo && (
                  <Col span={8}>
                    <Form.Item
                      label={
                        <span>
                          <span style={{ color: 'red' }}>*</span>身份证号码&nbsp;
                        </span>
                      }
                      labelCol={{ span: 10 }}
                      wrapperCol={{ span: 14 }}
                      name="id_card_no"
                    >
                      <Input disabled={!canEdit} placeholder="请输入身份证号码" />
                    </Form.Item>
                  </Col>
                )}
              </Row>
            )}
            <Row>
              <Col span={8}>
                <Form.Item
                  label={
                    <span>
                      <span style={{ color: 'red' }}>*</span> 邮箱&nbsp;
                    </span>
                  }
                  labelCol={{ span: 10 }}
                  wrapperCol={{ span: 14 }}
                  name="email"
                >
                  <Input disabled={!canEdit} />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  label={
                    <span>
                      <span style={{ color: 'red' }}>*</span> QQ&nbsp;
                    </span>
                  }
                  labelCol={{ span: 10 }}
                  wrapperCol={{ span: 14 }}
                  name="qq"
                >
                  <Input disabled={!canEdit} />
                </Form.Item>
              </Col>
            </Row>

            {features.address && (
              <Form.Item
                label={
                  <span>
                    <span style={{ color: 'red' }}>*</span> 公司详细地址&nbsp;
                  </span>
                }
                name="address"
              >
                <Input disabled={!canEdit} placeholder="请输入公司详细地址" />
              </Form.Item>
            )}
            {(features.legal || features.regNo) && (
              <Row>
                {features.legal && (
                  <Col span={8}>
                    <Form.Item
                      label={<span>公司法人&nbsp;</span>}
                      labelCol={{ span: 10 }}
                      wrapperCol={{ span: 14 }}
                      rules={[{ required: true, message: '请输入公司法人', whitespace: true }]}
                      name="legal"
                    >
                      <Input disabled={!canEdit} placeholder="请输入公司法人" />
                    </Form.Item>
                  </Col>
                )}
                {features.regNo && (
                  <Col span={8}>
                    <Form.Item
                      label={<span>统一社会信用代码&nbsp;</span>}
                      labelCol={{ span: 10 }}
                      wrapperCol={{ span: 14 }}
                      rules={[
                        { required: true, message: '请输入统一社会信用代码', whitespace: true },
                      ]}
                      name="reg_no"
                    >
                      <Input disabled={!canEdit} placeholder="请输入统一社会信用代码" />
                    </Form.Item>
                  </Col>
                )}
              </Row>
            )}
            {(features.regAuthority || features.regDate || features.regAddress) && (
              <Row>
                {features.regAuthority && (
                  <Col span={8}>
                    <Form.Item
                      label={<span>发照机关&nbsp;</span>}
                      labelCol={{ span: 10 }}
                      wrapperCol={{ span: 14 }}
                      rules={[{ required: true, message: '请输入发照机关', whitespace: true }]}
                      name="reg_authority"
                    >
                      <Input disabled={!canEdit} placeholder="请输入发照机关" />
                    </Form.Item>
                  </Col>
                )}

                {features.regAddress && (
                  <Col span={8}>
                    <Form.Item
                      label={<span>注册地址&nbsp;</span>}
                      labelCol={{ span: 10 }}
                      wrapperCol={{ span: 14 }}
                      rules={[{ required: true, message: '请输入注册地址', whitespace: true }]}
                      name="reg_addr"
                    >
                      <Input disabled={!canEdit} placeholder="请输入注册地址" />
                    </Form.Item>
                  </Col>
                )}
                {features.regDate && (
                  <Col span={8}>
                    <Form.Item
                      label={<span>注册时间（成立时间)&nbsp;</span>}
                      labelCol={{ span: 14 }}
                      wrapperCol={{ span: 28 }}
                      rules={[
                        {
                          required: true,
                          message: '请输入注册时间',
                          whitespace: true,
                          transform: val => val.format('YYYY-MM-DD'),
                        },
                      ]}
                      name="reg_date"
                    >
                      <DatePicker
                        disabled={!canEdit}
                        format="YYYY-MM-DD"
                        onChange={this.onRegDateChanged}
                      ></DatePicker>
                    </Form.Item>
                  </Col>
                )}
              </Row>
            )}
            {features.validPeriod && (
              <Row>
                <Col span={8}>
                  <Form.Item
                    label={<span>营业期限开始时间&nbsp;</span>}
                    labelCol={{ span: 14 }}
                    wrapperCol={{ span: 28 }}
                    rules={[
                      {
                        required: true,
                        message: '请输入营业期限开始时间',
                        whitespace: true,
                        transform: val => val.format('YYYY-MM-DD'),
                      },
                    ]}
                    name="valid_period_start"
                  >
                    <DatePicker disabled={!canEdit} format="YYYY-MM-DD"></DatePicker>
                  </Form.Item>
                </Col>
                <Col span={16}>
                  <Form.Item
                    label={<span>营业期限结束时间&nbsp;</span>}
                    labelCol={{ span: 8 }}
                    wrapperCol={{ span: 18 }}
                    rules={[
                      { required: true, message: '请输入营业期限结束时间', whitespace: true },
                    ]}
                    name="valid_period_end_type"
                  >
                    <Radio.Group disabled={!canEdit}>
                      <Radio value="0">永续经营</Radio>
                      <Radio value="1">
                        <Form.Item
                          name="valid_period_end"
                          labelCol={{ span: 28 }}
                          wrapperCol={{ span: 28 }}
                        >
                          <DatePicker disabled={!canEdit} format="YYYY-MM-DD"></DatePicker>
                        </Form.Item>
                      </Radio>
                    </Radio.Group>
                  </Form.Item>
                </Col>
              </Row>
            )}

            {features.shortName && (
              <Row>
                <Col span={8}>
                  <Form.Item
                    label={<span>公司简称&nbsp;</span>}
                    labelCol={{ span: 10 }}
                    wrapperCol={{ span: 14 }}
                    name="short_name"
                  >
                    <Input disabled={!canEdit} placeholder="请输入公司简称" />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    label={<span>主要品牌&nbsp;</span>}
                    labelCol={{ span: 10 }}
                    wrapperCol={{ span: 14 }}
                    name="main_brand"
                  >
                    <Input disabled={!canEdit} placeholder="请输入主要品牌" />
                  </Form.Item>
                </Col>
              </Row>
            )}
            {features.companyType && (
              <Row>
                <Col span={8}>
                  <Form.Item
                    label={<span>公司性质&nbsp;</span>}
                    labelCol={{ span: 10 }}
                    wrapperCol={{ span: 14 }}
                    name="company_type"
                  >
                    <Select
                      defaultValue={this.companyTypeOptions[0]}
                      style={{ width: 120 }}
                      value={this.state.company_type}
                      onChange={this.handleChangeCompanyType}
                      getPopupContainer={triggerNode => triggerNode.parentNode}
                    >
                      {this.companyTypeOptions.map((item, idx) => {
                        return <Option value={item}>{item}</Option>;
                      })}
                    </Select>
                  </Form.Item>
                </Col>

                <Col span={8}>
                  <Form.Item
                    label={<span>经营范围&nbsp;</span>}
                    labelCol={{ span: 10 }}
                    wrapperCol={{ span: 14 }}
                    name="business"
                  >
                    <Input disabled={!canEdit} placeholder="请输入经营范围" />
                  </Form.Item>
                </Col>

                <Col span={8}>
                  <Form.Item
                    label={<span>经营模式&nbsp;</span>}
                    labelCol={{ span: 10 }}
                    wrapperCol={{ span: 14 }}
                    name="working_model"
                  >
                    <Select
                      defaultValue={this.workingModelOptions[0]}
                      style={{ width: 120 }}
                      value={this.state.working_model}
                      onChange={this.handleChangeWorkingModelStatus}
                      getPopupContainer={triggerNode => triggerNode.parentNode}
                    >
                      {this.workingModelOptions.map((item, idx) => {
                        return <Option value={item}>{item}</Option>;
                      })}
                    </Select>
                  </Form.Item>
                </Col>
              </Row>
            )}

            <Space direction="vertical" />
            <Form.Item {...tailFormItemLayout}>
              <Button type="primary" htmlType="submit" disabled={!canEdit}>
                确定
              </Button>
            </Form.Item>
          </Form>
        </div>
      </div>
    );
  }
}

export default connect(({ home, company, product, merchant }) => ({
  ...company,
  ...home,
  ...product,
  ...merchant,
}))(CompanyDetailForm);
