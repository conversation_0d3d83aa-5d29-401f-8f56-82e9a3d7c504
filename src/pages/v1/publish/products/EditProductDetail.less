/* 遮罩层 */
.mask{
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  background: #000;
  z-index: 0;
  opacity: 0.5;
}

.tableView{
  width: 100%;
  height: 100%;
  overflow:scroll;
  overflow-x:hidden;
  padding-top: 10px;
}
/* 弹出层 */
.modalDlg{
  width: 80%;
  height: 90%;
  position: fixed;
  top: 20px;
  left: 0;
  right: 0;
  z-index: 3;
  margin: 0 auto;
  background-color: #fff;
  border-radius:5px;
  display: flex;
  flex-direction: column;
  padding: 10px;
 
  :global {
  
    .ant-form-item-label{
        width: 80px;
    }
     
    }
}
.selectItem{
  :global {
  
    .ant-form-item-label{
        width: 60px;
    }
     
    }
}
.itemView{
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: start;
  font-size: 16px;
  color: #000000;
  margin: 10px 10px ;
  margin-top: 20px;
}
  .titleName{
      font-size: 18px;
      color: #666666;
  }
  .closeIcon{
     position: fixed;
     top: 25px;
     right: 12%;
    font-size: 18px;
    color: #666666;
}
.tipText{
  color: red;
  font-size: 16px;
}

.keywordResult{
  margin-top: 15px;
  height: 20vh;
 
  flex-direction: row;
  display: flex;
  border:1px solid #E6E6E6;
  padding:10px;
  flex-wrap: wrap;
  word-wrap:break-word; 
  word-break:break-all; 
  overflow:scroll;
  overflow-x:hidden;
}
.AddItem{
  display: flex;
  width: 100px;
  height: 40px;
  flex-direction: row;
  justify-content: space-between;
  margin-top: 2px;
  align-items: center;
  border:1px solid #E6E6E6;
  padding: 3px 10px;
  margin-left: 5px;
  border-radius: 5px;

}

.limitItem {
  max-width: 60px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap; //文本不换行，这样超出一行的部分被截取，显示...
  
  }

.iconDelete{
  margin-left: 5px;
  margin-right: 5px;
}
.keywordItem{
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  margin-top: 5px;
  border:1px solid #E6E6E6;
  padding: 3px 10px;
  border-radius: 5px;
  margin-left: 5px;
}
.iconDelete{
  margin-left: 5px;
  margin-right: 5px;
}
.picDiv{
  display: flex;
  flex-direction: row;
  
}
.keywordView{
  margin-top: 15px;
  width: 100vh;
  height:10vh;
  flex-direction: row;
  display: flex;
  border:1px solid #E6E6E6;
  padding:10px;
  justify-content: left;
}
.spin{
  width: 80%;
  height: 90%;
  position: fixed;
  text-align: center;
  top: 20px;
  left: 0;
  right: 0;
  z-index: 3;
  margin: 0 auto;
  padding-top: 50px;
  justify-content: center;
  background: #000;
  opacity: 0.3;
}