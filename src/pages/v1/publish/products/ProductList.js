// @flow
import React, { PureComponent } from 'react';
import { connect } from 'dva';
import {
  Form,
  Table,
  Divider,
  Select,
  Row,
  Col,
  Button,
  AutoComplete,
  Dropdown,
  Menu,
  Layout,
  message,
  Modal,
  Tabs,
  ConfigProvider,
} from 'antd';
import zh_CN from 'antd/lib/locale-provider/zh_CN';
import { CloseOutlined, CaretDownOutlined } from '@ant-design/icons';
import 'antd/dist/antd.css';
import KeyWord from '@/components/KeyWord';
import styles from './ProductList.less';
import UpdateForm from '@/components/UpdateProductShop';
import router from "umi/router";

const { confirm } = Modal;
const { TabPane } = Tabs;
const { Header, Content } = Layout;

class ProductListForm extends PureComponent {
  state = {
    confirmDirty: false,
    autoCompleteResult: [],
    isShowKeyWord: false,
    addProductId: 0,
    addProductName: '',
    brand: '',
    updateModalVisible: false,
  };

  showAddProduct = () => {
    const { maxProducts = 30, productSize } = this.props;
    console.log(maxProducts);
    console.log(productSize);
    console.log(this.props);
    this.props.dispatch({ type: 'product/save', payload: { productInfo: {} } });
    if (productSize < maxProducts) {
      this.props.addProduct();
    } else {
      message.warn('超出添加产品上限，请联系管理员');
    }
  };
  componentDidMount() {
    const that = this;
    this.props.dispatch({
      type: 'product/getProductStat',
      payload: {},
      callBack(result2) {
        console.log(result2);
        that.setState({
          isUpdate: true,
        });
        that.props.dispatch({ type: 'product/getProductList', payload: {} });
      },
    });
  }
  getProductById = data => {
    const { number, opt } = data;
    const that = this;
    this.props.dispatch({
      type: 'product/getProductById',
      payload: { product_id: number },
      callBack(result2) {
        that.props.show();
      },
    });
  };

  getProductOnlyShow = data => {
    const { number } = data;
    router.push(`/publish/products/${number}`);
  };


  showKeyWord = data => {
    const that = this;
    const { number } = data;
    this.props.dispatch({
      type: 'product/getProductById',
      payload: { product_id: number },
      callBack(result) {
        const { name, word = [], brand } = result;

        that.setState({
          addProductName: name,
          isShowKeyWord: true,
          brand,
          itemList: word,
          type: 'keyword',
          addProductId: number,
          isAddKeyword: true,
        });
      },
    });
  };
  consoleKeyWord = () => {
    this.setState({
      isShowKeyWord: false,
    });
  };

  addKeyWordList = keyWordList => {
    const { addProductId } = this.state;
    console.log(addProductId);
    console.log(keyWordList);
    this.setState({
      isShowKeyWord: false,
    });
    this.props.dispatch({
      type: 'product/addTitleProduct',
      payload: { product_id: addProductId, keyWordList },
      callBack(result) {
        const { msg, data } = result;
        if (msg == 'success') {
          message.success('添加成功');
        } else {
          message.success('添加失败');
        }
      },
    });
  };
  optionProduct = data => {
    const that = this;
    const { number, opt, brand, name } = data;
    if (opt == 6) {
      this.showKeyWord({ name, number, brand });
    } else if (opt == 5) {
      this.props.dispatch({
        type: 'product/copyProduct',
        payload: { product_id: number, opt: parseInt(opt) },
        callBack(result) {
          const { msg, data } = result;
          if (msg == 'success') {
            message.success('复制成功');
          } else {
            message.success('添加失败');
          }
        },
      });
    } else if (parseInt(opt) == 4) {
      confirm({
        title: '删除产品',
        content: '删除后无法恢复，确定删除?',
        zIndex: 9999,
        cancelText: '取消',
        okText: '确定',
        onOk() {
          that.props.dispatch({
            type: 'product/optionProductById',
            payload: { product_id: number, opt: parseInt(opt) },
            callBack(optMsg) {
              if (optMsg == 'success') {
                switch (parseInt(opt)) {
                  case 1:
                    message.success('提交审核成功');
                    break;
                  case 2:
                    message.success('开始推广成功');
                    break;
                  case 3:
                    message.success('取消推广成功');
                    break;
                  case 4:
                    message.success('删除成功');
                    break;
                }
              } else {
                message.error(optMsg);
              }
            },
          });
        },
        onCancel() {},
      });
    } else if (parseInt(opt) == 2) {
      confirm({
        title: '开始推广',
        content: '产品推广后，建议不要经常修改资料，否则会减少生成的信息量。',
        zIndex: 9999,
        cancelText: '取消',
        okText: '开始推广',
        onOk() {
          that.props.dispatch({
            type: 'product/optionProductById',
            payload: { product_id: number, opt: parseInt(opt) },
            callBack(optMsg) {
              if (optMsg == 'success') {
                switch (parseInt(opt)) {
                  case 1:
                    message.success('提交审核成功');
                    break;
                  case 2:
                    message.success('开始推广成功');
                    break;
                  case 3:
                    message.success('取消推广成功');
                    break;
                  case 4:
                    message.success('删除成功');
                    break;
                }
              } else {
                message.error(optMsg);
              }
            },
          });
        },
        onCancel() {},
      });
    } else {
      this.props.dispatch({
        type: 'product/optionProductById',
        payload: { product_id: number, opt: parseInt(opt) },
        callBack(optMsg) {
          if (optMsg == 'success') {
            switch (parseInt(opt)) {
              case 1:
                message.success('提交审核成功');
                break;
              case 2:
                message.success('开始推广成功');
                break;
              case 3:
                message.success('取消推广成功');
                break;
              case 4:
                message.success('删除成功');
                break;
            }
          } else {
            message.error(optMsg);
          }
        },
      });
    }
  };
  handleUpdateModalVisible = (flag, record) => {
    console.log(`handleUpdateModalVisible:${!!flag}`);
    console.log(`record:${record}`);
    this.setState({
      updateModalVisible: !!flag,
      stepFormValues: record || {},
    });
  };

  handleUpdate = fields => {
    console.log(fields);
    const { dispatch } = this.props;
    const that = this;
    const { pagination } = this.props;

    dispatch({
      type: 'product/updateProductShopById',
      payload: {
        ...fields,
      },
      callBack({ msg }) {
        console.log(msg);
        if (msg == 'success') {
          console.log(msg);
          message.success('配置成功');
          that.props.dispatch({ type: 'product/getProductList', payload: { pagination } });
        } else {
          message.error(msg);
        }
      },
    });

    this.handleUpdateModalVisible();
  };
  pageChange = currentPage => {
    this.props.dispatch({ type: 'product/getProductList', payload: { currentPage } });
  };
  changeTable = currentPage => {
    let status = 0;

    switch (currentPage) {
      case 0:
        status = 0;
        break;
      case 1:
        status = 4;
        break;
      case 2:
        status = 0;
        break;
      case 3:
        status = 0;
        break;
      case 4:
        status = 0;
        break;
      case 5:
        status = 0;
        break;
      case 6:
        status = 0;
        break;
    }
    this.props.dispatch({ type: 'product/getProductList', payload: { status } });
  };
  render() {
    const updateMethods = {
      handleUpdateModalVisible: this.handleUpdateModalVisible,
      handleUpdate: this.handleUpdate,
    };
    // In the fifth row, other columns are merged into first column
    // by setting it's colSpan to be 0
    const renderContent = (value, row, index) => {
      const obj = {
        children: value,
        props: {},
      };

      return obj;
    };

    const columns = [
      {
        title: '序号',
        dataIndex: 'sNumber',
        render: renderContent,
      },
      {
        title: '产品名称',
        dataIndex: 'name',
        render: renderContent,
      },
      {
        title: '添加/更新日期',
        dataIndex: 'time',
        render: (text, record) => {
          const { time, updateTime } = record;
          return (
            <div>
              <span>{time}</span>
              <br />
              <span style={{ color: '#A6A6A6' }}>{updateTime}</span>
            </div>
          );
        },
        width: 200,
      },
      {
        title: '产品状态',
        dataIndex: 'status',
        render: renderContent,
        width: 150,
      },
      {
        title: '操作',
        key: 'action',
        width: 200,
        render: (text, record) => {
          console.log('render', text, record);
          const { number, status, statusNumber, name, brand } = record;
          const menu = (
            <Menu>
              {statusNumber == 3 && (
                <Menu.Item key="2">
                  <div onClick={() => this.optionProduct({ number, opt: 2 })}>开始推广</div>
                </Menu.Item>
              )}

              {(statusNumber == 1 ||
                statusNumber == 5 ||
                statusNumber == 7 ||
                statusNumber == 3 ||
                statusNumber == 5 ||
                statusNumber == 6 ||
                statusNumber == 8) && (
                <Menu.Item key="0">
                  <div onClick={() => this.getProductById({ number })}>编辑</div>
                </Menu.Item>
              )}
              {(statusNumber == 1 ||
                statusNumber == 5 ||
                statusNumber == 7 ||
                statusNumber == 3 ||
                statusNumber == 5 ||
                statusNumber == 6 ||
                statusNumber == 8) && (
                <Menu.Item key="1">
                  <div
                    onClick={() =>
                      this.handleUpdateModalVisible(!this.state.updateModalVisible, record)
                    }
                  >
                    发布平台
                  </div>
                </Menu.Item>
              )}
              {/* <Menu.Item key="1">
            <div onClick={( )=>this.optionProduct({number,opt:5})}>复制</div>
          </Menu.Item>
          {(statusNumber==1||statusNumber==2||statusNumber==5)&&
           <Menu.Item key="2">
           <div onClick={( )=>this.optionProduct({number,opt:1})}>提交审核</div>
         </Menu.Item>}  */}
              {(statusNumber == 1 || statusNumber == 2 || statusNumber == 5) && (
                <Menu.Item key="3">
                  <div onClick={() => this.optionProduct({ number, opt: 4 })}>删除</div>
                </Menu.Item>
              )}
              {statusNumber == 4 && (
                <Menu.Item key="4">
                  <div onClick={() => this.optionProduct({ number, opt: 3 })}>暂停推广</div>
                </Menu.Item>
              )}

              {(statusNumber == 6 || statusNumber == 8) && (
                <Menu.Item key="5">
                  <div onClick={() => this.optionProduct({ number, opt: 6, name, brand })}>
                    添加关键字
                  </div>
                </Menu.Item>
              )}
            </Menu>
          );
          return (
            <span>
              <Button type="link" onClick={() => this.getProductOnlyShow({ number })}>
                查看
              </Button>

              <Divider type="vertical" />
              <Dropdown
                overlay={menu}
                trigger={['click']}
                getPopupContainer={triggerNode => triggerNode.parentNode}
              >
                <Button type="link">
                  操作
                  <CaretDownOutlined />
                </Button>
              </Dropdown>
            </span>
          );
        },
      },
    ];

    const { productList, pagination } = this.props;
    const paginationProps = {
      ...pagination,

      onChange: this.pageChange,
    };
    console.log(pagination);
    const { addProductName, itemList, brand, isAddKeyword, productState = {} } = this.state;

    return (
      <div style={{ background: '#fff', padding: 24 }}>
        <div>
          <div className={styles.titleName}>产品列表</div>
          <div className={styles.itemView}>
            <div style={{ marginLeft: '5px', marginBottom: '5px' }}>
              {' '}
              <Button type="primary" onClick={this.showAddProduct}>
                新增产品
              </Button>
            </div>
          </div>
          <div className={styles.tableView}>
            <ConfigProvider locale={zh_CN}>
              <Table
                columns={columns}
                dataSource={productList}
                bordered
                pagination={paginationProps}
              />
            </ConfigProvider>
          </div>
          <UpdateForm
            {...updateMethods}
            modalVisible={this.state.updateModalVisible}
            item={this.state.stepFormValues}
          />

          {this.state.isShowKeyWord && (
            <KeyWord
              isAddKeyword={isAddKeyword}
              brand={brand}
              productName={addProductName}
              keywordList={itemList}
              close={this.consoleKeyWord}
              onSaveBtn={item => {
                this.addKeyWordList(item);
              }}
            />
          )}
        </div>
      </div>
    );
  }
}

export default connect(({ product, home }) => ({ ...product, ...home }))(ProductListForm);
