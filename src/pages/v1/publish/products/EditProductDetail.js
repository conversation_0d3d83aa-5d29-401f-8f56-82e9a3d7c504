// @flow
import React, { PureComponent } from 'react';
import { connect } from 'dva';
import {
  Alert,
  Button,
  Cascader,
  Checkbox,
  Col,
  Collapse,
  Form,
  Input,
  InputNumber,
  Modal,
  Radio,
  Row,
  Select,
  Spin,
} from 'antd';
import { CloseCircleOutlined, DeleteOutlined } from '@ant-design/icons';
import 'antd/dist/antd.css';

import AllKeyword from '../../../../components/AllKeyword';
import Titles from '../../../../components/Titles';
import AlertInput from '../../../../components/AlertInput';
import AddTitles from '../../../../components/AddTitles';
import BigAlertInput from '../../../../components/BigAlertIput';
import styles from './EditProductDetail.less';
import PhotoWall from '../../../../components/PhotoWall';
import VideoWall from '../../../../components/VideoWall';
import UploadPic from '../../../../components/UploadPic';
import UploadVideo from '../../../../components/UploadVideo';
import AlertAllPrimary from '../../../../components/AlertAllPrimary';
import CustomUploadDisplaypic from '@/components/CustomUploadDisplaypic';
import CustomUploadDisplayvideo from '@/components/CustomUploadDisplayvideo';
import { baseUrl } from '../../../../configs/config';

const { Panel } = Collapse;
const { TextArea } = Input;
const { Option } = Select;
const { confirm } = Modal;

class EditProductDetailForm extends PureComponent {
  formRef = React.createRef();

  formRef2 = React.createRef();

  state = {
    productInfo: { videos: [] },
    confirmDirty: false,
    autoCompleteResult: [],
    isShowAlertInput: false,
    isShowAddTitle: false,
    isShowKeyWord: false,
    options: [],
    isShowAllKeyWord: false,
    isShowBigAlertInput: false,
    isCreate: true,
    isCreateProduct: true,
    isShowAllTitle: false,
    itemList: [],
    keyWordList: [], // 关键词
    optionTitleList: [], // 生成的标题
    TitleList: [], // 副标题
    AliasList: [], // 别名列表
    brand: '', // 品牌
    paragraphList: [],
    descList: [],
    QAList: [],
    picList: [],
    titlepicList: [],
    videos: [],
    alertTitle: '',
    product_id: '',
    name: '',
    onlyShow: false,
    itemText: '',
    itemIndex: 0,
    audit_res: '',
    isAudit: false,
    isShowPhotoWall: false,
    isShowVideoWall: false,
    videoChange: false,
    faq: '',
    isShowAlertAliasInput: false,
    isSping: false,
    onlyList: [],
    areaids: [],
    properties: {},
    panelKey: ['1'],
    cityList: [],
    CateList: [],
    isUpdateView: false,

    videosChange: false,
  };

  handleSubmit = async values => {};

  AutoSaveSubmit = e => {};

  componentDidMount() {
    const { company = {}, city, isOpenBaixing } = this.props;
    const { onlyList } = this.state;
    const { onlyShow, isCreateProduct } = this.props;
    let { id } = this.props;
    id = parseInt(id, 10);

    // 这个用于开关选项
    this.props.dispatch({ type: 'merchant/queryMerchant', payload: {} });
    this.props.dispatch({ type: 'home/getMyAllCate', payload: {} });
    this.props.dispatch({ type: 'home/getCity', payload: {} });
    this.props
      .dispatch({
        type: 'product/getProductById',
        payload: { product_id: id },
      })
      .then(productInfo => {
        let cateObj = [];

        const {
          material = [],
          description = [],
          title = [],
          option_title = [],
          videos = [],
          word = [],
          QAList = [],
          pic = [],
          titlepic = [],
          id,
          audit_res = '',
          cate = [],
          faq,
          alias = [],
          name,
          brand,
          areaids,
          properties,
          audit_res2 = {},
          status,
        } = productInfo;
        onlyList.concat(title);
        onlyList.concat(alias);

        if (cate && cate.length > 0) {
          cateObj = cate;
        } else if (company.cateList) {
          cateObj = company.cateList;
        }
        productInfo.titlepic = this.arrString2json(productInfo.titlepic);
        productInfo.videos = this.arrString2json(productInfo.videos);
        productInfo.pic = this.arrString2json(productInfo.pic);
        // filter zero
        productInfo.areaids =
          productInfo.areaids && productInfo.areaids.filter(item => item !== '0');
        this.setState(
          {
            CateList: cate,
            cityList: city,
            isCreateProduct,
            name: productInfo.name,
            cate: cateObj,
            productInfo,
          },
          () => {
            const form = this.formRef.current;
            form.resetFields();
          },
        );
        if (cateObj[cateObj.length - 1] > 0) {
          this.props
            .dispatch({
              type: 'product/getChildCateById',
              payload: { id: cateObj[cateObj.length - 1] },
            })
            .then(options => {
              if (options.length === 0) {
                this.props.dispatch({
                  type: 'product/getCateProperty',
                  payload: { id: cateObj[cateObj.length - 1], merge: isOpenBaixing ? '1' : '0' },
                  callBack: result3 => {
                    this.setState(
                      {
                        cateProPerty: result3,
                      },
                      () => {
                        const {
                          AddPropertiesTitle = {},
                          AddPropertiesValue = {},
                          cateProPerty,
                        } = this.state;

                        for (const i in properties) {
                          let ishave = false;
                          for (const j in result3) {
                            if (i === result3[j].fieldname) {
                              ishave = true;
                            }
                          }

                          if (!ishave) {
                            const newCatePropertyItem = {
                              displayname: i,
                              displayValue: properties[i],
                              fieldtype: 'addinput',
                              ItemIndex: i,
                            };
                            cateProPerty.push(newCatePropertyItem);
                            AddPropertiesTitle[i] = i;
                            AddPropertiesValue[i] = properties[i];
                          }
                        }

                        this.setState(
                          {
                            cateProPerty,
                            AddPropertiesTitle,
                            AddPropertiesValue,
                          },
                          () => {
                            // reset field
                            const form = this.formRef2.current;
                            form.resetFields();
                          },
                        );
                      },
                    );
                  },
                });
              }
            });
        }

        this.setState(
          {
            keyWordList: word || [],
            TitleList: title || [],
            optionTitleList: option_title || [],

            paragraphList: material || [],
            descList: description || [],
            QAList,
            product_id: id,
            onlyShow,
            audit_res,
            audit_res2,
            status,
            isCreateProduct,
            AliasList: alias || [],
            areaids,
            properties,
            faq,
          },
          () => {
            // reset field
            const form = this.formRef.current;
            form.resetFields();
          },
        );
      });
  }

  componentWillReceiveProps(nextProps) {}

  consoleCompany = () => {
    this.props.close();
  };

  handleChange = value => {
    this.setState({ cateChange: true });
    this.formRef.current.setFieldsValue({
      cate: value,
    });
  };

  commitAudit = () => {
    this.setState({
      isAudit: true,
    });
  };

  showAllKeyWord = () => {
    this.setState({
      isShowAllKeyWord: true,
      itemList: this.state.optionTitleList,
      type: 'option_title',
    });
  };

  closeAllKeyWord = () => {
    this.setState({
      isShowAllKeyWord: false,
    });
  };

  showAllAlias = () => {
    this.setState({
      isShowAllAlias: true,
      itemList: this.state.AliasList,
      type: 'alias',
    });
  };

  closeAllAlias = () => {
    this.setState({
      isShowAllAlias: false,
    });
  };

  showAllTitle = () => {
    this.setState({
      isShowAllTitle: true,
      itemList: this.state.TitleList,
      type: 'title',
    });
  };

  closeAllTitle = () => {
    this.setState({
      isShowAllTitle: false,
    });
  };

  replaceOptionTitle = optionTitleList => {
    this.setState({
      optionTitleChange: true,
      isShowAlertInput: false,
      optionTitleList,
    });
    this.formRef.current.setFieldsValue({
      option_tile: optionTitleList,
    });
  };

  replaceTitle = keyWordList => {
    this.setState({
      isShowAlertTitleInput: false,
      TitleList: keyWordList,
    });
    this.formRef.current.setFieldsValue({
      title: keyWordList,
    });
  };

  replaceAlias = keyWordList => {
    this.setState({
      isShowAlertaliasInput: false,
      aliasChange: true,
      AliasList: keyWordList,
    });
    this.formRef.current.setFieldsValue({
      alias: keyWordList,
    });
  };

  consoleAlertInput = () => {
    this.setState({
      isShowAlertInput: false,
    });
  };

  showAlertInput = () => {
    const { keyWordList } = this.state;
    if (keyWordList.length > 2000) {
      Modal.error({
        content: '关键字不能超过2000个',
        zIndex: 9999,
      });
      return;
    }
    this.setState({
      isShowAlertInput: true,
      itemList: this.state.keyWordList,
      type: 'keyword',
    });
  };

  showAddTitle = () => {
    this.setState({
      isShowAddTitle: true,
    });
  };

  hideAddTitle = () => {
    this.setState({
      isShowAddTitle: false,
    });
  };

  consoleAlertTitleInput = () => {
    this.setState({
      isShowAlertTitleInput: false,
    });
  };

  showAlerTitleInput = () => {
    const { TitleList } = this.state;
    if (TitleList.length > 50) {
      Modal.error({
        content: '副标题不能超过50个',
        zIndex: 9999,
      });
      return;
    }
    this.setState({
      alertTitle: '副标题',
      itemList: this.state.TitleList,
      isShowAlertTitleInput: true,
      type: 'title',
    });
  };

  consoleAlertAliasInput = () => {
    this.setState({
      isShowAlertAliasInput: false,
    });
  };

  showAlerAliasInput = () => {
    const { AliasList } = this.state;
    if (AliasList.length > 5) {
      Modal.error({
        content: '别名不能超过5个',
        zIndex: 9999,
      });
      return;
    }
    this.setState({
      alertTitle: '产品别名',
      itemList: this.state.AliasList,
      isShowAlertAliasInput: true,
      type: 'alias',
    });
  };

  createdesc = () => {
    const { descList } = this.state;
    if (descList.length >= 6) {
      Modal.error({
        content: '描述必须在3篇~6篇之间',
        zIndex: 9999,
      });
      return;
    }

    this.setState({
      alertTitle: '原创描述',
      itemList: this.state.descList,
      type: 'desc',
      alertDesc:
        '为保证产品推广效果，请您不要填写任何联系信息（如联系人，联系电话，传真，手机，邮箱，网址，微信，QQ等）',
      isShowBigAlertInput: true,
      isCreate: true,
      itemText: '',
    });
  };

  showAllPrimary = () => {
    this.setState({
      isShowAllPrimary: true,
      itemList: this.state.paragraphList,
    });
  };

  consoleAllPrimary = () => {
    console.log('sss');
    this.setState({
      isShowAllPrimary: false,
    });
  };

  createParagraph = () => {
    const { paragraphList } = this.state;
    if (paragraphList.length > 100) {
      Modal.error({
        content: '素材必须在15篇~100篇之间',
        zIndex: 9999,
      });
      return;
    }

    this.setState({
      isShowBigAlertInput: true,
      isCreate: true,
      alertTitle: '素材内容',
      itemList: this.state.paragraphList,
      type: 'paragraph',
      alertDesc:
        '为保证产品推广效果，请您不要填写任何联系信息（如联系人，联系电话，传真，手机，邮箱，网址，微信，QQ等）',
      itemText: '',
    });
  };

  Editdesc = ({ item, index }) => {
    this.setState({
      alertTitle: '原创描述',
      itemList: this.state.descList,
      type: 'desc',
      alertDesc:
        '为保证产品推广效果，请您不要填写任何联系信息（如联系人，联系电话，传真，手机，邮箱，网址，微信，QQ等）',
      isShowBigAlertInput: true,
      isCreate: false,
      itemText: item,
      itemIndex: index,
    });
  };

  EditParagraph = ({ item, index }) => {
    this.setState({
      isShowBigAlertInput: true,
      isCreate: false,
      alertTitle: '素材段落',
      itemList: this.state.paragraphList,
      type: 'paragraph',
      alertDesc:
        '为保证产品推广效果，请您不要填写任何联系信息（如联系人，联系电话，传真，手机，邮箱，网址，微信，QQ等）',
      itemText: item,
      itemIndex: index,
    });
  };

  EditQA = item => {
    this.setState({
      isShowBigAlertInput: true,
      type: 'QA',
      alertTitle: '问答',
      itemList: this.state.QAList,
      alertDesc:
        '为保证产品推广效果，请您不要填写任何联系信息（如联系人，联系电话，传真，手机，邮箱，网址，微信，QQ等）',
      itemText: item,
    });
  };

  addTitle = keyWord => {
    const { TitleList, onlyList } = this.state;
    const name =
      this.formRef.current.getFieldValue('name') &&
      this.formRef.current.getFieldValue('name').replace(/\s*/g, '');
    const brand =
      this.formRef.current.getFieldValue('brand') &&
      this.formRef.current.getFieldValue('brand').replace(/\s*/g, '');
    const keywordList = keyWord.split(/,|，|\r|\n|\r\n/);
    for (let i = 0; i < keywordList.length; i++) {
      if (keywordList[i] == '') {
        keywordList.splice(i, 1);
        i -= 1;
      } else if (
        onlyList.indexOf(keywordList[i]) >= 0 ||
        name == keywordList[i] ||
        brand == keywordList[i]
      ) {
        Modal.error({
          content: '副标题不能和名称，别名，品牌重复',
          zIndex: 9999,
        });
        this.setState({
          isShowAlertTitleInput: false,
        });
        return;
      } else {
        onlyList.push(keywordList[i]);
      }
    }
    if (keywordList.length + TitleList.length > 50) {
      Modal.error({
        content: '副标题不能超过50个',
        zIndex: 9999,
      });
      this.setState({
        isShowAlertTitleInput: false,
      });
      return;
    }
    const newTItle = this.state.TitleList;
    const titleListNew = newTItle.concat(keywordList);
    this.setState({
      isShowAlertTitleInput: false,
      TitleList: titleListNew,
    });
    this.formRef.current.setFieldsValue({
      title: titleListNew,
    });
  };

  editDescList = (item, index) => {
    const list = this.state.descList;
    list[index] = item;

    const that = this;
    that.props.dispatch({
      type: 'product/similar',
      payload: { data: list, index },
      callBack({ msg, code }) {
        if (code === 'input_param_error') {
          Modal.error({
            content: msg,
            zIndex: 9999,
          });
        } else {
          that.setState({
            descList: list,
          });
          that.formRef.current.setFieldsValue({
            description: list,
          });
        }
      },
    });
  };

  editParagraphList = (item, index) => {
    const list = this.state.paragraphList;
    const oldItem = list[index];
    list[index] = item;
    const that = this;

    that.props.dispatch({
      type: 'product/similar',
      payload: { data: list, index },
      callBack({ msg, code }) {
        if (code === 'input_param_error') {
          Modal.error({
            content: msg,
            zIndex: 9999,
          });
          console.log('set error');
          list[index] = oldItem;
        } else {
          console.log('set success');
          list[index] = item;

          that.setState({
            paragraphList: list,
            materialChange: true,
          });
          that.formRef.current.setFieldsValue({
            material: list,
          });
          that.AutoSaveSubmit();
        }
      },
    });
  };

  editQAList = (item, index) => {
    const list = this.state.QAList;
    list[index] = item;
    this.setState({
      QAList: list,
    });
    this.formRef.current.setFieldsValue({
      qa: list,
    });
  };

  addDescList = desc => {
    const that = this;
    let newList = that.state.descList;
    newList = newList.concat(desc);
    that.props.dispatch({
      type: 'product/similar',
      payload: { data: newList, index: newList.length - 1 },
      callBack({ msg, code }) {
        if (code === 'input_param_error') {
          Modal.error({
            content: msg,
            zIndex: 9999,
          });
        } else {
          that.setState({
            descList: newList,
            itemList: newList,
          });

          that.formRef.current.setFieldsValue({
            description: newList,
          });
        }
      },
    });
  };

  addParagraphList = Paragraph => {
    const that = this;
    let newList = that.state.paragraphList;
    newList = newList.concat(Paragraph);
    that.props.dispatch({
      type: 'product/similar',
      payload: { data: newList, index: newList.length - 1 },
      callBack({ msg, code }) {
        if (code === 'input_param_error') {
          Modal.error({
            content: msg,
            zIndex: 9999,
          });
        } else {
          that.setState({
            paragraphList: newList,
            itemList: newList,
          });
          that.formRef.current.setFieldsValue({
            material: newList,
          });
          that.AutoSaveSubmit();
        }
      },
    });
  };

  addQAList = QA => {
    this.setState({
      QAList: this.state.QAList.concat(QA),
      itemList: this.state.QAList.concat(QA),
    });
    this.formRef.current.setFieldsValue({
      qa: this.state.QAList.concat(QA),
    });
  };

  createQA = () => {
    this.setState({
      isShowBigAlertInput: true,
      isCreate: true,
      type: 'QA',
      alertTitle: '问答',
      itemList: this.state.QAList,
      alertDesc:
        '为保证产品推广效果，请您不要填写任何联系信息（如联系人，联系电话，传真，手机，邮箱，网址，微信，QQ等）',
    });
  };

  arrString2json(arr) {
    const result = [];
    if (!arr) {
      return [];
    }
    for (const item of arr) {
      try {
        result.push(JSON.parse(item));
      } catch (e) {
        result.push({ url: item, name: '图片名称' });
      }
    }
    return result;
  }

  closeBigAlert = () => {
    this.setState({
      isShowBigAlertInput: false,
      itemText: '',
      itemIndex: 0,
    });
  };

  showKeyWord = () => {
    const { keyWordList } = this.state;
    let name =
      this.formRef.current.getFieldValue('name') &&
      this.formRef.current.getFieldValue('name').replace(/\s*/g, '');
    let brand =
      this.formRef.current.getFieldValue('brand') &&
      this.formRef.current.getFieldValue('brand').replace(/\s*/g, '');
    if (keyWordList.length > 2000) {
      Modal.error({
        content: '关键字不能超过2000个',
        zIndex: 9999,
      });
      return;
    }

    if (!name || name === '') {
      Modal.warning({
        title: '提示',
        content: '请输入产品名称',
        zIndex: 9999,
      });
      return;
    }

    if (name.length > 20) {
      Modal.warning({
        title: '提示',
        content: '产品名称字数在20字以内',
        zIndex: 9999,
      });
      return;
    }

    if (brand != null && brand.length > 20) {
      Modal.warning({
        title: '提示',
        content: '品牌字数在20字以内',
        zIndex: 9999,
      });
      return;
    }
    name = name.replace(/\s*/g, '');
    if (brand) {
      brand = brand.replace(/\s*/g, '');
    }

    this.setState({
      name,
      brand,
      isShowKeyWord: true,
      itemList: keyWordList,
      type: 'keyword',
    });
  };

  showPhotoWall = () => {
    this.setState({
      isShowPhotoWall: true,
    });
  };

  showVideoWall = () => {
    this.setState({
      isShowVideoWall: true,
    });
  };

  showTitlePhotoWall = () => {
    this.setState({
      isShowTitlePhotoWall: true,
    });
  };

  consolePhotoWall = () => {
    this.setState({
      isShowPhotoWall: false,
    });
  };

  closeVideoWall = () => {
    this.setState({
      isShowVideoWall: false,
    });
  };

  consoleTitlePhotoWall = () => {
    this.setState({
      isShowTitlePhotoWall: false,
    });
  };

  save = item => {
    const { type } = this.state;
    if (type === 'QA') {
      this.addQAList(item);
    } else if (type === 'paragraph') {
      this.addParagraphList(item);
    } else if (type === 'desc') {
      this.addDescList(item);
    } else if (type === 'title') {
      this.addTitle(item);
    }
  };

  edit = (data, index) => {
    const { type } = this.state;
    if (type === 'QA') {
      this.editQAList(data, index);
    } else if (type === 'paragraph') {
      this.editParagraphList(data, index);
    } else if (type === 'desc') {
      this.editDescList(data, index);
    } else if (type === 'title') {
      this.addTitle(data);
    }
  };

  deleteDescItem = (item, index) => {
    const that = this;
    const { descList } = this.state;
    confirm({
      title: '删除描述',
      content: '确定删除该描述内容?',
      zIndex: 9999,
      cancelText: '取消',
      okText: '确定',
      onOk() {
        descList.splice(index, 1);
        const list = descList;
        that.setState({
          descList: list,
        });
        that.formRef.current.setFieldsValue({
          description: list,
        });
      },
      onCancel() {},
    });
  };

  deleteParagraphItem = (item, fromModel, ...indexs) => {
    const that = this;
    const { paragraphList, materialChange } = this.state;
    confirm({
      title: '删除描述',
      content: '确定删除该描述内容?',
      zIndex: 9999,
      cancelText: '取消',
      okText: '确定',
      onOk() {
        const newlist = [];
        for (let idx in paragraphList) {
          idx = parseInt(idx);
          if (!indexs.includes(idx)) {
            newlist.push(paragraphList[idx]);
          }
        }
        const list = newlist;
        that.setState({
          materialChange: !materialChange,
          paragraphList: list,
        });
        if (fromModel) {
          that.setState({
            itemList: list,
          });
        }
        that.formRef.current.setFieldsValue({
          material: list,
        });
      },
      onCancel() {},
    });
  };

  deleteQAItem = (item, index) => {
    const that = this;
    const { QAList } = this.state;
    confirm({
      title: '删除描述',
      content: '确定删除该描述内容?',
      zIndex: 9999,
      cancelText: '取消',
      okText: '确定',
      onOk() {
        QAList.splice(index, 1);
        const list = QAList;

        that.setState({
          QAList: list,
        });
        that.formRef.current.setFieldsValue({
          qa: list,
        });
      },
      onCancel() {},
    });
  };

  onChange = (value, selectedOptions) => {
    const that = this;
    const { company, isOpenBaixing } = this.props;
    const cate = [];

    selectedOptions.map(item => {
      cate.push(item.value);
      return item;
    });
    this.setState({ cateChange: true });

    this.formRef.current.setFieldsValue({
      cate,
    });
    if (cate.length > 2) {
      that.props
        .dispatch({
          type: 'product/getChildCateById',
          payload: { id: cate[cate.length - 1] },
        })
        .then(options => {
          if (options.length === 0) {
            that.props.dispatch({
              type: 'product/getCateNotSupportPlatform',
              payload: { id: cate[cate.length - 1], cid: company.id },
              callBack(data) {
                if (data && data.length > 0) {
                  that.setState({
                    cateTip: data.toString(),
                  });
                } else {
                  that.setState({
                    cateTip: '',
                  });
                }
              },
            });

            that.props.dispatch({
              type: 'product/getCateProperty',
              payload: { id: cate[cate.length - 1], merge: isOpenBaixing ? '1' : '0' },

              callBack(result3) {
                that.setState({
                  propertiesChange: true,
                  properties: {},
                  cateProPerty: result3,
                  AddPropertiesTitle: {},
                  AddPropertiesValue: {},
                });
              },
            });
          }
        });
    }
  };

  onChangeCity = (value, selectedOptions) => {
    if (!selectedOptions) {
      this.setState({
        areaidsChange: true,
        areaids: ['0'],
      });
      return;
    }
    const that = this;
    const areaids = [];
    const { company } = this.props;
    selectedOptions.map(item => {
      that.props.dispatch({
        type: 'product/getCityNotSupportPlatform',
        payload: { id: item.value, cid: company.id },
        callBack(data) {
          if (data && data.length > 0) {
            that.setState({
              areaidsTip: data.toString(),
            });
          } else {
            that.setState({
              areaidsTip: '',
            });
          }
        },
      });

      areaids.push(item.value);
    });

    this.setState({
      areaidsChange: true,
      areaids,
    });
  };

  onCityLoadData = selectedOptions => {
    const { cityList, isUpdateView } = this.state;
    const that = this;
    let id = 0;
    selectedOptions.map(item => {
      id = item.value;
      return item;
    });
    return;
  };

  onCateLoadData = selectedOptions => {
    const { CateList, isUpdateView } = this.state;
    const that = this;
    let id = 0;
    selectedOptions.map(item => {
      id = item.value;
      return item;
    });
    return;
  };

  filter = (inputValue, path) =>
    path.some(option => option.label.toLowerCase().indexOf(inputValue.toLowerCase()) > -1);

  validateMode = (rule, value, callback) => {
    const { onlyList, isAudit } = this.state;
    const name =
      this.formRef.current.getFieldValue('name') &&
      this.formRef.current.getFieldValue('name').replace(/\s*/g, '');
    const brand =
      this.formRef.current.getFieldValue('brand') &&
      this.formRef.current.getFieldValue('brand').replace(/\s*/g, '');

    if (!isAudit) {
      callback();
    }

    if (onlyList.indexOf(value) >= 0 || value === name) {
      callback('名称，别名，品牌不能重复');
    } else {
      callback();
    }
  };

  changePanelValue = e => {
    const { panelKey } = this.state;
    // console.log('panel', e)
    this.setState({
      panelKey: e,
    });
  };

  AddProPerty = () => {
    const { cateProPerty, AddPropertiesTitle = {}, AddPropertiesValue = {} } = this.state;

    const timestamp1 = Date.now();

    const newCatePropertyItem = {
      displayname: '',
      displayValue: '',
      fieldtype: 'addinput',
      ItemIndex: timestamp1,
    };
    AddPropertiesTitle[timestamp1] = '';
    AddPropertiesValue[timestamp1] = '';
    this.setState({
      propertiesChange: true,
      cateProPerty: [...cateProPerty, newCatePropertyItem],
      AddPropertiesTitle,
      AddPropertiesValue,
    });
  };

  changeAddProPertyItemTitle = (e, item) => {
    const { AddPropertiesTitle = {} } = this.state;

    AddPropertiesTitle[item.ItemIndex] = e.target.value;

    this.setState({
      propertiesChange: true,
      AddPropertiesTitle,
    });
  };

  changeAddProPertyItemValue = (e, item) => {
    const { AddPropertiesValue = {} } = this.state;

    AddPropertiesValue[item.ItemIndex] = e.target.value;

    this.setState({
      propertiesChange: true,
      AddPropertiesValue,
    });
  };

  delProperItem = (e, item) => {
    const { AddPropertiesValue, AddPropertiesTitle, cateProPerty, properties } = this.state;
    const newAddPropertiesValue = {};
    for (var i in AddPropertiesValue) {
      if (i != item.ItemIndex) {
        newAddPropertiesValue[i] = AddPropertiesValue[i];
      }
    }

    const newAddPropertiesTitle = {};
    for (const i in AddPropertiesTitle) {
      if (i !== item.ItemIndex) {
        newAddPropertiesTitle[i] = AddPropertiesTitle[i];
      }
    }

    const newcateProPerty = [];
    for (let i = 0; i < cateProPerty.length; i++) {
      if (cateProPerty[i].ItemIndex != item.ItemIndex) {
        newcateProPerty.push(cateProPerty[i]);
      }
    }

    const newProperties = {};
    for (var i in properties) {
      if (i != item.ItemIndex) {
        newProperties[i] = properties[i];
      }
    }

    this.setState({
      propertiesChange: true,
      cateProPerty: newcateProPerty,
      AddPropertiesTitle: newAddPropertiesTitle,
      AddPropertiesValue: newAddPropertiesValue,
      properties: newProperties,
    });
  };

  changeValue = (e, item) => {
    const { properties = {} } = this.state;

    if (item.fieldtype === 'select') {
      properties[item.fieldname] = e;
    }
    if (item.fieldtype === 'radio') {
      properties[item.fieldname] = e.target.value;
    }
    if (item.fieldtype === 'checkbox') {
      properties[item.fieldname] = e;
      console.log(properties[item.fieldname]);
    }
    if (item.fieldtype === 'input') {
      properties[item.fieldname] = e.target.value;
    }
    if (item.fieldtype === 'textarea') {
      properties[item.fieldname] = e.target.value;
    }
    this.setState({
      propertiesChange: true,
      properties,
    });
  };

  onRidaoClick = (e, item) => {
    const { properties = {}, propertiesChange } = this.state;
    let newPro = {};
    newPro = properties;

    if (properties[item.fieldname] == e.target.value) {
      newPro[item.fieldname] = '';

      this.setState({
        propertiesChange: true,
        properties: newPro,
      });
    }
  };

  onClearClick = (e, item) => {
    const { properties = {}, propertiesChange } = this.state;
    let newPro = {};
    newPro = properties;

    newPro[item.fieldname] = '';

    this.setState({
      propertiesChange: true,
      // changeView: !changeView,
      properties: newPro,
    });
  };

  onTitleUpdated = (keyWordList, TitleList, AliasList, optionTitleList) => {
    const keywordChange =
      this.state.keywordChange || !(this.state.keyWordList.join('#') == keyWordList.join('#'));
    const optionTitleChange =
      this.state.optionTitleChange ||
      !(this.state.optionTitleList.join('#') == optionTitleList.join('#'));
    this.setState({
      keyWordList,
      TitleList,
      AliasList,
      optionTitleList,
      optionTitleChange,
      keywordChange,
    });
  };

  render() {
    const CreateProPerty = () => {
      const cateProPertyList = [];
      const { AddPropertiesValue, AddPropertiesTitle } = this.state;
      const { properties = {}, cateProPerty = [] } = this.state;

      cateProPerty.map(item => {
        if (item.fieldtype === 'input') {
          cateProPertyList.push(
            <Form.Item
              label={item.displayname}
              labelAlign="left"
              name={item.fieldname}
              rules={[
                { required: item.required, message: `请输入${item.displayname}`, whitespace: true },
              ]}
            >
              <Input
                disabled={onlyShow}
                placeholder={`请输入${item.displayname}`}
                defaultValue={properties && properties[item.fieldname]}
                onChange={e => {
                  this.changeValue(e, item);
                }}
              />
            </Form.Item>,
          );
        }
        if (item.fieldtype === 'addinput') {
          cateProPertyList.push(
            <Row style={{ marginTop: '10px' }}>
              <Col span={4}>
                <Input
                  disabled={onlyShow}
                  placeholder="请输入参数名"
                  defaultValue={AddPropertiesTitle && AddPropertiesTitle[item.ItemIndex]}
                  onChange={e => {
                    this.changeAddProPertyItemTitle(e, item);
                  }}
                />
              </Col>
              <div style={{ lineHeight: '30px' }}>:</div>
              <Col span={6}>
                <Input
                  disabled={onlyShow}
                  placeholder="请输入参数值"
                  defaultValue={AddPropertiesValue && AddPropertiesValue[item.ItemIndex]}
                  onChange={e => {
                    this.changeAddProPertyItemValue(e, item);
                  }}
                />
              </Col>
              <Col span={1}>
                <Button
                  type="primary"
                  style={{ marginLeft: '5px' }}
                  size="Small"
                  onClick={e => {
                    this.delProperItem(e, item);
                  }}
                >
                  删除
                </Button>
              </Col>
            </Row>,
          );
        }

        if (item.fieldtype === 'textarea') {
          cateProPertyList.push(
            <Form.Item
              label={item.displayname}
              name={item.fieldname}
              labelAlign="left"
              rules={[
                { required: item.required, message: `请输入${item.displayname}`, whitespace: true },
              ]}
            >
              <TextArea
                defaultValue={properties && properties[item.fieldname]}
                rows={6}
                disabled={onlyShow}
                onChange={e => {
                  this.changeValue(e, item);
                }}
              />
            </Form.Item>,
          );
        }

        if (item.fieldtype === 'checkbox') {
          cateProPertyList.push(
            <Form.Item label={item.displayname} name={item.fieldname} labelAlign="left">
              <Checkbox.Group
                defaultValue={properties && properties[item.fieldname]}
                onChange={e => {
                  this.changeValue(e, item);
                }}
              >
                {item.fieldoptions.map(checkItem => (
                  <Checkbox value={checkItem.value ? checkItem.value : checkItem}>
                    {checkItem.label ? checkItem.label : checkItem}
                  </Checkbox>
                ))}
              </Checkbox.Group>
            </Form.Item>,
          );
        }
        if (item.fieldtype === 'radio') {
          cateProPertyList.push(
            <Form.Item
              label={item.displayname}
              name={item.fieldname}
              labelAlign="left"
              rules={[
                { required: item.required, message: `请选择${item.displayname}`, whitespace: true },
              ]}
            >
              <Radio.Group defaultValue={properties && properties[item.fieldname]}>
                {item.fieldoptions.map(checkItem => (
                  <Radio
                    value={checkItem.value ? checkItem.value : checkItem}
                    onClick={e => {
                      this.onRidaoClick(e, item);
                    }}
                    onChange={e => {
                      this.changeValue(e, item);
                    }}
                  >
                    {checkItem.label ? checkItem.label : checkItem}
                  </Radio>
                ))}
              </Radio.Group>
            </Form.Item>,
          );
        }
        if (item.fieldtype === 'select') {
          cateProPertyList.push(
            <Form.Item
              label={item.displayname}
              labelAlign="left"
              name={item.fieldname}
              rules={[
                { required: item.required, message: `请选择${item.displayname}`, whitespace: true },
              ]}
            >
              {' '}
              <Select
                defaultValue={properties && properties[item.fieldname]}
                style={{ width: 200 }}
                dropdownStyle={{ zIndex: 5000 }}
                allowClear
                onClear={e => {
                  this.onClearClick(e, item);
                }}
                onChange={e => {
                  this.changeValue(e, item);
                }}
              >
                {item.fieldoptions.map(checkItem =>
                  checkItem.label ? (
                    <Option value={checkItem.value}>{checkItem.label}</Option>
                  ) : (
                    <Option value={checkItem}>{checkItem}</Option>
                  ),
                )}
              </Select>
            </Form.Item>,
          );
        }
      });
      return (
        <div>
          <Row>
            <Col span={3} />
            <Col span={17}>
              <Collapse
                bordered={false}
                style={{ background: '#EEEEEE', marginBottom: 10 }}
                defaultActiveKey={this.state.panelKey}
                onChange={this.changePanelValue}
              >
                <Panel header="产品属性参数:" key="1">
                  <Form initialValues={properties} ref={this.formRef2} model={properties}>
                    {cateProPertyList}
                  </Form>
                  <Row>
                    <Button onClick={this.AddProPerty} style={{ marginTop: 10 }}>
                      添加自定义参数
                    </Button>
                  </Row>
                </Panel>
              </Collapse>
            </Col>
          </Row>
        </div>
      );
    };

    const { company = {}, userId, cateOptions, cityOptions } = this.props;
    const { productInfo = {} } = this.state;

    console.log('render called', productInfo);
    const {
      autoCompleteResult,
      audit_res,
      status,
      audit_res2,
      isCreateProduct,
      AliasList,
      keyWordList = [],
      optionTitleList = [],
      titlepicList,
      picList,
      videos,
      descList,
      QAList,
      paragraphList,
      itemList,
      alertTitle,
      areaids,
      cate,
      type,
      name,
      brand,
      TitleList = [],
      onlyShow,
      isSping,
      options,
    } = this.state;
    let objLen = 0;
    const auditView = [];
    if (audit_res2 != null) {
      for (const i in audit_res2) {
        let tipStr = '';
        switch (i) {
          case 'name':
            tipStr = `产品名称:${audit_res2[i]};`;

            break;
          case 'videos':
            tipStr = `视频:${audit_res2[i]};`;
            break;
          case 'alias':
            tipStr = `产品别名:${audit_res2[i]};`;

            break;
          case 'areaids':
            tipStr = `面向地区:${audit_res2[i]};`;

            break;
          case 'cate':
            tipStr = `分类:${audit_res2[i]};`;

            break;
          case 'brand':
            tipStr = `品牌:${audit_res2[i]};`;

            break;
          case 'price':
            tipStr = `单价:${audit_res2[i]};`;

            break;
          case 'unit':
            tipStr = `单位:${audit_res2[i]};`;

            break;
          case 'min_order':
            tipStr = `起订量:${audit_res2[i]};`;

            break;
          case 'inventory':
            tipStr = `库存:${audit_res2[i]};`;

            break;
          case 'titlepic':
            tipStr = `标题图片:${audit_res2[i]};`;

            break;
          case 'pic':
            tipStr = `详情图片:${audit_res2[i]};`;

            break;
          case 'properties':
            tipStr = `属性:${audit_res2[i]};`;

            break;
          case 'word':
            tipStr = `关键词:${audit_res2[i]};`;

            break;
          case 'material':
            tipStr = `素材:${audit_res2[i]};`;

            break;
          case 'faq':
            tipStr = `问答:${audit_res2[i]};`;

            break;
          case 'option_title':
            tipStr = `标题:${audit_res2[i]};`;

            break;
        }

        auditView.push(
          <Alert type="error" description={`审核未通过，原因：${tipStr}`} closable banner />,
        );

        objLen++;
      }
    }

    const formItemLayout = {
      labelCol: { span: 3 },
      wrapperCol: { span: 15 },
    };

    const tailFormItemLayout = {
      wrapperCol: {
        xs: {
          span: 24,
          offset: 0,
        },
        sm: {
          span: 16,
          offset: 8,
        },
      },
    };

    const Authorization = {
      Authorization: `Bearer ${window.sessionStorage.getItem('Authorization')}`,
      multiple: '',
    };
    return (
      <div style={{ zIndex: 20, position: 'relative' }}>
        <div style={{ background: '#fff', padding: 24 }}>
          <div className={styles.mask} />
          <div className={styles.modalDlg}>
            <div className={styles.titleName}>产品详情</div>
            <CloseCircleOutlined className={styles.closeIcon} onClick={this.consoleCompany} />

            <Form
              className={styles.tableView}
              {...formItemLayout}
              onFinish={this.handleSubmit}
              ref={this.formRef}
              initialValues={{
                name: productInfo && productInfo.name,
                alias: AliasList,

                areaids:
                  productInfo && productInfo.areaids && productInfo.areaids[0] == '0'
                    ? []
                    : productInfo.areaids,
                cate: productInfo && productInfo.cate,
                brand: productInfo && productInfo.brand,
                price: productInfo && productInfo.price,
                unit: productInfo && productInfo.unit,
                min_order: productInfo && productInfo.min_order,
                inventory: productInfo && productInfo.inventory,
                titlepic: productInfo && productInfo.titlepic,
                pic: productInfo && productInfo.pic,
                faq: productInfo && productInfo.faq,
              }}
            >
              {audit_res2 != null && objLen != 0 && status == 5 && auditView}
              <Form.Item
                label={<span className={styles.lableName}>产品名称&nbsp;</span>}
                help="产品名称字数在20字以内"
                name="name"
                rules={[{ required: true, message: '例如“洒水车”' }]}
              >
                <Input
                  placeholder="例如“洒水车”"
                  disabled={onlyShow}
                  onChange={e => {
                    this.setState({ name: e.target.value });
                  }}
                />
              </Form.Item>
              <Form.Item
                label={
                  <span>
                    <span style={{ color: 'red' }}>*</span>信息标题&nbsp;
                  </span>
                }
                name="word"
              >
                <div>
                  <Row>
                    <Button
                      type="primary"
                      style={{ marginLeft: '10px' }}
                      onClick={this.showAddTitle}
                    >
                      添加标题
                    </Button>
                    {/* <div className={styles.tipText}>{keyWordList.length>0?"当前最多可生成"+keyWordList.length*2+"条信息":""}</div> */}
                  </Row>
                  <Row>
                    <div className={styles.keywordView}>
                      当前已添加{optionTitleList ? optionTitleList.length : 0}
                      个标题，最少添加200个，最多添加2000个
                      {optionTitleList && optionTitleList.length > 0 && (
                        <Button
                          type="primary"
                          style={{ marginLeft: '10px' }}
                          onClick={this.showAllKeyWord}
                        >
                          查看所有
                        </Button>
                      )}
                    </div>
                  </Row>
                </div>
              </Form.Item>
              <Form.Item
                label={<span>面向地区&nbsp;</span>}
                help={onlyShow ? '' : '默认面向全国'}
                name="areaids"
              >
                <Cascader
                  options={cityOptions}
                  placeholder="全国"
                  changeOnSelect
                  defaultValue={productInfo.areaids}
                  getPopupContainer={triggerNode => triggerNode.parentNode}
                />
                {this.state.areaidsTip && this.state.areaidsTip.length > 0 && (
                  <Alert
                    type="warning"
                    description={`当前选择城市无法发布到以下平台：${this.state.areaidsTip}`}
                    closable
                    banner
                  />
                )}
              </Form.Item>

              <Form.Item
                label={
                  <span>
                    <span style={{ color: 'red' }}>*</span>分类&nbsp;
                  </span>
                }
                help={onlyShow ? '' : '分类需要选择到最后一级'}
                name="cate"
              >
                <Cascader
                  options={cateOptions}
                  changeOnSelect
                  placeholder="分类需要选择到最后一级"
                  showSearch={this.filter}
                  defaultValue={productInfo.cate}
                  getPopupContainer={triggerNode => triggerNode.parentNode}
                />
                {this.state.cateTip && this.state.cateTip.length > 0 && (
                  <Alert
                    type="warning"
                    description={`当前选择分类无法发布到以下平台：${this.state.cateTip}`}
                    closable
                    banner
                  />
                )}
              </Form.Item>

              <CreateProPerty />
              <Form.Item
                label={<span>品牌&nbsp;</span>}
                help="品牌字数在20字以内"
                name="brand"
                rules={[{ validator: this.validateMode }]}
              >
                <Input placeholder="例如“东风”（无品牌的公司请填写简称）" disabled={onlyShow} />
              </Form.Item>

              <Row>
                <Col span={12}>
                  <Form.Item
                    label={
                      <span className={styles.lableName}>
                        <span style={{ color: 'red' }}>*</span>单价&nbsp;
                      </span>
                    }
                    help={onlyShow ? '' : '（0 代表面议）'}
                    name="price"
                    labelCol={{ span: 6 }}
                    wrapperCol={{ span: 5 }}
                  >
                    <InputNumber style={{ width: '120px' }} disabled={onlyShow} />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label={
                      <span>
                        <span style={{ color: 'red' }}>*</span>单位&nbsp;
                      </span>
                    }
                    name="unit"
                    labelCol={{ span: 5 }}
                    wrapperCol={{ span: 5 }}
                  >
                    <Input style={{ width: '120px' }} disabled={onlyShow} />
                  </Form.Item>
                </Col>
              </Row>
              <Row>
                <Col span={12}>
                  <Form.Item
                    label={<span className={styles.lableName}>起订量&nbsp;</span>}
                    name="min_order"
                    labelCol={{ span: 6 }}
                    wrapperCol={{ span: 5 }}
                  >
                    <InputNumber style={{ width: '120px' }} disabled={onlyShow} />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label={<span className={styles.lableName}>库存&nbsp;</span>}
                    name="inventory"
                    labelCol={{ span: 5 }}
                    wrapperCol={{ span: 5 }}
                  >
                    <InputNumber style={{ width: '120px' }} disabled={onlyShow} />
                  </Form.Item>
                </Col>
              </Row>
              <Row />
              <Form.Item
                label={
                  <span>
                    <span style={{ color: 'red' }}>*</span>标题图片&nbsp;
                  </span>
                }
                name="titlepic"
                valuePropName="titlepic"
                getValueFromEvent={this.normFile}
              >
                <div>
                  <Row>
                    <div className={styles.tipText}>
                      {onlyShow ? '' : '图片清晰、完整，上传3-50张'}
                    </div>
                  </Row>
                  <Row>
                    <CustomUploadDisplaypic images={productInfo.titlepic} />
                  </Row>
                </div>
              </Form.Item>

              <Form.Item
                label={
                  <span>
                    <span style={{ color: 'red' }}>*</span>详情图片&nbsp;
                  </span>
                }
                name="pic"
                valuePropName="pic"
                getValueFromEvent={this.normFile}
              >
                <div>
                  <Row>
                    <div className={styles.tipText}>
                      {onlyShow ? '' : '图片清晰、完整，上传3-50张'}
                    </div>
                  </Row>
                  <Row>
                    <CustomUploadDisplaypic images={productInfo.pic} />
                  </Row>
                </div>
              </Form.Item>
              <Form.Item
                label={<span>视频&nbsp;</span>}
                name="videos"
                valuePropName="videos"
                getValueFromEvent={this.normFile}
              >
                <div>
                  <Row>
                    <div className={styles.tipText}>
                      {onlyShow ? '' : '选择与产品相关的视频，最多上传6个，仅支持在黄页88网显示。'}
                    </div>
                  </Row>
                  <Row>
                    <CustomUploadDisplayvideo videos={productInfo.videos} />
                  </Row>
                </div>
              </Form.Item>

              <Form.Item
                label={
                  <span>
                    <span style={{ color: 'red' }}>*</span>素材内容&nbsp;
                  </span>
                }
                name="material"
              >
                <div>
                  <Row>
                    <Button
                      type="primary"
                      style={{ marginLeft: '10px' }}
                      disabled={onlyShow}
                      onClick={this.createParagraph}
                    >
                      添加素材内容
                    </Button>
                    <Button
                      type="primary"
                      style={{ marginLeft: '10px' }}
                      onClick={this.showAllPrimary}
                    >
                      查看全部
                    </Button>
                    {/* <Button type="primary" style={{marginLeft:'10px'}} >段落示例</Button> */}
                    <div className={styles.tipText}>
                      {onlyShow
                        ? ''
                        : '素材内容必须在50-5000字之间，上传15-100篇。素材内容为比较通用的产品定义、描述、外观、组成、特性、结构、范围、注意事项等，需保证语句完整且通顺。'}
                    </div>
                  </Row>
                  <Row>
                    <div className={styles.keywordResult}>
                      {paragraphList.map((item, index) => (
                        <div
                          className={styles.AddItem}
                          disabled={onlyShow}
                          onClick={() => this.EditParagraph({ item, index })}
                        >
                          {`素材${index + 1}`}

                          <DeleteOutlined
                            className={styles.iconDelete}
                            onClick={e => {
                              e.stopPropagation();
                              this.deleteParagraphItem(item, false, index);
                            }}
                          />
                        </div>
                      ))}
                    </div>
                  </Row>
                </div>
              </Form.Item>

              <Form.Item label={<span>产品问答&nbsp;</span>} help="最多400字" name="faq">
                <TextArea rows={6} disabled={onlyShow} />
              </Form.Item>

              <Form.Item {...tailFormItemLayout}>
                <Button type="primary" htmlType="submit">
                  {this.state.onlyShow ? '返回' : '保存草稿'}
                </Button>
                {!this.state.onlyShow && (
                  <Button
                    type="primary"
                    htmlType="submit"
                    onClick={this.commitAudit}
                    style={{ marginLeft: '10px' }}
                  >
                    提交审核
                  </Button>
                )}
              </Form.Item>
            </Form>
          </div>
          {isSping && (
            <div className={styles.spin}>
              <Spin size="large" spinning={isSping} />
            </div>
          )}
        </div>

        {this.state.isShowAllKeyWord && (
          <Titles
            close={this.closeAllKeyWord}
            keywordList={itemList}
            MaxLen={30}
            Name="标题"
            type={this.state.type}
            onSaveBtn={item => {
              this.replaceOptionTitle(item);
            }}
          />
        )}
        {this.state.isShowAllTitle && (
          <AllKeyword
            close={this.closeAllTitle}
            keywordList={itemList}
            type={this.state.type}
            onSaveBtn={item => {
              this.replaceTitle(item);
            }}
          />
        )}
        {this.state.isShowAllAlias && (
          <AllKeyword
            close={this.closeAllAlias}
            keywordList={itemList}
            type={this.state.type}
            onSaveBtn={item => {
              this.replaceAlias(item);
            }}
          />
        )}
        {this.state.isShowPhotoWall && (
          <PhotoWall
            productName={name}
            close={this.consolePhotoWall}
            picList={picList}
            onAddPic={this.onAddPic}
          />
        )}
        {this.state.isShowVideoWall && (
          <VideoWall
            productName={name}
            close={this.closeVideoWall}
            selectedVideos={videos}
            onVideoAdded={this.onVideoAdded}
          />
        )}
        {this.state.isShowTitlePhotoWall && (
          <PhotoWall
            productName={name}
            close={this.consoleTitlePhotoWall}
            picList={titlepicList}
            onAddPic={this.onAddTitlePic}
          />
        )}

        {this.state.isShowAddTitle && (
          <AddTitles
            close={this.hideAddTitle}
            keywordList={keyWordList}
            alias={AliasList}
            productName={name}
            userId={userId}
            optionTitleList={optionTitleList}
            brand={brand}
            titleList={TitleList}
            onSaveBtn={this.onTitleUpdated}
          />
        )}
        {this.state.isShowAllPrimary && (
          <AlertAllPrimary
            close={this.consoleAllPrimary}
            itemList={itemList}
            editParagraphList={(item, index) => {
              this.editParagraphList(item, index);
            }}
            deleteParagraphItem={(item, ...args) => {
              this.deleteParagraphItem(item, true, ...args);
            }}
          />
        )}
        {this.state.isShowAlertInput && (
          <AlertInput
            close={this.consoleAlertInput}
            itemList={itemList}
            type={this.state.type}
            onSaveBtn={item => {
              this.save(item);
            }}
          />
        )}
        {this.state.isShowAlertTitleInput && (
          <AlertInput
            close={this.consoleAlertTitleInput}
            itemList={itemList}
            type={this.state.type}
            onSaveBtn={item => {
              this.save(item);
            }}
          />
        )}
        {this.state.isShowAlertAliasInput && (
          <AlertInput
            close={this.consoleAlertAliasInput}
            itemList={itemList}
            type={this.state.type}
            onSaveBtn={item => {
              this.save(item);
            }}
          />
        )}
        {this.state.isShowBigAlertInput && (
          <BigAlertInput
            itemIndex={this.state.itemIndex}
            itemText={this.state.itemText}
            title={alertTitle}
            itemList={itemList}
            desc={this.state.alertDesc}
            type={this.state.type}
            close={this.closeBigAlert}
            onSaveBtn={item => {
              this.save(item);
            }}
            isCreate={this.state.isCreate}
            onEditBtn={(item, index) => {
              this.edit(item, index);
            }}
          />
        )}
      </div>
    );
  }
}

export default connect(({ product, home, merchant }) => ({
  ...product,
  ...home,
  ...merchant,
}))(EditProductDetailForm);
