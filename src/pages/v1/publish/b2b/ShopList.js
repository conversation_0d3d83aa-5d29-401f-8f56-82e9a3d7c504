// @flow
import React, { PureComponent } from 'react';
import { connect } from 'dva';
import {
  Form,
  Table,
  Popover,
  Divider,
  Select,
  Row,
  Col,
  Button,
  AutoComplete,
  Input,
  Layout,
  ConfigProvider,
} from 'antd';
import { CloseOutlined } from '@ant-design/icons';
import 'antd/dist/antd.css';
import styles from './ShopList.less';
import zh_CN from 'antd/lib/locale-provider/zh_CN';
import UploadStatementPic from '@/components/UploadStatementPic';

const { Option } = Select;

const { Header, Content } = Layout;

class ShopListForm extends PureComponent {
  state = {
    confirmDirty: false,
    autoCompleteResult: [],
  };

  handleChange = value => {
    console.log(`selected ${value}`);
  };

  componentDidMount() {
    this.props.dispatch({ type: 'merchant/queryMerchant', payload: {} });
  }
  getProductOnlyShow = data => {
    this.props.dispatch({
      type: 'home/getUrl',
      payload: {
        url: 'http://my.huangye88.com/chanpin/',
      },
      callBack: url => {
        window.open(url, '_blank');
      },
    });
  };

  ShowAddPicAlbum = id => {
    this.setState({
      type: 'add',
      isShowAddPicAlbum: true,
      selId: id,
    });
  };
  consoleAddPicAlbum = () => {
    this.setState({
      isShowAddPicAlbum: false,
    });
    this.props.dispatch({ type: 'merchant/queryMerchant', payload: {} });
  };

  openCompany = () => {
    this.props.showCompany();
  };

  render() {
    // In the fifth row, other columns are merged into first column
    // by setting it's colSpan to be 0
    const renderContent = (value, row, index) => {
      const obj = {
        children: value,
        props: {},
      };

      return obj;
    };

    const columns = [
      {
        title: '序号',
        dataIndex: 'id',
        render: renderContent,
        width: 100,
      },
      {
        title: 'B2B平台',
        dataIndex: 'name',
        render: renderContent,
        width: 150,
      },
      {
        title: '平台地址',
        dataIndex: 'company_site',
        render: (text, record) => {
          return (
            <a type="link" href={record.company_site} target="_blank" rel="noreferrer">
              {record.company_site}
            </a>
          );
        },
      },
      {
        title: '启用状态',
        dataIndex: 'pause',
        render: (text, record) => (record.pause ? '暂停' : '启用'),
        width: 100,
      },
      {
        title: '每个推广产品发布量',
        dataIndex: 'pub_per_count',
        render: renderContent,
        width: 100,
      },

      {
        title: '状态',
        dataIndex: 'status',
        render: (text, record) => {
          console.log(
            record.reason != undefined && record.reason != '' && record.reason.length > 0,
          );
          return (
            <div>
              {record.reason != undefined && record.reason != '' && record.reason.length > 0 && (
                <Popover placement="bottom" zIndex={9999} trigger="hover" content={record.reason}>
                  <span style={{ color: 'blue' }}>{record.status}</span>
                </Popover>
              )}
              {(record.reason == undefined || record.reason == '') && <div> {record.status}</div>}
              {record.action == 1 && (
                <Row>
                  <a style={{ color: 'blue' }} onClick={this.openCompany}>
                    上传营业执照
                  </a>
                </Row>
              )}
              {record.action == 2 && (
                <Row>
                  <a
                    style={{ color: 'blue' }}
                    onClick={() => {
                      this.ShowAddPicAlbum(record.id);
                    }}
                  >
                    {' '}
                    上传企业声明
                  </a>
                </Row>
              )}
            </div>
          );
        },

        width: 200,
      },
    ];
    const { merchantList = [] } = this.props;

    return (
      <div style={{ background: '#fff', padding: 24 }}>
        <div>
          <div className={styles.titleName}>B2B商铺</div>
          <ConfigProvider locale={zh_CN}>
            <Table
              columns={columns}
              dataSource={merchantList}
              bordered
              pagination={{ position: 'none' }}
            />
          </ConfigProvider>
        </div>
        {this.state.isShowAddPicAlbum && (
          <UploadStatementPic close={this.consoleAddPicAlbum} id={this.state.selId} />
        )}
      </div>
    );
  }
}

export default connect(({ merchant }) => ({ ...merchant }))(ShopListForm);
