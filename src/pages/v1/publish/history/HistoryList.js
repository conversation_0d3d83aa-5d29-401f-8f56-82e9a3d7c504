// @flow
import React, { PureComponent } from 'react';
import { connect } from 'dva';
import { Table, Select, Row, Col, Button, Input, Layout, <PERSON><PERSON>, Modal, ConfigProvider } from 'antd';
import { CloseOutlined } from '@ant-design/icons';
import 'antd/dist/antd.css';
import zh_CN from 'antd/lib/locale-provider/zh_CN';
import styles from './HistoryList.less';
import Manual from '@/components/Manual.js';

const { Option } = Select;

const { Header, Content } = Layout;

class ProductListForm extends PureComponent {
  state = {
    confirmDirty: false,
    autoCompleteResult: [],
    keyword: '',
    pub_type: '',
    platform: '-1',
    status: '',
    isShowManual: false,
    isShowMsg: false,
    isAiShoulu: false,
  };

  consoleManual = () => {
    this.setState({
      isShowManual: false,
    });
  };

  handleChange = value => {
    this.setState({
      pub_type: value,
    });
  };

  handlePlatformChange = value => {
    this.setState({
      platform: value,
    });
  };

  handleChangeStatus = value => {
    this.setState({
      status: value,
    });
  };

  changePageSize = content => {
    console.log(content);
    const { InfoList = [], pagination } = this.props;
    const { pageSize, current } = content;
    const { keyword, pub_type, status, platform } = this.state;
    if (this.state.status == 5) {
      this.setState({
        isAiShoulu: true,
      });
    } else {
      this.setState({
        isAiShoulu: false,
      });
    }
    this.props.dispatch({
      type: 'info/getInfoList',
      payload: { keyword, pub_type, platform, status, currentPage: current, pageSize },
    });
  };

  pageChange = currentPage => {
    console.log(currentPage);
    if (this.state.status == 5) {
      this.setState({
        isAiShoulu: true,
      });
    } else {
      this.setState({
        isAiShoulu: false,
      });
    }
    const { keyword, pub_type, platform, status } = this.state;

    this.props.dispatch({
      type: 'info/getInfoList',
      payload: { keyword, pub_type, platform, status, currentPage },
    });
  };

  componentDidMount() {
    if (this.state.status == 5) {
      this.setState({
        isAiShoulu: true,
      });
    } else {
      this.setState({
        isAiShoulu: false,
      });
    }
    this.props.dispatch({ type: 'info/getInfoList', payload: {} });
  }

  handleChangeKey = e => {
    this.setState({
      keyword: e.target.value,
    });
  };

  search = () => {
    const { keyword, pub_type, platform, status } = this.state;
    if (this.state.status == 4) {
      this.setState({
        isShowMsg: true,
      });
    } else {
      this.setState({
        isShowMsg: false,
      });
    }
    if (this.state.status == 5) {
      this.setState({
        isAiShoulu: true,
      });
    } else {
      this.setState({
        isAiShoulu: false,
      });
    }
    this.props.dispatch({
      type: 'info/getInfoList',
      payload: { keyword, pub_type, platform, status, currentPage: 1 },
    });
  };

  showManual = () => {
    this.setState({
      isShowManual: true,
    });
  };

  handleFormReset = () => {
    this.setState({
      keyword: '',
      pub_type: '',
      platform: '',
    });
  };

  handleSelectModal = record => {
    const { dispatch } = this.props;
    const that = this;

    this.props.dispatch({
      type: 'info/setInfo',
      payload: record,
      callBack(result) {
        that.showManual();
      },
    });
  };

  handleFailed = record => {
    Modal.error({
      content: record.failed_reason,
      zIndex: 9999,
    });
  };

  render() {
    // In the fifth row, other columns are merged into first column
    // by setting it's colSpan to be 0
    const renderContent = (value, row, index) => {
      const obj = {
        children: value,
        props: {},
      };

      return obj;
    };

    const columns = [
      {
        title: '标题',
        dataIndex: 'name',
        render: renderContent,
      },

      {
        title: '发布日期',
        dataIndex: 'time',
        render: renderContent,
        width: 200,
      },

      {
        title: '发布平台',
        dataIndex: 'merchant_class',
        render: renderContent,
        width: 100,
      },
      {
        title: '发布来源',
        dataIndex: 'pubType',
        render: renderContent,
        width: 100,
      },
      {
        title: '状态',
        dataIndex: 'status',
        render: (text, record) => {
          if (record.status == 1) {
            return '等待推送';
          }
          if (record.status == 2) {
            return '推送成功';
          }
          if (record.status == 3) {
            return '审核未通过';
          }
          if (record.status == 4) {
            return '推送失败';
          }
        },
        width: 100,
      },
      {
        title: '操作',
        key: 'action',
        width: 100,
        render: (text, record) => (
          <span className={styles.aText}>
            {record.status === 2 && (
              <a href={record.res_url} target="_blank" rel="noreferrer">
                浏览
              </a>
            )}

            {record.status !== 2 && (
              <Button type="link" onClick={() => this.handleSelectModal(record)}>
                查看
              </Button>
            )}

            {record.status === 4 && (
              <Button type="link" onClick={() => this.handleFailed(record)}>
                原因
              </Button>
            )}
          </span>
        ),
      },
    ];

    const { InfoList = [], pagination = {} } = this.props;
    const paginationProps = {
      ...pagination,
      showSizeChanger: true,
      showQuickJumper: true,
      pageSizeOptions: ['10', '20', '30', '100'],
    };
    const { isOpenAi } = this.props;
    const { post_names } = this.props;
    console.log(InfoList);
    return (
      <div style={{ background: '#fff', padding: 24 }}>
        <div>
          <div className={styles.titleName}>发布历史</div>
          <Row>
            <div className={styles.itemView}>发布信息总数：{pagination.total}</div>
          </Row>
          <Row>
            <Col span={5}>
              <div className={styles.itemView}>
                关键字：
                <Input
                  placeholder="请输入"
                  value={this.state.keyword}
                  style={{ width: 120 }}
                  onChange={this.handleChangeKey}
                />
              </div>
            </Col>
            <Col span={5}>
              <div className={styles.itemView}>
                平台：
                <Select
                  defaultValue="全部"
                  style={{ width: 120 }}
                  value={this.state.platform}
                  onChange={this.handlePlatformChange}
                  getPopupContainer={triggerNode => triggerNode.parentNode}
                >
                  <Option value="-1">全部</Option>

                  {post_names.map((item, index) => (
                    <Option value={item.id}>{item.name}</Option>
                  ))}
                </Select>
              </div>
            </Col>
            <Col span={5}>
              <div className={styles.itemView}>
                来源：
                <Select
                  defaultValue="全部"
                  style={{ width: 120 }}
                  value={this.state.pub_type}
                  onChange={this.handleChange}
                  getPopupContainer={triggerNode => triggerNode.parentNode}
                >
                  <Option value="">全部</Option>
                  <Option value="manual">手动发布</Option>
                  <Option value="auto">自动发布</Option>
                </Select>
              </div>
            </Col>
            <Col span={5}>
              <div className={styles.itemView}>
                状态：
                <Select
                  defaultValue="全部"
                  style={{ width: 120 }}
                  value={this.state.status}
                  onChange={this.handleChangeStatus}
                  getPopupContainer={triggerNode => triggerNode.parentNode}
                >
                  <Option value="">全部</Option>
                  <Option value="1">等待推送</Option>
                  <Option value="2">推送成功</Option>
                  <Option value="4">推送失败</Option>
                </Select>
              </div>
            </Col>
            <Col span={4}>
              <div className={styles.itemView}>
                <span className={styles.submitButtons} style={{ marginLeft: '5px' }}>
                  <Button type="primary" onClick={this.search}>
                    查询
                  </Button>
                </span>
              </div>
            </Col>
          </Row>
          {/* <Col md={6} sm={24}>
            <div className={styles.itemView}>
            已发布<span style={{color:'#ED5B97',fontSize:'24px'}}>{InfoList.length}</span>条
            </div>
          </Col> */}
          <div className={styles.itemView}>
            {' '}
            特别说明：手动发布统计的数据是指用户通过客户端手动发布的信息量
          </div>
          {this.state.isShowMsg && (
            <Alert
              message="本失败信息不算在此客户发布信息总数里，仅供查找信息推送失败原因。"
              type="warning"
              showIcon
            />
          )}
          <div className={styles.tableView}>
            <ConfigProvider locale={zh_CN}>
              <Table
                columns={columns}
                dataSource={InfoList}
                bordered
                pagination={paginationProps}
                onChange={this.changePageSize}
              />
            </ConfigProvider>
          </div>
          <div></div>
        </div>
        {this.state.isShowManual && <Manual close={this.consoleManual} />}
      </div>
    );
  }
}

export default connect(({ info, home, merchant }) => ({ ...info, ...home, ...merchant }))(
  ProductListForm,
);
