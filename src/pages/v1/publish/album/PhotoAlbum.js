// @flow
import React, { PureComponent } from 'react';
import { connect } from 'dva';
import {
  Form,
  Table,

  Divider,
  Select,
  Row,
  Col,
  Button,
  AutoComplete,
  Input,
  Upload,
  Layout,
  message,
  Modal
} from 'antd';
import { CloseOutlined, CloudUploadOutlined ,ExclamationCircleOutlined} from '@ant-design/icons';
import 'antd/dist/antd.css';
import styles from './PhotoAlbum.less';

import MyAlbum from '../../../../components/MyAlbum';
import AddPhotoAlbum from '../../../../components/AddPhotoAlbum';
import UpdateAlbumPic from '../../../../components/UpdateAlbumPic';
import AlbumPhotoWall from '../../../../components/AlbumPhotoWall';

const { Option } = Select;
const columnCount = 8;
const { Header, Content } = Layout;
const { confirm } = Modal;

class PhotoAlbum extends PureComponent {
  state = {

    AlbumsList: [],

    isShowAddPicAlbum: false,
    isShowAddPhotoAlbum: false,
    type: 'add',
    isChange: false
  };
  componentDidMount() {
    const that = this;
    const {isChange}=this.state;
    this.props.dispatch({
      type: 'picture/getAlbumsList', payload: {}, callBack: (AlbumsList) => {

        that.setState({
          AlbumsList: AlbumsList,
          selId: AlbumsList[0].id,
          isChange:!isChange
        })
      }
    });


  }
  addPhotoAlbum = (item) => {
    const { isChange, type, selId } = this.state;
    const that = this;
    this.setState({

      isShowAddPhotoAlbum: false
    })

    if (type == "update") {
      this.props.dispatch({
        type: 'picture/updateAlbums', payload: { name: item, is_open: '1', id: selId }, callBack: (selList) => {
          that.props.dispatch({
            type: 'picture/getAlbumsList', payload: {}, callBack: (AlbumsList) => {

              that.setState({
                AlbumsList: AlbumsList,
                isChange: !isChange
              })
            }
          });
        }
      });
    } else {
      this.props.dispatch({
        type: 'picture/addAlbums', payload: { name: item, is_open: '1' }}).then(()=>{
        that.props.dispatch({
          type: 'picture/getAlbumsList', payload: {}, callBack: (AlbumsList) => {
            that.setState({
              AlbumsList: AlbumsList,
              isChange: !isChange
            })
          }
        });
      }).catch(err=>{
        message.error(err.msg || err)
      });
    }


  }
  ShowAddPhotoAlbum = () => {
    this.setState({
      type: 'add',
      isShowAddPhotoAlbum: true
    })
  }
  consoleAlertInput = () => {
    this.setState({
      isShowAddPhotoAlbum: false,
    })
  }

  ShowAddPicAlbum = () => {
    this.setState({
      type: 'add',
      isShowAddPicAlbum: true
    })
  }
  consoleAddPicAlbum = () => {
    this.setState({
      isShowAddPicAlbum: false,
    })
  }
  ShowAlbumPic = (item) => {
    const { id, name } = item;

    this.setState({
      selId: id,
      selName: name,
      isShowAlbumPic: true
    })
  }
  consoleAlbumPic = () => {
    this.setState({
      isShowAlbumPic: false,
    })
  }

  sliceArray = (array, size) => {
    var result = [];
    for (var x = 0; x < Math.ceil(array.length / size); x++) {
      var start = x * size;
      var end = start + size;
      result.push(array.slice(start, end));
    }
    return result;
  }
  ChangeName = (item) => {

    const { id, name } = item;
    this.setState({
      type: 'update',
      selId: id,
      selName: name,
      isShowAddPhotoAlbum: true
    })
  }
  delAlbum = (item) => {
    const { id } = item;
    const that = this;
    const {isChange}=this.state;
    confirm({
      title: '删除相册',
      icon: <ExclamationCircleOutlined />,
      content: '删除相册后，相册里的图片将转移到默认相册，确定删除？',
      okText: '确定',
      okType: 'danger',
      cancelText: '取消',
      onOk() {
        that.props.dispatch({
          type: 'picture/DeleteAlbums', payload: { is_open: '0', id: id }, callBack: (selList) => {
            that.props.dispatch({
              type: 'picture/getAlbumsList', payload: {}, callBack: (AlbumsList) => {

                that.setState({
                  AlbumsList: AlbumsList,
                  isChange: !isChange
                })
              }
            });
          }
        });
      },
      onCancel() {
        console.log('Cancel');
      },
    });

  }


  render() {
    const { AlbumsList = [], selId = 0 } = this.state;

    let list = this.sliceArray(AlbumsList, columnCount);
    return (
      <div style={{ background: '#fff', padding: 24 }}>
        <div>
          <div className={styles.titleName}>相册管理</div>
          <div className={styles.itemView}>
            <Row>
              <Col><div style={{ marginLeft: '5px', marginBottom: '5px' }}>  <Button type="primary" onClick={this.ShowAddPhotoAlbum} >新建相册</Button></div></Col>
              <Col><div style={{ marginLeft: '5px', marginBottom: '5px' }}>  <Button type="primary" onClick={this.ShowAddPicAlbum} >上传图片</Button></div></Col>
            </Row>

          </div>
          <div className={styles.cityChexBox} style={{ height: '100%' }}>
            <Row style={{ justifyContent: 'start' }}>

              {AlbumsList.map((item, index) => {
                return <Col key={item.name+index} >
                  <MyAlbum
                    ChangeName={this.ChangeName}
                    delAlbum={this.delAlbum}
                    openAlbum={this.ShowAlbumPic}
                    albums={item}
                    index={index}
                  >
                  </MyAlbum> </Col>
              })}
            </Row>
          </div>
        </div>
        {this.state.isShowAddPhotoAlbum && <AddPhotoAlbum close={this.consoleAlertInput} type={this.state.type} value={this.state.selName} onSaveBtn={(item) => { this.addPhotoAlbum(item) }} />}
        {this.state.isShowAddPicAlbum && <UpdateAlbumPic close={this.consoleAddPicAlbum} value={selId} />}
        {this.state.isShowAlbumPic && <AlbumPhotoWall close={this.consoleAlbumPic} value={selId}  />}

      </div>
    );
  }
}

export default connect(({ picture }) => ({
  ...picture
}))(PhotoAlbum);
