// @flow
import React, { PureComponent } from 'react';
import { connect } from 'dva';
import {
  Form,
  Table,
  Config<PERSON><PERSON>ider,
  Divider,
  Select,
  Row,
  Col,
  Button,
  AutoComplete,
  Input,
  Layout,
  Modal,
  Progress
} from 'antd';
import { CloseOutlined } from '@ant-design/icons';
import 'antd/dist/antd.css';
import zh_CN from 'antd/lib/locale-provider/zh_CN'
import styles from './Ranking.less';

const { Option } = Select;

const { Header, Content } = Layout;

 class RankingList extends PureComponent {
  state = {
    status:0,
  };
  handleChange=(value)=> {
    this.setState({
      pub_type:value
    })
  }

  componentDidMount(){
    console.log( this.props)
    this.props.dispatch({ type:'ranking/getRanks', payload: {status:0}});
  }

  search=()=> {
    const {keyword,pub_type}=this.state;
  }

  handleChangeStatus=(value)=> {
    this.setState({
      status:value
    })
  }
  changePageSize=(content)=>{
    console.log(content)
    const {rankList=[],pagination={},rankMode={} }=this.props;
    const {pageSize}=content;
    const {status}=this.state;
    this.props.dispatch({ type: 'ranking/getRanks', payload: {status,currentPage:pagination.current,pageSize}});
  }
  pageChange=(currentPage)=>{
    const {status}=this.state;
    this.props.dispatch({ type: 'ranking/getRanks', payload: {status,currentPage}});
  }
  search=()=> {
    const {status}=this.state;
    this.props.dispatch({ type: 'ranking/getRanks', payload: {status,currentPage:1},callBack:function(rankList){
        if(!rankList||rankList.length==0){
          Modal.info({
            content: '暂无排名信息',
            zIndex:9999
          });
        }
    }});
  }

  handleChangeStatus=(value)=> {
    this.setState({
      status:value
    })
  }
  render() {
    const renderContent = (value, row, index) => {
      const obj = {
        children: value,
        props: {},
      };

      return obj;
    };

const columns = [
  {
    title: '关键词',
    dataIndex: 'keyword',
    render: renderContent,
  },
  {
    title: '搜索引擎',
    dataIndex: 'engine',
    render: renderContent,
    width: 200,
  },
  {
    title: '位置',
    dataIndex: 'page',
    render: renderContent,
    width: 200,
  },
  {
    title: '操作',
    key: 'action',
    width: 100,
    render: (text, record) => (
      <span className={styles.aText}>
          {<Button type="link" onClick={()=> {
            window.open(record.url, '_blank');
          }}>查看快照</Button>}

      </span>
    ),
  },
];

const {rankList=[],pagination={},rankMode={} }=this.props;
const paginationProps = {
  ...pagination,
  showSizeChanger: false,
  showQuickJumper: true,
  pageSizeOptions:['10', '20', '30','100'],

  onChange:this.pageChange
};


    return (
      <div  style={{ background:'#fff', padding: 24  }}>
        <div >
          <div className={styles.titleName}>排名统计</div>
          <Row  type="flex"  justify="space-around"  gutter={16}  align="middle">
          <Col span={6} >
          <div style={{display:'flex',flexDirection:'column',justifyContent:'center',textAlign:'center',height:'80px' ,backgroundColor:'#F9976B',borderRadius:'5px',margin:'20px'}}>
              <div style={{color:'#ffffff',fontSize:'28px'}}>{rankMode.total}</div>
              <div style={{color:'#ffffff',marginTop:'3px'}}>总排名数量</div>
            </div>
         </Col>
         <Col span={6}>
         <div style={{display:'flex',flexDirection:'column',justifyContent:'center',textAlign:'center',height:'80px' ,backgroundColor:'#E6E6E6',borderRadius:'5px',margin:'20px'}}>
              <div style={{color:'#888888',fontSize:'28px'}}>{rankMode.productCount}</div>
              <div style={{color:'#888888',marginTop:'3px'}}>推广产品数</div>
            </div>

          </Col>
          <Col span={6}>
          <div style={{display:'flex',flexDirection:'column',justifyContent:'center',textAlign:'center',height:'80px' ,backgroundColor:'#E6E6E6',borderRadius:'5px',margin:'20px'}}>
              <div style={{color:'#888888',fontSize:'28px'}}>{rankMode.infoCount}</div>
              <div style={{color:'#888888',marginTop:'3px'}}>发布信息数</div>
            </div>
          </Col>
          <Col span={6}>
          {/* <div style={{display:'flex',flexDirection:'column',justifyContent:'center',textAlign:'center',height:'80px' ,backgroundColor:'#F9976B',borderRadius:'5px',margin:'20px'}}>
              <div style={{color:'#ffffff',fontSize:'28px' }}>刷新排名</div>
            </div>
             */}
          </Col>
        </Row>
        <Row>
        <Col md={7} sm={24}>
            <div className={styles.lableView}>
          收录端：
            <Select defaultValue="全部" style={{ width: 120 }} value={this.state.status} onChange={this.handleChangeStatus}
            getPopupContainer={triggerNode => triggerNode.parentNode}
            >
               <Option value={0}>全部</Option>
              <Option value={1}>百度</Option>
              <Option value={2}>360</Option>
              <Option value={3}>搜狗 </Option>
              <Option value={4}>神马</Option>
              <Option value={5}>头条</Option>
            </Select>
            </div>

          </Col>
          <Col md={3} sm={24}>
            <div className={styles.lableView}>
            <span className={styles.submitButtons}>
              <Button type="primary" onClick={this.search}>
                查询
              </Button>
            </span>
            </div>
          </Col>
          </Row>
        <div className={styles.tableView}>
        <ConfigProvider  locale={zh_CN}>
          <Table columns={columns} dataSource={rankList} bordered
            pagination={paginationProps}

            />
               </ConfigProvider >
          </div>
      </div>
      </div>
   );
  }
}

export default connect(({ranking,home}) => ({...ranking,...home
}))(RankingList);
