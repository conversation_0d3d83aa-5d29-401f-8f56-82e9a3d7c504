import React, { PureComponent } from 'react';
import { connect } from 'dva';
// import { ConfigProvider, Table, Input } from 'antd';
import { Button, Col, Input, Modal, Row } from 'antd';
import styles from './AiChatGPT.less';
import { baseUrl } from '@/configs/config';

const { TextArea } = Input;

const { confirm } = Modal;

class AiChatGpt extends PureComponent {
  state = {
    tip: {
      total: 0,
      num: 0,
      searched: false,
    },
    history: [], // 用本地的localstorage
    examples: [
      {
        label: '学习',
        text: '圆周率第88位是多少？',
      },
      {
        label: '编程',
        text: `优化代码，简化代码:"func insertionSort(nums [int)[int {if len(nums)<= 1{
return nums
for i := 0; i < len(nums); i++{
//每次从未排序区间取一个数据valuevalue
:= nums[i]
在已排序区间找到插入位置j :=i- 1
for; j >= 0; j--{
//如果比 value大后移if
nums[j]> value
Unknown macro: {nums[j+1]= nums[j]}
else
Unknown macro: { break}
}
//插入数据valuenums[j+1]=
value
return nums
}”`,
      },
      {
        label: '数据分析',
        text: '写个excel公式,如果单元格是空，返回空，如果非空，返回1',
      },
      {
        label: '文案',
        text: '写出5段关于AI智能的介绍',
      },
      {
        label: '翻译',
        text: '勿忘初心翻译成英文',
      },
      {
        label: '总结',
        text:
          '一句话描述这段摘要：科研团队对我国西北地区约1.7亿年前的一种侏罗纪远古植物化石进行了重新研究。这种植物此前被认为是裸子植物，名为美丽镰鳞果。最新研究中，科研团队运用显微CT技术对这种古植物化石进行扫描，发现化石内部包含有双层珠被的倒生胚珠，这是判断被子植物的关键特征。基于此，科研团队判断这是一种远古被子植物。由于化石中展现的是这种植物的多个相连果实，科研团队将其重新命名为美丽青甘宁果序（ingganninginfructus formosa）。',
      },
    ], // 案例
    conversations: [
      { role: 'AI', class: 'ai', text: '您好，我是ChatGPT智能问答机器人，您可以向我提问' },
    ],
    kw: '',
    loading: false,
    selected: '',
  };

  // 用于获取需要滚动的元素的引用
  messagesEndRef = React.createRef();

  componentDidMount() {}

  onSaveBtn = () => {
    this.props.onSave(this.state.selected);
    this.notifyClose();
  };

  didSearch = () => {
    this.doSearch({ kw: this.state.kw });
  };

  doSearch = payload => {
    this.setState({ loading: true });
    this.state.conversations.length = 0;
    this.state.conversations.push({ role: '我', class: 'user', text: payload.kw });
    let i = 0;
    const thinking = setInterval(() => {
      this.state.conversations.length = 1;
      this.state.conversations.push({
        role: 'AI',
        class: 'ai',
        text: `思考中${'.'.repeat(i + 1)}`,
      });
      i = (i + 1) % 3;
      const conversations = [].concat(...this.state.conversations);
      this.setState({ conversations });
    }, 500);
    this.props
      .dispatch({
        type: 'chat/ask',
        payload,
      })
      .then(data => {
        clearInterval(thinking);

        this.state.conversations.length = 1;
        if (data.data.content != '' && data.data.content != 'false') {
          this.state.conversations.push({
            role: 'AI',
            class: 'ai',
            text: data.data.content,
          });
          const conversations = [].concat(...this.state.conversations);
          const { tip } = this.state;
          tip.searched = true;
          tip.total = data.data.user_maxnum;
          tip.num = data.data.user_maxnum - data.data.user_total;
          this.setState({ searched: true, kw: '', conversations, tip });

          this.state.history.unshift({
            q: { role: 'user', text: payload.kw },
            a: {
              role: 'ai',
              text: data.data.content,
            },
          });

          let history = [].concat(...this.state.history);
          if (history.length > 10) {
            history = history.slice(0, 10);
          }
          this.setState({ searched: true, kw: '', loading: false, history });
        } else {
          this.state.history.unshift({
            q: { role: 'user', text: payload.kw },
            a: {
              role: 'ai',
              text: '不太理解你的问题，请换个方式提问。',
            },
          });
        }
      })
      .catch(e => {
        console.log(e);
        clearInterval(thinking);
        let content = e.msg || e.message;
        if (content.includes('超限额')) {
          content += '，点击购买';
          Modal.warning({
            title: '提示',
            content: content,
            zIndex: 9999,
            onOk: () => {
              window.open('https://fafa.huangye88.com/tool/AIpay.html', '_blank');
            },
            okText: '去购买',
          });
        } else {
          Modal.warning({ title: '提示', content: content, zIndex: 9999 });
        }

        this.setState({ loading: false });
      });
  };

  doStreamSearch = payload => {
    this.setState({ loading: true });
    this.state.conversations.length = 0;
    this.state.conversations.push({ role: '我', class: 'user', text: payload.kw });
    let i = 0;
    const thinking = setInterval(() => {
      this.state.conversations.length = 1;
      this.state.conversations.push({
        role: 'AI',
        class: 'ai',
        text: `思考中${'.'.repeat(i + 1)}`,
      });
      i = (i + 1) % 3;
      const conversations = [].concat(...this.state.conversations);
      this.setState({ conversations });
    }, 500);
    const that = this;
    fetch(`${baseUrl}/tools/chat/stream/ask?kw=${encodeURIComponent(payload.kw)}`, {
      method: 'GET',
      headers: {
        Authorization: `Bearer ${window.sessionStorage.getItem('Authorization')}`,
      },
    })
      .then(response => response.body)
      .then(async body => {
        clearInterval(thinking);

        let text = '';
        let index = 0;
        let hasRead = false;
        const writer = setInterval(() => {
          if (index < text.length) {
            that.state.conversations.length = 1;
            const newline = text.substring(0, ++index);
            that.state.conversations.push({
              role: 'AI',
              class: 'ai',
              text: newline,
            });
            const conversations = [].concat(...that.state.conversations);
            that.setState({ searched: true, kw: '', conversations });
            if (newline.endsWith('\n') || index % 30 == 0) {
              that.scrollToBottom();
            }
          } else if (hasRead) {
            clearInterval(writer);
          }
        }, 100);
        const reader = body.getReader();
        while (true) {
          const { value, done } = await reader.read();
          if (done) break;
          const trunk = new TextDecoder('utf-8').decode(value);
          console.log(trunk);
          const lines = trunk.split('0\r\n');
          text += lines[0];
          try {
            const o = JSON.parse(text);
            if (o && o.code && o.msg) {
              text = '不太理解你的问题，请换个方式提问。';
              break;
            }
          } catch (e) {
            if (lines.length > 1) {
              break;
            }
          }
        }
        hasRead = true;

        that.state.history.unshift({
          q: { role: 'user', text: payload.kw },
          a: {
            role: 'ai',
            text: text || '不太理解你的问题，请换个方式提问。',
          },
        });
        const history = [].concat(...that.state.history);
        that.setState({ searched: true, kw: '', loading: false, history });
        that.scrollToBottom();
      })
      .catch(e => {
        clearInterval(thinking);
        //  <a href='https://fafa.huangye88.com/tool/AIpay.html' target='_blank'>充值</a>
        let content = e.msg || e.message;
        if (content.includes('超限额')) {
          content +=
            "，去<a href='https://fafa.huangye88.com/tool/AIpay.html' target='_blank'>购买</a>";
        }
        Modal.warning({ title: '提示', content: content, zIndex: 9999 });
        that.setState({ loading: false });
      });
  };

  loadChatHistories = () => {
    this.props
      .dispatch({
        type: 'chat/chatHistories',
        payload: { type: 0 },
      })
      .then(data => {
        const history = data.data.map(item => item.keyword);
        this.setState({ history });
      })
      .catch(e => {
        Modal.warning({ title: '提示', content: e.msg, zIndex: 9999 });
      });
  };

  notifyClose = () => {
    this.props.close();
  };

  selectQuestion = q => () => {
    this.setState({
      kw: q,
    });
  };

  showHistory = item => () => {
    this.setState({
      conversations: [item.q, item.a],
    });
  };

  handleChangeKey = e => {
    this.setState({
      kw: e.target.value,
    });
  };

  // 在 PureCompoent 中不需要再次定义 constructor

  pageChange = page => {
    this.doSearch({ kw: this.state.kw });
  };

  // 用于滚动到元素底部
  scrollToBottom = () => {
    this.messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
  };

  render() {
    const { history = [], kw, loading, examples, tip, conversations } = this.state;
    // 监听内容变化，滚动到元素底部
    return (
      <div className={styles.content}>
        <Row>
          <Col span={16}>
            <Row>
              <Col span={24}>
                <div className={styles.conversation}>
                  {conversations.map(item =>
                    item.class == 'ai' ? (
                      <div>
                        <span className={styles.ai}>{item.role}</span>
                        <p className={styles.ai}>{item.text}</p>
                      </div>
                    ) : (
                      <div>
                        <span className={styles.user}>{item.role}</span>
                        <p className={styles.user}>{item.text}</p>
                      </div>
                    ),
                  )}
                  <div className={styles.bottom} ref={this.messagesEndRef} />
                </div>
              </Col>
            </Row>
            <Row>
              <Col span={20}>
                <div className={styles.border}>
                  <TextArea
                    placeholder="请输入问题"
                    rows={3}
                    value={this.state.kw}
                    onChange={this.handleChangeKey}
                  />
                </div>
              </Col>
              <Col span={4}>
                <Button
                  type="primary"
                  onClick={this.didSearch}
                  loading={loading}
                  className={styles.ask}
                >
                  发送
                </Button>
              </Col>
            </Row>
            <Row>
              {tip.searched && (
                <Row className={styles.searchtip}>
                  总次数<span>{tip.total}次，</span>还可提问<span>{tip.num}</span>次，次数不够？点击
                  <a href="https://fafa.huangye88.com/tool/AIpay.html" target="_blank">
                    充值
                  </a>
                </Row>
              )}
            </Row>
            <Row>
              <Col span={24}>
                <h1 className={styles.tip}>Ai机器人可以做什么，点击查看例句</h1>
                <div>
                  {examples.map(item => (
                    <div className={styles.examples} onClick={this.selectQuestion(item.text)}>
                      <span className={styles.dot}></span>
                      <span className={styles.label}>{item.label}:</span>
                      <span className={styles.text}>{item.text}</span>
                    </div>
                  ))}
                </div>
              </Col>
            </Row>
          </Col>
          <Col span={8}>
            <div className={styles.history}>
              <h1 className={styles.title}>历史聊天记录</h1>
              <ul>
                {history.map(item => (
                  <li onClick={this.showHistory(item)}>{item.q.text}</li>
                ))}
              </ul>
            </div>
          </Col>
        </Row>
      </div>
    );
  }
}

export default connect(({}) => ({}))(AiChatGpt);
