.content {
  background-color: #ffffff;
}

.conversation {
  margin-top: 2px;
  height: 300px;
  overflow: scroll;
  width: 100%;
  background-color: #efefef;
  border: 1px solid #e6e6e6;
  padding: 10px;
}

.conversation div {
  margin: 5px;
  padding: 10px;
  overflow: hidden;
}

span.ai,
span.user {
  background-color: #001529;
  font-size: 18px;
  font-family: 'Microsoft YaHei';
  color: #ffffff;
  width: 36px;
  height: 36px;
  line-height: 36px;
  text-align: center;
  display: inline-block;
}
span.ai {
  float: left;
}
span.user {
  float: right;
}
p.ai,
p.user {
  display: inline-block;
  border-radius: 5px;
  background-color: #ffffff;
  font-size: 16px;
  padding: 5px 5px;
  //line-height: 36px;
  max-width: 90%;
  font-family: 'Microsoft YaHei';
  color: #333333;
  white-space: pre-line;
}
p.ai {
  float: left;
  margin-left: 5px;
}
p.user {
  float: right;
  margin-right: 5px;
}
.conversation div.bottom {
  padding: 0px;
}

.border {
  border: 1px solid #1890ff;
  background-color: #ffffff;
}

.examples {
  background-color: #f5faff;
  border-radius: 5px;
  height: 44px;
  line-height: 44px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  margin: 0px 5px;
  max-width: 440px;
  display: inline-block;
  padding: 0px 5px;
}

.examples span.label {
  font-weight: bold;
  font-size: 16px;
  font-family: 'Microsoft YaHei';
  color: #001529;
  padding: 5px;
}

.examples span.text {
  font-size: 14px;
  font-family: 'Microsoft YaHei';
  color: #333333;
}

h1.title {
  font-size: 16px;
  font-family: 'Microsoft YaHei';
  background-color: #a2b9d0;
  color: #ffffff;
  height: 44px;
  line-height: 44px;
  padding-left: 10px;
}

.history {
  background-color: #ffffff;
  height: 100%;
  max-height: 800px;
  padding: 10px;
  overflow: auto;
}

.history ul {
  list-style-type: none;
  padding: 0;
  background-color: #f5faff;
  margin: 0px;
}

.history li {
  width: 95%;
  overflow: hidden;
  font-size: 14px;
  font-family: 'Microsoft YaHei';
  color: #6385a7;
  white-space: nowrap;
  text-overflow: ellipsis;
  display: inline-block;
  height: 50px;
  line-height: 50px;
  margin: 0px 10px;
  border-bottom: 1px solid #e0efff;
  box-sizing: border-box;
}

.ask {
  position: absolute;
  left: 0px;
  bottom: 0px;
  font-size: 22px;
  font-family: 'Microsoft YaHei';
  color: #ffffff;
  width: 100%;
  height: 100%;
  background-color: #1890ff;
  border-radius: 5px;
  padding: 0px;
}

.tip {
  font-size: 18px;
  font-weight: 600;
  height: 48px;
}
.dot {
  width: 8px;
  height: 8px;
  display: inline-block;
  background-color: #a2b9d0;
  border-radius: 5px;
}

.searchtip {
  line-height: 32px;
}
