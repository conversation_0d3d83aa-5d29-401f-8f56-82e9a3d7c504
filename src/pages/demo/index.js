import React, { PureComponent } from 'react';
import { connect } from 'dva';
import MyHeader from '@/components/Header';
import AlbumList from '@/components/AlbumList';
import LeftMenu from '@/components/LeftMenu';
import PopAddalbum from '@/components/PopAddalbum'
import PhotoManage from '@/pages/v2/publish/album/PhotoManage'
import DeleteTip from '@/components/DeleteTip'
import MovephotoTip from '@/components/MovephotoTip'
import PageComtent from '@/components/PageComtent'
import PopAlteralbum from '@/components/PopAlteralbum'
import Cascader from '@/components/Cascader'
import DatePicker from '@/components/DatePicker'
import Upload from '@/components/Upload'
import UploadFile from '@/components/UploadFile'
import RankSelect from '@/components/RankSelect'
import RankTable from '@/components/RankTable'
import Error from '@/components/Error'
import HistoryTable from '@/components/HistoryTable'
import ViewInfo from '@/components/ViewInfo'

class IndexDemo extends PureComponent {
  state = {
  };

  render() {
    return <div>
      <h1>通用头</h1>
      <MyHeader></MyHeader>
      <h1>相册列表</h1>
      <AlbumList></AlbumList>
      <h1>左侧菜单(用不上，自动生成即可）</h1>
      <LeftMenu></LeftMenu>
      <h1>添加相册</h1>
      <PopAddalbum></PopAddalbum>
      <h1>图片管理</h1>
      <PhotoManage></PhotoManage>
      <h1>删除提示</h1>
      <DeleteTip></DeleteTip>
      <h1>移动提示</h1>
      <MovephotoTip></MovephotoTip>
      <h1>分页</h1>
      <PageComtent></PageComtent>
      <h1>修改相册</h1>
      <PopAlteralbum></PopAlteralbum>
      <h1>连动选择</h1>
      <Cascader></Cascader>
      <h1>日期选择</h1>
      <DatePicker></DatePicker>
      <h1>文件上传</h1>
      <Upload></Upload>
      <h1>文件上传2</h1>
      <UploadFile></UploadFile>
      <RankSelect></RankSelect>
      <RankTable></RankTable>
      <Error></Error>
      <HistoryTable></HistoryTable>
      <ViewInfo></ViewInfo>
    </div>;
  }
}

export default connect(() => ({}))(IndexDemo);
