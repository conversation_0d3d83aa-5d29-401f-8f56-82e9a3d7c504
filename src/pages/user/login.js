import React from 'react';
import { connect } from 'dva';
import jwt_decode from 'jwt-decode';
import { Button, Row, Form, Input, Checkbox, Select, Modal, Col, message } from 'antd';

import router from 'umi/router';
import styles from './login.less';
import AesUtil from '../../utils/AesUtil';
import AlertToast from '../../components/AlertToast';
import AlertRegister from '../../components/AlertRegister';
import BindKuyiso from '@/components/BindKuyiso';

const FormItem = Form.Item;
const { Option } = Select;

const { confirm } = Modal;
class Login extends React.Component {
  formRef = React.createRef();

  state = {
    mobiles: '',
    Users: {},
    isRemember: false,
    isShowAlert: false,
    isShowRegister: false,
    modalVisible: false,
    cid: 0,
  };

  componentDidMount() {
    this.getCookies();
  }

  unique = arr => {
    if (!Array.isArray(arr)) {
      console.log('type error!');
      return;
    }
    const res = [arr[0]];
    for (let i = 1; i < arr.length; i++) {
      let flag = true;
      for (let j = 0; j < res.length; j++) {
        if (arr[i] === res[j]) {
          flag = false;
          break;
        }
      }
      if (flag) {
        res.push(arr[i]);
      }
    }
    return res;
  };

  getCookies = () => {
    const that = this;

    const mobile = window.localStorage.getItem('mobile');
    const isRemember = window.localStorage.getItem('isRemember');
    let mobiles;
    if (mobile) {
      mobiles = mobile.split('-');
    }

    if (isRemember == 'true') {
      that.setState({
        isRemember: true,
      });
    } else {
      that.setState({
        isRemember: false,
      });
    }
    console.log(mobiles);

    if (mobiles != null && mobiles.length > 0) {
      const result = this.unique(mobiles);
      that.setState({
        mobiles: result,
      });
    } else {
      that.setState({
        mobiles,
      });
    }

    if (mobiles && mobiles.length > 0) {
      for (let i = 0; i < mobiles.length; i++) {
        const { Users } = that.state;
        const password = AesUtil.decrypt(window.localStorage.getItem(mobiles[i]));
        Users[mobiles[i]] = password;
        that.setState({
          Users,
        });
      }
    }
  };

  /**
   * 保存cookie
   * @param name  cookie名称
   * @param value cookie值
   */
  setCookie = (name, value) => {
    window.localStorage.setItem(name, value);
  };

  /**
   * 清除cookie
   * @param name  cookie名称
   */
  clearCookie = name => {
    console.log(name);
    window.localStorage.removeItem(name);
    const mobile = window.localStorage.getItem('mobile');
    console.log(mobile);
  };

  handleOpenModal = () => {
    this.setState({
      modalVisible: true,
    });
  };

  handleOk = values => {
    const that = this;
    const { isRemember } = this.state;

    that.props
      .dispatch({ type: 'home/login', payload: values })
      .then(data => {
        const { mobiles = [] } = that.state;
        let mobilesStr = '';
        for (let i = 0; i < mobiles.length; i++) {
          if (i == 0) {
            mobilesStr += mobiles[i];
          } else {
            mobilesStr = `${mobilesStr}-${mobiles[i]}`;
          }
        }
        if (mobiles.length > 0 && values.mobile.indexOf(mobilesStr) == -1) {
          mobilesStr = `${mobilesStr}-${values.mobile}`;
        } else {
          mobilesStr = values.mobile;
        }

        const passwordAES = AesUtil.encrypt(values.password);
        if (mobiles.indexOf(values.mobile) == -1) {
          that.setCookie('mobile', mobilesStr);
        }

        if (isRemember) {
          that.setCookie(values.mobile, passwordAES);
        } else {
          that.setCookie(values.mobile, '');
        }
        that.setCookie('isRemember', `${isRemember}`);
        // eslint-disable-next-line @typescript-eslint/camelcase
        const { access_token, exp } = data;

        const tokenData = jwt_decode(access_token);
        window.sessionStorage.setItem('Authorization', access_token);
        window.sessionStorage.setItem('exp', exp);
        if (tokenData.shouldBindKuyiso) {
          this.setState({ cid: tokenData.cid });
          this.handleOpenModal();
        } else {
          message.success('登陆成功');
          window.sessionStorage.setItem('isLogin', true);
          router.push({ pathname: '/' });
        }
      })
      .catch(error => {
        if (error.msg === '该用户已经过期，请联系客服。') {
          that.setState({
            isShowAlert: true,
          });
        } else if (error.msg === '手机号码不存在') {
          console.log('status');
          that.setState({
            isShowRegister: true,
          });
        } else if (error.msg === '该VIP已经过期，请联系客服。') {
          message.error('该VIP账号使用权限已过期，请联系对接客服。');
        } else {
          // 错误提示包含升级，就提示升级
          if (error.msg.indexOf('升级') > -1) {
            Modal.warning({
              title: '提示',
              content: error.msg,
            });
          } else {
            message.error(error.msg);
          }
        }
      });
  };

  rememberPassword = e => {
    const isRemember = e.target.checked;
    this.setState({
      isRemember,
    });
  };

  selectUser = value => {
    const val = this.refs.lowlevelinput.input.value;

    this.formRef.current.setFieldsValue({
      mobile: val,
    });
    const { mobiles = [], Users } = this.state;
    for (let i = 0; i < mobiles.length; i++) {
      if (mobiles[i] == val) {
        const password = Users[val];

        this.formRef.current.setFieldsValue({
          password,
        });
      }
    }
  };

  consoleAlert = () => {
    this.setState({
      isShowAlert: false,
      isShowRegister: false,
    });
  };

  onGoLogin = () => {
    this.setState({
      isShowAlert: false,
      isShowRegister: false,
    });
  };

  onShowTip = () => {
    this.setState({
      isShowAlert: false,
    });
    confirm({
      content: '该账号的發發助手已到期，请续费后登陆。',
      zIndex: 9999,
      okText: '立即续费',
      cancelText: '取消',
      onOk() {
        window.open('http://fafa.huangye88.com/tool/', '_blank');
      },
      onCancel() {
        console.log('Cancel');
      },
    });
  };

  openMembership = () => {
    // this.props.dispatch({ type: 'home/getUrl', payload: {
    //   url:"http://my.huangye88.com/item/tool/",
    // },callBack:(url)=>{
    // }});
    window.open('http://my.huangye88.com/item/tool/', '_blank');
  };

  openRegister = () => {
    // this.props.dispatch({ type: 'home/getUrl', payload: {
    //   url:"http://my.huangye88.com/item/tool/",
    // },callBack:(url)=>{
    // }});
    window.open('https://my.huangye88.com/newmember.html', '_blank');
  };

  openFindPassWord = () => {
    // this.props.dispatch({ type: 'home/getUrl', payload: {
    //   url:"http://my.huangye88.com/item/tool/",
    // },callBack:(url)=>{
    // }});
  };

  handleCloseModal = () => {
    this.setState({
      modalVisible: false,
    });
    const { token, exp } = this.state;
    window.sessionStorage.setItem('isLogin', true);
    router.push({ pathname: '/' });
  };

  render() {
    const {
      mobiles = [],
      Users,
      isRemember,
      isShowAlert,
      isShowRegister,
      modalVisible,
    } = this.state;

    return (
      <div className={styles.form}>
        <div className={styles.logo}>
          <span>發發助手</span>
        </div>
        <Form onFinish={this.handleOk} ref={this.formRef}>
          <FormItem
            label="账号"
            name="mobile"
            rules={[{ required: true, message: '请输入手机号！' }]}
          >
            <div>
              {' '}
              <Input
                list="username_list"
                onChange={this.selectUser}
                placeholder="黄页88登陆手机号"
                ref="lowlevelinput"
              />
              <datalist id="username_list">
                {mobiles.length > 0 &&
                  mobiles.map((item, idx) => <option value={item} key={idx} />)}
              </datalist>
            </div>
          </FormItem>
          <FormItem
            hasFeedback
            label="密码"
            name="password"
            rules={[{ required: true, message: '请输入登录密码！' }]}
          >
            <Input size="large" type="password" onPressEnter={this.handleOk} placeholder="密码" />
          </FormItem>
          <Row>
            <Col span={10}>
              {' '}
              <Checkbox
                onChange={this.rememberPassword}
                style={{ marginBottom: '5px' }}
                checked={isRemember}
                defaultChecked={isRemember}
              >
                记住密码
              </Checkbox>
            </Col>
            <Col span={8} offset={6}>
              {' '}
              <a
                size="small"
                href="http://my.huangye88.com/findpwd/"
                target="_blank"
                rel="noreferrer"
              >
                忘记密码
              </a>
            </Col>
          </Row>

          <FormItem>
            <Row>
              <Button type="primary" htmlType="submit" size="large">
                立即登录
              </Button>
            </Row>
          </FormItem>

          {this.state.isShowAlert && (
            <AlertToast
              close={this.consoleAlert}
              btnContent="立即开通"
              content="该账号未开通發發助手"
              onGoLogin={this.onGoLogin}
              onShowTip={this.onShowTip}
              onBtnClick={this.openMembership}
            />
          )}
          {this.state.isShowRegister && (
            <AlertRegister
              close={this.consoleAlert}
              btnContent="立即注册"
              content="该手机号未注册账号"
              onGoLogin={this.onGoLogin}
              onBtnClick={this.openMembership}
            />
          )}
          {modalVisible && <BindKuyiso refresh={this.handleCloseModal} />}
        </Form>
      </div>
    );
  }
}

export default connect(({ home }) => ({ ...home }))(Login);
