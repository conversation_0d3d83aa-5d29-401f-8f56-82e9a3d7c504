import React, { PureComponent } from 'react';
import { connect } from 'dva';
import { Col, ConfigProvider, Dropdown, Layout, Menu, message, Modal, Progress, Row } from 'antd';
import {
  BarsOutlined,
  ExclamationCircleOutlined,
  LogoutOutlined,
  QuestionOutlined,
} from '@ant-design/icons';
import zhCN from 'antd/lib/locale/zh_CN';
import moment from 'moment';
import styles from './Indexpage.less';
import Home from '../v1/home/<USER>';
import Release from '../../components/Release';
import AiRobots from '../../components/AiRobots';
import CompanyDetail from '../v1/company/detail/CompanyDetail';
import CompanyDetailAi from '../v1/company/aicaigou/CompanyDetailAi';

import EditProductDetail from '../v1/publish/products/EditProductDetail';
import Manual from '../../components/Manual';
import ProductList from '../v1/publish/products/ProductList';
import HistoryList from '../v1/publish/history/HistoryList';
import ShopList from '../v1/publish/b2b/ShopList';
import Ranking from '../v1/publish/ranking/Ranking';
import Seek from '../../components/Seek';
import FunctionList from '../../components/functionList';
import PhotoAlbum from '../v1/publish/album/PhotoAlbum';

import 'moment/locale/zh-cn';
import getToken from '@/utils/login';

// const { remote } = require('electron')
// remote.getCurrentWebContents().openDevTools();
const { Header, Content, Footer } = Layout;
const Inquire_Type_Include = 1;
const Inquire_Type_Ranking = 2;
let conn;
const { confirm } = Modal;

class Indexpage extends PureComponent {
  state = {
    selKey: ['1'],
    isShowCompanyDetail: false,
    isShowProductDetail: false,
    isShowEditProductDetail: false,
    isShowManual: false,
    isShowProductList: false,
    isShowHistoryList: false,
    isShowShopList: false,
    isShowRanking: false,
    isShowSeek: false,
    isShowKeyWord: false,
    onlyShow: false,
    isShowIncludeProgress: true,
    isShowIRankingProgress: true,
    isquit: false,
    InquireType: Inquire_Type_Include,
    isShowFunction: false,
    isShowPhotoAlbum: false,
    isShowAi: false,
  };

  componentDidMount() {
    this.props.dispatch({
      type: 'home/load',
      payload: {},
    });
    this.props.dispatch({ type: 'home/getMyAllCate', payload: {} });
    this.props.dispatch({ type: 'home/getCity', payload: {} });
    moment.locale('zh-cn');
    if (getToken()) {
      this.props.dispatch({ type: 'merchant/queryMerchant', payload: {} });
    }
    const that = this;
  }

  mmenuClick = item => {
    const { key } = item;
    this.setState({
      selKey: [key],
    });
  };

  consoleCompany = () => {
    this.setState({
      isShowCompanyDetail: false,
    });
  };

  showCompany = () => {
    this.setState({
      isShowCompanyDetail: true,
    });
  };

  consoleProduct = () => {
    this.setState({
      isShowProductDetail: false,
    });
  };

  openFeatures = () => {
    window.open('http://fafa.huangye88.com/tool/', '_blank');
  };

  showProduct = () => {
    const { canPost } = this.props;
    if (!canPost) {
      Modal.info({
        content: '未开通该功能，请开通后重新登陆使用',
        zIndex: 9999,
        okText: '立即开通',
        onOk: this.openFeatures,
      });
      return;
    }
    this.setState({
      isShowProductDetail: true,
    });
  };

  consoleEditProduct = () => {
    this.setState({
      isShowEditProductDetail: false,
    });
  };

  showEditProduct = () => {
    const { canPost } = this.props;
    if (!canPost) {
      Modal.info({
        content: '未开通该功能，请开通后重新登陆使用',
        zIndex: 9999,
        okText: '立即开通',
        onOk: this.openFeatures,
      });
      return;
    }
    this.setState({
      isShowEditProductDetail: true,
      onlyShow: false,
    });
  };

  onlyShowEditProduct = () => {
    this.setState({
      isShowEditProductDetail: true,
      onlyShow: true,
    });
  };

  consoleManual = () => {
    this.setState({
      isShowManual: false,
    });
  };

  showManual = () => {
    const { canPost } = this.props;
    if (!canPost) {
      Modal.info({
        content: '未开通该功能，请开通后重新登陆使用',
        zIndex: 9999,
        okText: '立即开通',
        onOk: this.openFeatures,
      });
      return;
    }
    this.props.dispatch({
      type: 'home/getUrl',
      payload: {
        url: 'http://fabuxinxi.huangye88.com',
      },
      callBack: url => {
        window.open(url, '_blank');
      },
    });
    // this.setState({
    //   isShowManual:true,
    //  })
  };

  showPhotoAlbum = () => {
    this.setState({
      isShowPhotoAlbum: true,
    });
  };

  consoleProductList = () => {
    this.setState({
      isShowProductList: false,
    });
  };

  showProductList = () => {
    const { canPost } = this.props;
    if (!canPost) {
      Modal.info({
        content: '未开通该功能，请开通后重新登陆使用',
        zIndex: 9999,
        okText: '立即开通',
        onOk: this.openFeatures,
      });
      return;
    }
    this.setState({
      isShowProductList: true,
    });
  };

  consoleHistoryList = () => {
    this.setState({
      isShowHistoryList: false,
    });
  };

  showHistoryList = () => {
    const { canPost } = this.props;
    if (!canPost) {
      Modal.info({
        content: '未开通该功能，请开通后重新登陆使用',
        zIndex: 9999,
        okText: '立即开通',
        onOk: this.openFeatures,
      });
      return;
    }
    this.setState({
      isShowHistoryList: true,
    });
  };

  consoleShopList = () => {
    this.setState({
      isShowShopList: false,
    });
  };

  showShopList = () => {
    const { canPost } = this.props;
    if (!canPost) {
      Modal.info({
        content: '未开通该功能，请开通后重新登陆使用',
        zIndex: 9999,
        okText: '立即开通',
        onOk: this.openFeatures,
      });
      return;
    }
    this.setState({
      isShowShopList: true,
    });
  };

  consoleRanking = () => {
    this.setState({
      isShowRanking: false,
    });
  };

  showRanking = () => {
    const { rankPost } = this.props;
    if (!rankPost) {
      Modal.info({
        content: '未开通该功能，请开通后重新登陆使用',
        zIndex: 9999,
        okText: '立即开通',
        onOk: this.openFeatures,
      });
      return;
    }
    this.setState({
      isShowRanking: true,
    });
  };

  consoleSeek = () => {
    this.setState({
      isShowSeek: false,
    });
  };

  showSeek = () => {
    const { seekPost } = this.props;
    if (!seekPost) {
      Modal.info({
        content: '未开通该功能，请开通后重新登陆使用',
        zIndex: 9999,
        okText: '立即开通',
        onOk: this.openFeatures,
      });
      return;
    }
    this.setState({
      isShowSeek: true,
    });
  };

  consoleFunction = () => {
    this.setState({
      isShowFunction: false,
    });
  };

  showFunction = () => {
    this.setState({
      isShowFunction: true,
    });
  };

  consoleAi = () => {
    this.setState({
      isShowAi: false,
    });
  };

  showAi = () => {
    const { isOpenAi } = this.props;
    if (!isOpenAi) {
      Modal.info({
        content: '未开通爱采购功能，请联系客服开通',
        zIndex: 9999,
      });
      return;
    }
    this.setState({
      isShowAi: true,
    });
  };

  consolePhotoAlbum = () => {
    this.setState({
      isShowPhotoAlbum: false,
    });
  };

  showPhotoAlbum = () => {
    this.setState({
      isShowPhotoAlbum: true,
    });
  };

  onMenuClick = event => {
    const { key } = event;
    if (key === 'logout') {
      const { dispatch } = this.props;
      if (dispatch) {
        this.props.dispatch({ type: 'home/loginOut' });
      }
    } else if (key === 'help') {
      this.props.dispatch({
        type: 'home/getUrl',
        payload: {
          url: 'http://www.huangye88.com/help/fafazhushou_43.html',
        },
        callBack: url => {
          window.open(url, '_blank');
        },
      });
    }
  };

  render() {
    const { onlyShow } = this.state;
    const menuHeaderDropdown = (
      <Menu className={styles.menu} selectedKeys={[]} onClick={this.onMenuClick}>
        <Menu.Item key="help">
          <QuestionOutlined />
          帮助
        </Menu.Item>
        <Menu.Item key="logout">
          <LogoutOutlined />
          注销
        </Menu.Item>
      </Menu>
    );

    const { companyData = {}, RankingProgressRate } = this.props;
    const productInfo = {};
    return (
      <Layout className="layout">
        <Header>
          <div className="logo" />
          <div className={styles.right}>
            <Dropdown overlay={menuHeaderDropdown}>
              <span className={`${styles.action} ${styles.account}`}>
                <span className={styles.name}>{companyData.username}</span>

                <BarsOutlined style={{ color: 'white' }} />
              </span>
            </Dropdown>
          </div>
          <Menu
            theme="dark"
            mode="horizontal"
            defaultSelectedKeys={this.state.selKey}
            style={{ lineHeight: '64px' }}
            onClick={this.mmenuClick}
          >
            <Menu.Item key="1">首页</Menu.Item>
            <Menu.Item key="2">信息发布</Menu.Item>
            <Menu.Item key="3">AI机器人</Menu.Item>
          </Menu>
        </Header>
        <Content className={styles.content}>
          <ConfigProvider locale={zhCN}>
            {this.state.selKey[0] == '1' && (
              <Home
                showCompany={this.showCompany}
                showHistoryList={this.showHistoryList}
                showRanking={this.showRanking}
                showSeek={this.showSeek}
                showFunction={this.showFunction}
                showAi={this.showAi}
                showShopList={this.showShopList}
              />
            )}
            {this.state.selKey[0] == '2' && (
              <Release
                showProduct={this.showProduct}
                showManual={this.showManual}
                showPhotoAlbum={this.showPhotoAlbum}
                showProductList={this.showProductList}
                showShopList={this.showShopList}
                showHistoryList={this.showHistoryList}
                showRanking={this.showRanking}
                showSeek={this.showSeek}
                showCompany={this.showCompany}
              />
            )}
            {this.state.selKey[0] == '3' && <AiRobots />}
            {this.state.isShowCompanyDetail && <CompanyDetail close={this.consoleCompany} />}

            {this.state.isShowManual && <Manual close={this.consoleManual} />}
            {this.state.isShowProductList && (
              <ProductList
                close={this.consoleProductList}
                show={this.showEditProduct}
                onlyShow={this.onlyShowEditProduct}
                addProduct={this.showProduct}
              />
            )}
            {this.state.isShowHistoryList && <HistoryList close={this.consoleHistoryList} />}
            {this.state.isShowShopList && (
              <ShopList close={this.consoleShopList} showCompany={this.showCompany} />
            )}
            {this.state.isShowRanking && <Ranking close={this.consoleRanking} />}
            {this.state.isShowSeek && <Seek close={this.consoleSeek} />}
            {this.state.isShowFunction && <FunctionList close={this.consoleFunction} />}

            {this.state.isShowAi && <CompanyDetailAi close={this.consoleAi} />}
            {this.state.isShowPhotoAlbum && <PhotoAlbum close={this.consolePhotoAlbum} />}
            {/* {this.state.isShowRanking&&<Examble close={this.consoleRanking}/>} */}
            {this.state.isShowProductDetail && (
              <EditProductDetail
                close={this.consoleProduct}
                productInfo={productInfo}
                isCreateProduct
              />
            )}
            {this.state.isShowEditProductDetail && (
              <EditProductDetail
                close={this.consoleEditProduct}
                isCreateProduct={false}
                onlyShow={onlyShow}
              />
            )}
          </ConfigProvider>
        </Content>
        <Footer style={{ backgroundColor: '#FFF', paddingRight: 10 }}>
          <div>
            <Row>
              <Col span={8}></Col>
              <Col span={8}></Col>
              <Col span={8} style={{ flexirection: 'row', justifyContent: 'flex-end' }}>
                {false && (
                  <div className={styles.processe}>
                    <div> 实时查排名:</div>
                    {RankingProgressRate == 100 ? (
                      '查询完毕'
                    ) : (
                      <Progress
                        style={{ width: 150 }}
                        percent={RankingProgressRate}
                        size="small"
                        status="active"
                      />
                    )}
                  </div>
                )}
              </Col>
            </Row>
          </div>
        </Footer>
      </Layout>
    );
  }
}

export default connect(({ home, backstage, merchant }) => ({
  ...home,
  ...backstage,
  ...merchant,
}))(Indexpage);
