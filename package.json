{"name": "ant-design-pro", "version": "1.0.0", "private": true, "description": "An out-of-box UI solution for enterprise applications", "scripts": {"analyze": "cross-env ANALYZE=1 umi build", "build": "umi build", "deploy": "cross-env ANT_DESIGN_PRO_ONLY_DO_NOT_USE_IN_YOUR_PRODUCTION=site npm run site && npm run gh-pages", "fetch:blocks": "fetch-blocks", "format-imports": "import-sort --write '**/*.{js,jsx,ts,tsx}'", "gh-pages": "cp CNAME ./dist/ && gh-pages -d dist", "lint": "npm run lint:js && npm run lint:style && npm run lint:prettier", "lint-staged": "lint-staged", "lint-staged:js": "eslint --ext .js,.jsx,.ts,.tsx ", "lint:fix": "eslint --fix --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src && npm run lint:style", "lint:js": "eslint --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src", "lint:prettier": "check-prettier lint", "lint:style": "stylelint --fix \"src/**/*.less\" --syntax less", "prettier": "prettier -c --write **/*", "start": "set PORT=9001 && set NODE_OPTIONS=--openssl-legacy-provider && umi dev", "linux-start": "PORT=9001  NODE_OPTIONS=--openssl-legacy-provider umi dev", "start:no-mock": "cross-env MOCK=none umi dev", "test": "umi test", "test:all": "node ./tests/run-tests.js", "test:component": "umi test ./src/components"}, "husky": {"hooks": {"pre-commit": "npm run lint-staged"}}, "lint-staged": {"**/*.less": "stylelint --syntax less", "**/*.{js,jsx,tsx,ts,less,md,json}": ["prettier --write", "git add"], "**/*.{js,jsx}": "npm run lint-staged:js", "**/*.{js,ts,tsx}": "npm run lint-staged:js"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 10"], "dependencies": {"@ant-design/compatible": "^1.0.8", "@ant-design/icons": "^4.6.2", "@ant-design/pro-layout": "^6.17.0", "@antv/data-set": "^0.10.2", "aes-js": "^3.1.2", "antd": "^4.7.3", "axios": "^0.19.0", "classnames": "^2.2.6", "crypto-js": "^4.2.0", "dayjs": "^1.11.7", "dva": "^2.4.1", "dva-reset-state": "^0.1.1", "jwt-decode": "3.1.2", "lodash": "^4.17.21", "lodash-decorators": "^6.0.1", "memoize-one": "^5.0.4", "moment": "^2.24.0", "omit.js": "^1.0.2", "path-to-regexp": "^3.0.0", "prop-types": "^15.7.2", "qs": "^6.7.0", "react": "^16.8.6", "react-container-query": "^0.11.0", "react-copy-to-clipboard": "^5.0.1", "react-document-title": "^2.0.3", "react-dom": "^16.8.6", "react-media": "^1.9.2", "react-media-hook2": "^1.0.5", "react-photo-view": "^0.5.2", "react-tiny-virtual-list": "^2.2.0", "react-virtualized": "^9.21.1", "redux": "^4.0.1", "rsuite": "^4.8.6", "umi": "^2.8.7", "umi-plugin-pro-block": "^1.3.2", "umi-plugin-react": "^1.9.5", "umi-request": "^1.0.8", "video.js": "^7.20.3"}, "devDependencies": {"@ant-design/colors": "^3.1.0", "@types/classnames": "^2.3.1", "@types/history": "^4.7.2", "@types/jest": "^24.0.13", "@types/lodash": "^4.14.133", "@types/qs": "^6.5.3", "@types/react": "^16.8.19", "@types/react-document-title": "^2.0.3", "@types/react-dom": "^16.8.4", "@umijs/fabric": "^1.1.0", "chalk": "^2.4.2", "check-prettier": "^1.0.3", "cross-env": "^5.2.0", "cross-port-killer": "^1.1.1", "enzyme": "^3.9.0", "eslint": "^5.16.0", "fetch-blocks": "^1.0.0", "gh-pages": "^2.0.1", "husky": "^2.3.0", "import-sort-cli": "^6.0.0", "import-sort-parser-babylon": "^6.0.0", "import-sort-parser-typescript": "^6.0.0", "import-sort-style-module": "^6.0.0", "jsdom-global": "^3.0.2", "lint-staged": "^8.1.7", "mockjs": "^1.0.1-beta3", "node-fetch": "^2.6.0", "prettier": "^1.18.2", "pro-download": "1.0.1", "slash2": "^2.0.0", "stylelint": "^10.1.0", "umi-plugin-ga": "^1.1.3", "umi-plugin-pro": "^1.0.2", "umi-types": "^0.3.8", "webpack-theme-color-replacer": "^1.2.15"}, "engines": {"node": ">=10.0.0"}, "checkFiles": ["src/**/*.js*", "src/**/*.ts*", "src/**/*.less", "config/**/*.js*", "scripts/**/*.js"]}