import { IConfig, IPlugin } from 'umi-types';
import defaultSettings from './defaultSettings'; // https://umijs.org/config/

import slash from 'slash2';
import webpackPlugin from './plugin.config';
const { pwa, primaryColor } = defaultSettings; // preview.pro.ant.design only do not use in your production ;
// preview.pro.ant.design 专用环境变量，请不要在你的项目中使用它。

const { ANT_DESIGN_PRO_ONLY_DO_NOT_USE_IN_YOUR_PRODUCTION } = process.env;
const isAntDesignProPreview = ANT_DESIGN_PRO_ONLY_DO_NOT_USE_IN_YOUR_PRODUCTION === 'site';
const plugins: IPlugin[] = [
  [
    'umi-plugin-react',
    {
      antd: true,
      dva: {
        hmr: true,
      },
      locale: {
        // default false
        enable: false,
        // default zh-CN
        default: 'zh-CN',
        // default true, when it is true, will use `navigator.language` overwrite default
        baseNavigator: false,
      },
      dynamicImport: {
        loadingComponent: './components/PageLoading/index',
        webpackChunkName: true,
        level: 3,
      },
      pwa: pwa
        ? {
            workboxPluginMode: 'InjectManifest',
            workboxOptions: {
              importWorkboxFrom: 'local',
            },
          }
        : false, // default close dll, because issue https://github.com/ant-design/ant-design-pro/issues/4665
      // dll features https://webpack.js.org/plugins/dll-plugin/
      // dll: {
      //   include: ['dva', 'dva/router', 'dva/saga', 'dva/fetch'],
      //   exclude: ['@babel/runtime', 'netlify-lambda'],
      // },
    },
  ],
  [
    'umi-plugin-pro-block',
    {
      moveMock: false,
      moveService: false,
      modifyRequest: true,
      autoAddMenu: true,
    },
  ],
]; // 针对 preview.pro.ant.design 的 GA 统计代码

if (isAntDesignProPreview) {
  plugins.push([
    'umi-plugin-ga',
    {
      code: '***********-6',
    },
  ]);
  plugins.push([
    'umi-plugin-pro',
    {
      serverUrl: 'https://ant-design-pro.netlify.com',
    },
  ]);
}

export default {
  plugins,
  block: {
    defaultGitUrl: 'https://github.com/ant-design/pro-blocks',
  },
  hash: true,
  targets: {
    ie: 11,
  },
  devtool: isAntDesignProPreview ? 'source-map' : false,
  // umi routes: https://umijs.org/zh/guide/router.html
  routes: [
    {
      path: '/user/login',
      component: '../layouts/UserLayout',
      routes: [
        {
          name: 'login',
          path: '/user/login',
          component: './user/login',
        },
      ],
    },
    {
      path: '/demo',
      component: '../layouts/BlankLayout',
      routes: [
        {
          name: 'demo',
          path: '/demo/',
          component: './demo/index',
        },
      ],
    },
    {
      path: '/',
      component: '../layouts/BasicLayout',
      routes: [
        {
          name: '首页',
          // hideInMenu: true,
          path: '/',
          // authority: ['system_user'],
          component: './v2/home/<USER>',
        },
        {
          path: '/company',
          name: '公司认证',
          routes: [
            {
              path: '/company/detail',
              name: '公司资料',
              component: './v2/company/detail/CompanyDate',
            },
            {
              path: '/company/aicaigou',
              name: '爱采购认证',
              component: './v2/company/aicaigou/AicaigouDate',
            },
            {
              path: '/company/process',
              name: '平台开通进度',
              component: './v2/company/process/PlatformProcess',
            },
          ],
        },
        {
          path: '/publish',
          name: '信息发布',
          routes: [
            {
              path: '/publish/products',
              name: '自动发布',
              component: './v2/publish/products/ProductList',
            },
            {
              path: '/publish/products/customstep1/:id(\\d+)?',
              name: '自动发布',
              hideInMenu: true,
              component: './v2/publish/producttemplate/CustomStep1',
            },
            {
              path: '/publish/products/customstep2/:id(\\d+)',
              name: '自动发布',
              hideInMenu: true,
              component: './v2/publish/producttemplate/CustomStep2',
            },
            {
              path: '/publish/products/customstep3/:id(\\d+)',
              name: '自动发布',
              hideInMenu: true,
              component: './v2/publish/producttemplate/CustomStep3',
            },
            {
              path: '/publish/products/customstep4/:id(\\d+)',
              name: '自动发布',
              hideInMenu: true,
              component: './v2/publish/producttemplate/CustomStep4',
            },
            {
              path: '/publish/products/add1/:id(\\d+)?',
              name: '自动发布',
              hideInMenu: true,
              component: './v2/publish/producttemplate/NewlyaddStep1',
            },
            {
              path: '/publish/products/add2/:id(\\d+)',
              name: '自动发布',
              hideInMenu: true,
              component: './v2/publish/producttemplate/NewlyaddStep2',
            },
            {
              path: '/publish/products/add3/:id(\\d+)',
              name: '自动发布',
              hideInMenu: true,
              component: './v2/publish/producttemplate/NewlyaddStep3',
            },
            {
              path: '/publish/products/add4/:id(\\d+)',
              name: '自动发布',
              hideInMenu: true,
              component: './v2/publish/producttemplate/NewlyaddStep4',
            },
            {
              path: '/publish/album',
              name: '相册管理',
              component: './v2/publish/album/AlbumManage',
            },
            {
              path: '/publish/album/:albumId',
              name: '相册管理',
              hideInMenu: true,
              component: './v2/publish/album/PhotoManage',
            },
            {
              path: '/publish/history',
              name: '发布历史',
              component: './v2/publish/history/ReleaseHistory',
            },
            {
              path: '/publish/ranking',
              name: '排名统计',
              component: './v2/publish/ranking/RankCount',
            },
            {
              path: '/publish/b2b',
              name: 'B2B商铺',
              component: './v2/publish/b2b/B2BPlatformProcess',
            },
          ],
        },
        {
          path: '/ai',
          name: 'AI机器人',
          routes: [
            // {
            //   path: '/ai/chatgpt',
            //   name: 'ChatGPT',
            //   component: './v2/ai/chatgpt/ChatGPT',
            // },
            {
              path: '/ai/wenxin',
              name: '文心一言',
              component: './v2/ai/wenxin/ErnieBot',
            },
            {
              path: '/ai/doubao',
              name: '豆包',
              component: './v2/ai/doubao/index',
            },
            {
              path: '/ai/deepseek',
              name: 'deepseek',
              component: './v2/ai/deepseek/index',
            },
          ],
        },
        {
          path: '/douyin',
          name: '抖音获客',
          routes: [
            {
              path: '/douyin/product',
              name: '产品介绍',
              component: './v2/douyin/ProductLink',
            },
            {
              path: '/douyin/login',
              name: '登录入口',
              component: './v2/douyin/LoginLink',
            },
          ],
        },
        // {
        //   path: '/MyTask',
        //   name: 'MyTask',
        //   authority: ['system_user'],
        //
        //   component: './MyTask/table-list',
        //   hideInMenu: true,
        // },
        {
          component: './404',
        },
      ],
    },

    {
      component: './404',
    },
  ],
  // Theme for antd: https://ant.design/docs/react/customize-theme-cn
  theme: {
    'primary-color': primaryColor,
  },
  define: {
    ANT_DESIGN_PRO_ONLY_DO_NOT_USE_IN_YOUR_PRODUCTION:
      ANT_DESIGN_PRO_ONLY_DO_NOT_USE_IN_YOUR_PRODUCTION || '', // preview.pro.ant.design only do not use in your production ; preview.pro.ant.design 专用环境变量，请不要在你的项目中使用它。
  },
  ignoreMomentLocale: true,
  lessLoaderOptions: {
    javascriptEnabled: true,
  },
  disableRedirectHoist: true,
  cssLoaderOptions: {
    modules: true,
    getLocalIdent: (
      context: {
        resourcePath: string;
      },
      _: string,
      localName: string,
    ) => {
      if (
        context.resourcePath.includes('node_modules') ||
        context.resourcePath.includes('ant.design.pro.less') ||
        context.resourcePath.includes('global.less')
      ) {
        return localName;
      }

      const match = context.resourcePath.match(/src(.*)/);

      if (match && match[1]) {
        const antdProPath = match[1].replace('.less', '');
        const arr = slash(antdProPath)
          .split('/')
          .map((a: string) => a.replace(/([A-Z])/g, '-$1'))
          .map((a: string) => a.toLowerCase());
        return `antd-pro${arr.join('-')}-${localName}`.replace(/--/g, '-');
      }

      return localName;
    },
  },
  manifest: {
    basePath: '/',
  },
  chainWebpack: webpackPlugin,
  /*
  proxy: {
    '/server/api/': {
      target: 'https://preview.pro.ant.design/',
      changeOrigin: true,
      pathRewrite: { '^/server': '' },
    },
  },
  */
} as IConfig;
