{"compilerOptions": {"outDir": "build/dist", "module": "esnext", "target": "esnext", "lib": ["esnext", "dom"], "sourceMap": true, "baseUrl": ".", "jsx": "react", "allowSyntheticDefaultImports": true, "moduleResolution": "node", "forceConsistentCasingInFileNames": true, "noImplicitReturns": true, "suppressImplicitAnyIndexErrors": true, "noUnusedLocals": true, "allowJs": true, "experimentalDecorators": true, "strict": true, "paths": {"@/*": ["./src/*"]}}, "exclude": ["node_modules", "build", "scripts", "acceptance-tests", "webpack", "jest", "src/setupTests.ts", "tslint:latest", "tslint-config-prettier"]}