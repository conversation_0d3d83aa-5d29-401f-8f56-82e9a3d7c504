image: node:22.12.0-alpine

cache:
  paths:
    - node_modules/
    - npm-packages-offline-cache/

before_script:
  - if [ "$RUNNER_OS" != "Darwin" ]; then   sed -i 's/dl-cdn.alpinelinux.org/mirrors.ustc.edu.cn/g' /etc/apk/repositories;apk --quiet update; apk add sshpass; apk add openssh-client; fi

stages:
  - build
  - deploy

build:
  stage: build
  script:
    #    - mv src/config.json.develop src/config.json
    - yarn install --registry https://registry.npmmirror.com
    - export NODE_OPTIONS=--openssl-legacy-provider && yarn build
  cache:
    paths: []
  artifacts:
    paths:
      - dist
  only:
    - master
  tags:
    - react

deploy:
  stage: deploy
  script:
    - export SSHPASS=$AIXUNPAN_USER_PASS
    - sshpass -e scp -o stricthostkeychecking=no -r ./dist <EMAIL>:/data0/htdocs/ffzs/prd
  only:
    - master
  tags:
    - react

build-dev:
  stage: build
  script:
    #    - yarn config set registry https://registry.npm.taobao.org -g
    - cp -rf src/configs/config.dev.js src/configs/config.js
    - yarn install --registry https://registry.npmmirror.com
    - export NODE_OPTIONS=--openssl-legacy-provider && yarn build
  cache:
    paths: []
  artifacts:
    paths:
      - dist
  only:
    - develop
  tags:
    - react

deploy-dev:
  stage: deploy
  script:
    - export SSHPASS=$AIXUNPAN_USER_PASS
    - sshpass -e scp -o stricthostkeychecking=no -r ./dist <EMAIL>:/data0/htdocs/ffzs/dev
  only:
    - develop
  tags:
    - react
